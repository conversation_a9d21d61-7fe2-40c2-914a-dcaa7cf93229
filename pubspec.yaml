name: fila_app
description: Aplicativo de gerenciamento de filas para hospitais e médicos.
publish_to: none # Remove this line if you wish to publish to pub.dev

environment:
  sdk: ^3.5.4

dependencies:
  flutter:
    sdk: flutter  
  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.8
  flutter_polyline_points: ^2.1.0
  parse_server_sdk_flutter: ^9.0.0
  provider: ^6.1.2
  mobile_scanner: ^6.0.7
  uuid: ^4.5.1
  qr_flutter: ^4.1.0
  google_maps_flutter: ^2.10.1
  url_launcher: ^6.3.1
  font_awesome_flutter: ^10.8.0
  timezone: ^0.10.0
  badges: ^3.1.2
  http: ^1.3.0
  mask_text_input_formatter: ^2.9.0
  flutter_dotenv: ^5.0.2
  flutter_native_splash: ^2.4.5
  device_info_plus: ^11.3.3
  flutter_secure_storage: ^9.2.4
  intl: ^0.19.0
  printing: ^5.14.2
  pdf: ^3.11.3
  get: ^4.7.2
  shared_preferences: ^2.5.2
  geolocator: ^13.0.2
  geocoding: ^3.0.0
  map_launcher: ^3.5.0
  package_info_plus: ^8.3.0
  hive_flutter: ^1.1.0
  cached_network_image: ^3.4.1
  connectivity_plus: ^6.1.3  # Esta é uma versão recente que usa List<ConnectivityResult>
  path_provider: ^2.1.5
  cloud_firestore: ^4.17.5
  firebase_auth: ^4.16.0
  intl_phone_number_input: ^0.7.3
  image_picker: ^1.1.2
  firebase_core: ^2.16.0
  firebase_messaging: ^14.7.0
  flutter_local_notifications: ^19.0.0
  firebase_analytics: ^10.7.4
  web_socket_channel: ^2.4.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.3

# Override para resolver o conflito de dependências
dependency_overrides:
  package_info_plus: ^4.0.1
  geolocator_android: ^4.6.0
  # Removi o override do parse_server_sdk para usar apenas o parse_server_sdk_flutter
  uuid: ^3.0.7
  http: ^0.13.0
  timezone: ^0.9.4
  printing: ^5.13.3
  flutter_local_notifications: ^19.0.0  # Forçar esta versão

flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/logo-removebg-preview-1-28.png"
  adaptive_icon_background: "#75CBBB" # Cor principal do app
  adaptive_icon_foreground: "assets/logo-removebg-preview-1-28.png"
  min_sdk_android: 21
  remove_alpha_ios: true

flutter_native_splash:
  # Usando a primeira cor do gradiente (mais clara)
  color: "0xFF75CBBB"
  image: "assets/logo-removebg-preview-1-28.png"
  android: true
  ios: true

flutter:
  fonts:
    - family: Georgia
      fonts:
        - asset: assets/fonts/Georgia.ttf
  uses-material-design: true

  assets:
    - .env
    - assets/
    - assets/fonts/

CachedNetworkImage:
  imageUrl: imageUrl
  placeholder: (context, url) => const CircularProgressIndicator()
  errorWidget: (context, url, error) => const Icon(Icons.error)
