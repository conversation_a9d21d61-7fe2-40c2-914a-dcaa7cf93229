# Gradle properties for Flutter with Java support
org.gradle.jvmargs=-Xmx1536M -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8 -XX:+UseG1GC -XX:+ExplicitGCInvokesConcurrent --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED

# Java home path - corrigido para JDK 17
org.gradle.java.home=C:\\Program Files\\Java\\jdk-17

# Desativar validação estrita do JVM target
kotlin.jvm.target.validation.mode=warning

# Performance optimization - desativar paralelismo e daemon para evitar erros
org.gradle.parallel=false
org.gradle.caching=true
org.gradle.configureondemand=false
org.gradle.daemon=false

# Android configuration
android.useAndroidX=true
android.enableJetifier=true
android.nonFinalResIds=false
android.defaults.buildfeatures.buildconfig=true

# New recommended option (replacing deprecated enableDexingArtifactTransform)
android.useFullClasspathForDexingTransform=true

# Configurações adicionais para melhorar a estabilidade
org.gradle.workers.max=1
android.enableR8.fullMode=false

# Kotlin
kotlin.code.style=official