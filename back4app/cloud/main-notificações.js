// =============================================================================
// FUNÇÕES DE NOTIFICAÇÕES PUSH
// =============================================================================

// Utilitários para processamento de IDs e validação de parâmetros
const UtilsNotificacao = {
  // Normaliza IDs para garantir formato consistente
  normalizarId: (id) => {
    if (!id) return null;
    if (typeof id === 'object') {
      return id.id || id.objectId || null;
    }
    return String(id);
  },
  
  // Valida e normaliza tipo de dispositivo
  normalizarDeviceType: (deviceType) => {
    if (!deviceType) return 'android';
    const tipo = String(deviceType).toLowerCase();
    return ['ios', 'android'].includes(tipo) ? tipo : 'android';
  },
  
  // Verifica o ambiente atual (produção ou desenvolvimento)
  isProducao: () => {
    return process.env.NODE_ENV === 'production';
  },
  
  // Logger condicional baseado no ambiente
  log: (mensagem, nivel = 'info') => {
    const prefixo = `[PUSH][${nivel.toUpperCase()}]`;
    const isVerboso = !UtilsNotificacao.isProducao();
    
    if (nivel === 'error' || nivel === 'warn') {
      console[nivel](`${prefixo} ${mensagem}`);
    } else if (isVerboso || nivel === 'info') {
      console.log(`${prefixo} ${mensagem}`);
    }
  }
};

// Função centralizada para envio de notificações push
Parse.Cloud.define("enviarNotificacaoPush", async (request) => {
  const inicioExecucao = Date.now();
  const ambiente = UtilsNotificacao.isProducao() ? 'producao' : 'desenvolvimento';
  
  try {
    UtilsNotificacao.log(`Iniciando envio de notificação push (ambiente: ${ambiente})...`);
    
    let {
      destinatarios,     // Array com IDs dos usuários destinatários
      canais,            // Array com nomes dos canais
      titulo,            // Título da notificação
      mensagem,          // Texto da notificação
      tipo,              // Tipo para processamento no cliente
      dadosAdicionais,   // Dados extras para a notificação
      tipoDestinatario,  // Tipo dos usuários destinatários (patient, doctor, etc.)
      evitarDuplicidade = true // Evitar envio para canal e destinatário ao mesmo tempo
    } = request.params;
    
    // Validação mais robusta dos parâmetros com normalização
    if (destinatarios) {
      if (!Array.isArray(destinatarios)) {
        destinatarios = [destinatarios];
      }
      // Normalizar todos os IDs
      destinatarios = destinatarios.map(id => UtilsNotificacao.normalizarId(id)).filter(Boolean);
    }
    
    if (canais) {
      if (!Array.isArray(canais)) {
        canais = [canais];
      }
      // Filtrar canais vazios e normalizar
      canais = canais.filter(canal => canal && typeof canal === 'string' && canal.trim().length > 0);
    }

    // Validar parâmetros obrigatórios
    if ((!destinatarios || destinatarios.length === 0) && 
        (!canais || canais.length === 0)) {
      throw new Error("Necessário fornecer destinatários ou canais válidos para enviar notificações");
    }

    if (!titulo || typeof titulo !== 'string') {
      throw new Error("Título é obrigatório e deve ser uma string");
    }

    if (!mensagem || typeof mensagem !== 'string') {
      throw new Error("Mensagem é obrigatória e deve ser uma string");
    }
    
    // Evitar duplicidade se solicitado
    if (evitarDuplicidade && destinatarios?.length > 0 && canais?.length > 0) {
      UtilsNotificacao.log("Parâmetros contêm tanto destinatários quanto canais. Priorizando canais para evitar duplicidade.");
      destinatarios = []; // Priorizamos os canais
    }

    UtilsNotificacao.log(`Enviando notificação: "${titulo}" para ${destinatarios?.length || 0} destinatários / ${canais?.length || 0} canais`);

    // Estrutura de dados melhorada para compatibilidade com iOS e Android
    // Formato otimizado para garantir entrega em ambas plataformas
    const pushData = {
      // Campos padrão para Parse Server
      title: titulo,
      alert: mensagem,
      badge: "Increment",
      sound: "default",
      
      // Campos específicos para Android
      android: {
        notification: {
          title: titulo,
          body: mensagem,
          click_action: "FLUTTER_NOTIFICATION_CLICK",
          channel_id: "saude_sem_espera_channel",
          priority: "high"
        }
      },
      
      // Campos específicos para iOS
      ios: {
        alert: {
          title: titulo,
          body: mensagem
        },
        badge: 1,
        sound: "default",
        category: tipo || "GENERAL_CATEGORY"
      },
      
      // Adicionar tipo para processamento no cliente
      tipo: tipo || "geral",
      
      // Garantir que "click_action" esteja no nível principal para compatibilidade
      click_action: "FLUTTER_NOTIFICATION_CLICK",
      
      // Adicionar ambiente para rastreamento
      ambiente: ambiente
    };

    // Adicionar dados extras se fornecidos
    if (dadosAdicionais && typeof dadosAdicionais === 'object') {
      // Adicionar dados apenas no nível principal para compatibilidade com Parse Server
      Object.assign(pushData, dadosAdicionais);
      
      // Verificar se há dados específicos para Android e mesclá-los
      if (dadosAdicionais.android) {
        pushData.android = {...pushData.android, ...dadosAdicionais.android};
      }
      
      // Verificar se há dados específicos para iOS e mesclá-los
      if (dadosAdicionais.ios) {
        pushData.ios = {...pushData.ios, ...dadosAdicionais.ios};
      }
      
      UtilsNotificacao.log(`Dados adicionais mesclados ao payload: ${JSON.stringify(Object.keys(dadosAdicionais))}`);
    }

    // Variável para rastrear o status do envio
    let notificacaoEnviada = false;
    let detalhesEnvio = {
      ambiente: ambiente,
      timestamp: new Date().toISOString()
    };

    // Implementar um mecanismo de limitação de taxa (rate limiting) básico
    // Apenas para demonstração - em produção, usar uma solução como Redis para gerenciar taxas
    const MAX_DESTINATARIOS_POR_LOTE = 1000; // Máximo de destinatários por requisição
    
    // Estratégia de envio - primeiro tenta canais, depois destinatários
    if (canais && canais.length > 0) {
      UtilsNotificacao.log(`Tentando enviar para canais: ${canais.join(', ')}`);
      
      // Enviar para canais específicos
      const pushResponse = await Parse.Push.send({
        channels: canais,
        data: pushData
      }, { useMasterKey: true });
      
      notificacaoEnviada = true;
      detalhesEnvio.canais = canais;
      UtilsNotificacao.log(`Notificação enviada com sucesso para ${canais.length} canais: ${canais.join(', ')}`);
    } else if (destinatarios && destinatarios.length > 0) {
      UtilsNotificacao.log(`Tentando enviar para ${destinatarios.length} destinatários do tipo ${tipoDestinatario || "qualquer"}`);
      
      // Para grandes quantidades, dividir em lotes
      if (destinatarios.length > MAX_DESTINATARIOS_POR_LOTE) {
        UtilsNotificacao.log(`Dividindo ${destinatarios.length} destinatários em lotes de ${MAX_DESTINATARIOS_POR_LOTE}`);
        const lotes = [];
        for (let i = 0; i < destinatarios.length; i += MAX_DESTINATARIOS_POR_LOTE) {
          lotes.push(destinatarios.slice(i, i + MAX_DESTINATARIOS_POR_LOTE));
        }
        
        detalhesEnvio.lotes = lotes.length;
        let instalacoes = 0;
        
        // Processar cada lote separadamente
        for (let i = 0; i < lotes.length; i++) {
          const lote = lotes[i];
          UtilsNotificacao.log(`Processando lote ${i+1}/${lotes.length} com ${lote.length} destinatários`);
          
          // Criar query de instalações para este lote
          const pushQuery = new Parse.Query(Parse.Installation);
          pushQuery.containedIn("userId", lote);
          
          if (tipoDestinatario && typeof tipoDestinatario === 'string') {
            pushQuery.equalTo("userType", tipoDestinatario);
          }
          
          // Verificar instalações apenas para estatísticas
          const lotInstalacoes = await pushQuery.count({ useMasterKey: true });
          instalacoes += lotInstalacoes;
          
          // Enviar apenas se houver instalações
          if (lotInstalacoes > 0) {
            await Parse.Push.send({
              where: pushQuery,
              data: pushData
            }, { useMasterKey: true });
            
            UtilsNotificacao.log(`Lote ${i+1}: Notificação enviada para ${lotInstalacoes} dispositivos`);
          } else {
            UtilsNotificacao.log(`Lote ${i+1}: Nenhuma instalação encontrada, pulando`);
          }
        }
        
        notificacaoEnviada = true;
        detalhesEnvio.destinatarios = destinatarios.length;
        detalhesEnvio.tipoDestinatario = tipoDestinatario;
        detalhesEnvio.instalacoes = instalacoes;
        UtilsNotificacao.log(`Notificação enviada com sucesso para ${destinatarios.length} usuários (${instalacoes} dispositivos) em ${lotes.length} lotes`);
      } else {
        // Processar normalmente para quantidades pequenas
        // Criar query de instalações para estes destinatários
        const pushQuery = new Parse.Query(Parse.Installation);
        pushQuery.containedIn("userId", destinatarios);
  
        // Se foi especificado um tipo de destinatário, adicionar à query
        if (tipoDestinatario && typeof tipoDestinatario === 'string') {
          pushQuery.equalTo("userType", tipoDestinatario);
          UtilsNotificacao.log(`Filtrado por tipo de destinatário: ${tipoDestinatario}`);
        }
  
        // Verificar quantas instalações correspondem à consulta
        const instalacoes = await pushQuery.count({ useMasterKey: true });
        UtilsNotificacao.log(`Encontradas ${instalacoes} instalações para notificação`);
  
        if (instalacoes === 0) {
          UtilsNotificacao.log(`Atenção: Nenhuma instalação encontrada para os destinatários especificados.`, 'warn');
        } else {
          // Enviar a notificação
          const pushResponse = await Parse.Push.send({
            where: pushQuery,
            data: pushData
          }, { useMasterKey: true });
          
          notificacaoEnviada = true;
          detalhesEnvio.destinatarios = destinatarios;
          detalhesEnvio.tipoDestinatario = tipoDestinatario;
          detalhesEnvio.instalacoes = instalacoes;
          UtilsNotificacao.log(`Notificação enviada com sucesso para ${destinatarios.length} usuários (${instalacoes} dispositivos) do tipo ${tipoDestinatario || "qualquer"}`);
        }
      }
    }

    // Se não conseguiu enviar por nenhum método
    if (!notificacaoEnviada) {
      throw new Error("Não foi possível enviar a notificação: destinatários ou canais inválidos");
    }

    // Calcular tempo de execução
    const tempoExecucao = Date.now() - inicioExecucao;
    detalhesEnvio.tempo_execucao_ms = tempoExecucao;
    
    // Registrar a notificação para fins de auditoria
    try {
      const logNotificacao = new Parse.Object("NotificacaoLog");
      logNotificacao.set("titulo", titulo);
      logNotificacao.set("mensagem", mensagem);
      logNotificacao.set("tipo", tipo || "geral");
      logNotificacao.set("destinatarios_count", destinatarios?.length || 0);
      logNotificacao.set("canais_count", canais?.length || 0);
      logNotificacao.set("data_envio", new Date());
      logNotificacao.set("sucesso", true);
      logNotificacao.set("ambiente", ambiente);
      logNotificacao.set("tempo_execucao_ms", tempoExecucao);
      logNotificacao.set("detalhes", JSON.stringify(detalhesEnvio));
      
      await logNotificacao.save(null, { useMasterKey: true });
      UtilsNotificacao.log(`Log de notificação registrado com sucesso (${tempoExecucao}ms)`);
    } catch (logError) {
      // Apenas registrar o erro, não impedir o fluxo principal
      UtilsNotificacao.log(`Erro ao registrar log de notificação: ${logError.message}`, 'warn');
    }

    return {
      success: true,
      message: "Notificação enviada com sucesso",
      details: detalhesEnvio
    };
  } catch (error) {
    const tempoExecucao = Date.now() - inicioExecucao;
    UtilsNotificacao.log(`Erro ao enviar notificação push: ${error.message}`, 'error');
    UtilsNotificacao.log(error.stack, 'error');

    // Registrar falha para análise
    try {
      const logFalha = new Parse.Object("NotificacaoLog");
      logFalha.set("titulo", request.params.titulo || "Desconhecido");
      logFalha.set("mensagem", request.params.mensagem || "Desconhecido");
      logFalha.set("erro", error.message);
      logFalha.set("erro_detalhes", error.stack);
      logFalha.set("parametros_entrada", JSON.stringify(request.params));
      logFalha.set("data_erro", new Date());
      logFalha.set("sucesso", false);
      logFalha.set("ambiente", ambiente);
      logFalha.set("tempo_execucao_ms", tempoExecucao);
      await logFalha.save(null, { useMasterKey: true });
    } catch (logError) {
      UtilsNotificacao.log(`Erro ao registrar falha de notificação: ${logError.message}`, 'error');
    }

    return {
      success: false,
      error: error.message,
      errorType: error.name
    };
  }
});

// Função para registrar dispositivos para notificações push
Parse.Cloud.define('registerDeviceForPush', async (request) => {
  const inicioExecucao = Date.now();
  const ambiente = UtilsNotificacao.isProducao() ? 'producao' : 'desenvolvimento';
  
  try {
    let { deviceId, userId, userType, token, deviceType } = request.params;

    // Validações e normalizações
    if (!deviceId || !token) {
      throw new Error("Parâmetros incompletos: deviceId e token são obrigatórios");
    }

    // Normalizar deviceType
    deviceType = UtilsNotificacao.normalizarDeviceType(deviceType);
    
    // Normalizar userId
    userId = UtilsNotificacao.normalizarId(userId);

    UtilsNotificacao.log(`Registrando dispositivo ${deviceId} tipo ${deviceType} para notificações push (${userType} ${userId})`);

    // Buscar instalação existente por deviceId ou token
    // Esta abordagem é mais robusta, pois verifica tanto pelo ID do dispositivo quanto pelo token
    const installationQuery = Parse.Query.or(
      new Parse.Query(Parse.Installation).equalTo('installationId', deviceId),
      new Parse.Query(Parse.Installation).equalTo('deviceToken', token)
    );
    
    let installation = await installationQuery.first({ useMasterKey: true });

    if (!installation) {
      // Criar nova instalação se não existir
      installation = new Parse.Installation();
      installation.set('installationId', deviceId);
      UtilsNotificacao.log("Criando nova instalação");
    } else {
      UtilsNotificacao.log(`Atualizando instalação existente: ${installation.id}`);
    }

    // Normalizar e validar o tipo de usuário
    const validUserTypes = ['patient', 'doctor', 'medico', 'secretary', 'secretaria', 'hospital', 'consultorio', 'admin'];
    const normalizedUserType = userType?.toLowerCase() || 'patient';
    
    let effectiveUserType = normalizedUserType;
    
    // Mapeamento para tipos padrão se necessário
    if (!validUserTypes.includes(normalizedUserType)) {
      if (['paciente'].includes(normalizedUserType)) {
        effectiveUserType = 'patient';
      } else if (['médico', 'doctor'].includes(normalizedUserType)) {
        effectiveUserType = 'doctor';
      } else {
        UtilsNotificacao.log(`Tipo de usuário não reconhecido: ${userType}, usando default`, 'warn');
        effectiveUserType = 'patient'; // Tipo padrão
      }
    }

    // Atualizar dados da instalação
    installation.set('deviceToken', token);
    installation.set('deviceType', deviceType);
    installation.set('userId', userId);
    installation.set('userType', effectiveUserType);
    installation.set('lastUpdated', new Date());
    installation.set('ambiente', ambiente);

    // Configurar canais de notificação padronizados
    const channels = [];
    
    // Canal global por tipo de usuário
    const globalChannel = `${effectiveUserType}s`; // patients, doctors, secretaries, hospitals
    channels.push(globalChannel);
    
    // Canal individual para o usuário
    if (userId) {
      const userChannel = `${effectiveUserType}_${userId}`;
      channels.push(userChannel);
    } else {
      UtilsNotificacao.log("Aviso: userId não fornecido, canal individual não será criado", 'warn');
    }

    // Adicionar canais específicos por função
    const userRoles = request.user?.get('roles') || [];
    if (Array.isArray(userRoles) && userRoles.length > 0) {
      userRoles.forEach(role => {
        if (role && typeof role === 'string' && role.startsWith('role:')) {
          channels.push(`${role.substring(5)}s`); // Converter role:admin para admins, etc.
        }
      });
    }

    // Adicionar canal de ambiente (produção/desenvolvimento)
    channels.push(`ambiente_${ambiente}`);
    
    installation.set('channels', channels);

    // Salvar a instalação com masterKey
    await installation.save(null, { useMasterKey: true });
    UtilsNotificacao.log(`Dispositivo registrado com sucesso para canais: ${channels.join(', ')}`);

    // Calcular tempo de execução
    const tempoExecucao = Date.now() - inicioExecucao;
    
    // Registrar log de instalação
    try {
      const logInstalacao = new Parse.Object("InstallationLog");
      logInstalacao.set("deviceId", deviceId);
      logInstalacao.set("userId", userId);
      logInstalacao.set("userType", effectiveUserType);
      logInstalacao.set("deviceType", deviceType);
      logInstalacao.set("channels", channels);
      logInstalacao.set("data_registro", new Date());
      logInstalacao.set("ambiente", ambiente);
      logInstalacao.set("tempo_execucao_ms", tempoExecucao);
      await logInstalacao.save(null, { useMasterKey: true });
    } catch (logError) {
      UtilsNotificacao.log("Erro ao salvar log de instalação: " + logError.message, 'warn');
      // Não interrompe o fluxo principal
    }

    return { 
      success: true, 
      message: 'Dispositivo registrado com sucesso', 
      channels: channels,
      deviceType: deviceType,
      ambiente: ambiente
    };
  } catch (error) {
    UtilsNotificacao.log('Erro ao registrar dispositivo para notificações: ' + error.message, 'error');
    return { 
      success: false, 
      error: error.message,
      parametros: JSON.stringify(request.params)
    };
  }
});

// =============================================================================
// TRIGGERS DE NOTIFICAÇÕES (AFTERAVE, AFTERDELETE)
// =============================================================================

// Send notification when position in queue changes
Parse.Cloud.afterSave('Fila', async (request) => {
  const filaObject = request.object;
  const ambiente = UtilsNotificacao.isProducao() ? 'producao' : 'desenvolvimento';

  // Log no início do trigger com nível de detalhe controlado
  if (!UtilsNotificacao.isProducao()) {
    UtilsNotificacao.log(`Fila afterSave - ObjectId: ${filaObject.id}, É novo objeto: ${!request.original}`);
  }

  // Skip if this isn't an update or if it's a new object
  if (!request.original) {
    return;
  }

  // Extrair dados com normalizações
  const posicaoAtual = filaObject.get('posicao');
  const posicaoAnterior = request.original.get('posicao');
  const idPaciente = UtilsNotificacao.normalizarId(filaObject.get('idPaciente'));
  const status = filaObject.get('status');
  const statusAnterior = request.original.get('status');

  // Verificar se temos ID de paciente válido
  if (!idPaciente) {
    UtilsNotificacao.log(`Fila afterSave - ID de paciente inválido: ${filaObject.id}`, 'warn');
    return;
  }

  // Log detalhado apenas em ambiente não-produção
  if (!UtilsNotificacao.isProducao()) {
    UtilsNotificacao.log(`Fila afterSave - Dados: ID Paciente: ${idPaciente}, Posição: ${posicaoAnterior}->${posicaoAtual}, Status: ${statusAnterior}->${status}`);
  }

  try {
    // Case 1: Position changed in queue - apenas notificar mudanças significativas (mais de 1 posição)
    if (posicaoAnterior && posicaoAtual !== posicaoAnterior && Math.abs(posicaoAtual - posicaoAnterior) > 1) {
      await Parse.Cloud.run("enviarNotificacaoPush", {
        canais: [`patient_${idPaciente}`],
        titulo: 'Atualização na fila',
        mensagem: `Sua posição na fila mudou de ${posicaoAnterior} para ${posicaoAtual}.`,
        tipo: 'posicao_alterada',
        dadosAdicionais: {
          badge: 1,
          sound: 'default',
          category: 'POSITION_CHANGE',
          fila_id: filaObject.id,
          ambiente: ambiente
        }
      }, { useMasterKey: true });
    }

    // Case 2: About to be called (position <= 3)
    if (posicaoAtual <= 3 && posicaoAnterior > 3) {
      await Parse.Cloud.run("enviarNotificacaoPush", {
        canais: [`patient_${idPaciente}`],
        titulo: 'Quase sua vez!',
        mensagem: `Sua posição na fila agora é ${posicaoAtual}. Prepare-se para ser atendido!`,
        tipo: 'quase_sua_vez',
        dadosAdicionais: {
          badge: 1,
          sound: 'default',
          category: 'ALMOST_TURN',
          fila_id: filaObject.id,
          ambiente: ambiente
        }
      }, { useMasterKey: true });
    }

    // Case 3: Status changed to 'em_atendimento'
    if (status === 'em_atendimento' && statusAnterior !== 'em_atendimento') {
      await Parse.Cloud.run("enviarNotificacaoPush", {
        canais: [`patient_${idPaciente}`],
        titulo: 'Sua vez chegou!',
        mensagem: 'Você está sendo chamado para atendimento agora.',
        tipo: 'chamado_atendimento',
        dadosAdicionais: {
          badge: 1,
          sound: 'default',
          category: 'BEING_CALLED',
          fila_id: filaObject.id,
          ambiente: ambiente
        }
      }, { useMasterKey: true });
    }

    // Case 4: Status changed to 'atendido'
    if (status === 'atendido' && statusAnterior !== 'atendido') {
      await Parse.Cloud.run("enviarNotificacaoPush", {
        canais: [`patient_${idPaciente}`],
        titulo: 'Atendimento finalizado',
        mensagem: 'Seu atendimento foi finalizado. Obrigado por sua visita!',
        tipo: 'atendimento_finalizado',
        dadosAdicionais: {
          badge: 1,
          sound: 'default',
          category: 'COMPLETED',
          fila_id: filaObject.id,
          ambiente: ambiente
        }
      }, { useMasterKey: true });
    }
  } catch (error) {
    UtilsNotificacao.log(`Error sending push notification in Fila afterSave: ${error.message}`, 'error');
    // Registrar erro em sistema de logs
    try {
      const errorLog = new Parse.Object("NotificacaoErroLog");
      errorLog.set("tipo", "trigger_fila");
      errorLog.set("objeto_id", filaObject.id);
      errorLog.set("mensagem_erro", error.message);
      errorLog.set("stack", error.stack);
      errorLog.set("dados", {
        idPaciente,
        posicaoAtual,
        posicaoAnterior,
        status,
        statusAnterior
      });
      errorLog.set("ambiente", ambiente);
      await errorLog.save(null, { useMasterKey: true });
    } catch (logError) {
      // Apenas log no console se não conseguir salvar
      UtilsNotificacao.log(`Erro ao salvar log de erro: ${logError.message}`, 'error');
    }
  }
});

// Send notification when a patient enters the queue
Parse.Cloud.afterSave('FilaSolicitacao', async (request) => {
  const solicitacao = request.object;
  const ambiente = UtilsNotificacao.isProducao() ? 'producao' : 'desenvolvimento';

  // Log no início do trigger apenas em ambiente não-produção
  if (!UtilsNotificacao.isProducao()) {
    UtilsNotificacao.log(`FilaSolicitacao afterSave - ObjectId: ${solicitacao.id}, É objeto existente: ${request.object.existed()}`);
  }

  // Only run for new objects with status 'pendente'
  if (request.object.existed() || solicitacao.get('status') !== 'pendente') {
    return;
  }

  try {
    // Extrair e normalizar IDs
    const medicoId = UtilsNotificacao.normalizarId(solicitacao.get('medicoId'));
    const hospitalId = UtilsNotificacao.normalizarId(solicitacao.get('hospitalId'));

    // Validar IDs necessários
    if (!medicoId || !hospitalId) {
      UtilsNotificacao.log(`FilaSolicitacao afterSave - IDs inválidos: medicoId=${medicoId}, hospitalId=${hospitalId}`, 'warn');
      return;
    }

    // Notify doctors and secretaries of the hospital using the centralized function
    await Parse.Cloud.run("enviarNotificacaoPush", {
      canais: [`doctor_${medicoId}`, `hospital_${hospitalId}`],
      titulo: 'Nova solicitação',
      mensagem: 'Um paciente solicitou entrada na fila de atendimento.',
      tipo: 'nova_solicitacao',
      dadosAdicionais: {
        badge: 1,
        sound: 'default',
        category: 'NEW_REQUEST',
        solicitacao_id: solicitacao.id,
        ambiente: ambiente
      }
    }, { useMasterKey: true });
  } catch (error) {
    UtilsNotificacao.log(`Error sending push notification in FilaSolicitacao afterSave: ${error.message}`, 'error');
    
    // Registrar erro em sistema de logs
    try {
      const errorLog = new Parse.Object("NotificacaoErroLog");
      errorLog.set("tipo", "trigger_fila_solicitacao");
      errorLog.set("objeto_id", solicitacao.id);
      errorLog.set("mensagem_erro", error.message);
      errorLog.set("stack", error.stack);
      errorLog.set("ambiente", ambiente);
      await errorLog.save(null, { useMasterKey: true });
    } catch (logError) {
      UtilsNotificacao.log(`Erro ao salvar log de erro: ${logError.message}`, 'error');
    }
  }
});

// Send notification when a solicitation is approved or rejected
Parse.Cloud.afterSave('Notificacao', async (request) => {
  const notificacao = request.object;
  const ambiente = UtilsNotificacao.isProducao() ? 'producao' : 'desenvolvimento';

  // Log no início do trigger apenas em ambiente não-produção
  if (!UtilsNotificacao.isProducao()) {
    UtilsNotificacao.log(`Notificacao afterSave - ObjectId: ${notificacao.id}, Tipo: ${notificacao.get('tipo')}`);
  }

  // Only run for new notifications
  if (request.object.existed()) {
    return;
  }

  try {
    const tipo = notificacao.get('tipo');
    
    // Lista explícita dos tipos aceitos para evitar processamento desnecessário
    const tiposAceitos = ['entrada_fila', 'cancelamento'];
    
    if (!tiposAceitos.includes(tipo)) {
      UtilsNotificacao.log(`Notificacao afterSave - Tipo não tratado: ${tipo}`, 'info');
      return;
    }
    
    // Extrair e normalizar ID da solicitação
    const solicitacaoId = UtilsNotificacao.normalizarId(notificacao.get('solicitacao_id'));
    
    if (!solicitacaoId) {
      UtilsNotificacao.log(`Notificacao afterSave - ID de solicitação inválido para tipo ${tipo}`, 'warn');
      return;
    }

    // Find the solicitation to get patient ID - usando ID normalizado
    const solicitacaoQuery = new Parse.Query('FilaSolicitacao');
    solicitacaoQuery.equalTo('objectId', solicitacaoId);
    
    // Otimizar a query para buscar apenas o campo necessário
    solicitacaoQuery.select('idPaciente');
    
    const solicitacao = await solicitacaoQuery.first({ useMasterKey: true });

    if (!solicitacao) {
      UtilsNotificacao.log(`Notificacao afterSave - Solicitação não encontrada: ${solicitacaoId}`, 'warn');
      return;
    }
    
    const idPaciente = UtilsNotificacao.normalizarId(solicitacao.get('idPaciente'));
    
    if (!idPaciente) {
      UtilsNotificacao.log(`Notificacao afterSave - Solicitação sem ID de paciente: ${solicitacaoId}`, 'warn');
      return;
    }

    if (tipo === 'entrada_fila') {
      // Notification for approved request
      await Parse.Cloud.run("enviarNotificacaoPush", {
        canais: [`patient_${idPaciente}`],
        titulo: 'Solicitação aprovada',
        mensagem: 'Sua solicitação de atendimento foi aprovada. Você está na fila!',
        tipo: 'solicitacao_aprovada',
        dadosAdicionais: {
          badge: 1,
          sound: 'default',
          category: 'REQUEST_APPROVED',
          notificacao_id: notificacao.id,
          ambiente: ambiente
        }
      }, { useMasterKey: true });
    } else if (tipo === 'cancelamento') {
      // Notification for rejected request
      await Parse.Cloud.run("enviarNotificacaoPush", {
        canais: [`patient_${idPaciente}`],
        titulo: 'Solicitação recusada',
        mensagem: 'Sua solicitação de atendimento foi recusada.',
        tipo: 'solicitacao_recusada',
        dadosAdicionais: {
          badge: 1,
          sound: 'default',
          category: 'REQUEST_REJECTED',
          notificacao_id: notificacao.id,
          ambiente: ambiente
        }
      }, { useMasterKey: true });
    }
  } catch (error) {
    UtilsNotificacao.log(`Error sending push notification in Notificacao afterSave: ${error.message}`, 'error');
    
    // Registrar erro em sistema de logs
    try {
      const errorLog = new Parse.Object("NotificacaoErroLog");
      errorLog.set("tipo", "trigger_notificacao");
      errorLog.set("objeto_id", notificacao.id);
      errorLog.set("mensagem_erro", error.message);
      errorLog.set("stack", error.stack);
      errorLog.set("ambiente", ambiente);
      await errorLog.save(null, { useMasterKey: true });
    } catch (logError) {
      UtilsNotificacao.log(`Erro ao salvar log de erro: ${logError.message}`, 'error');
    }
  }
});

// Send notification when a message is broadcasted to a queue
Parse.Cloud.afterSave('MensagemFila', async (request) => {
  const mensagem = request.object;
  const ambiente = UtilsNotificacao.isProducao() ? 'producao' : 'desenvolvimento';

  // Log no início do trigger apenas em ambiente não-produção
  if (!UtilsNotificacao.isProducao()) {
    UtilsNotificacao.log(`MensagemFila afterSave - ObjectId: ${mensagem.id}, É objeto existente: ${request.object.existed()}`);
  }

  // Only run for new messages
  if (request.object.existed()) {
    return;
  }

  try {
    // Extrair e normalizar IDs
    const medicoId = UtilsNotificacao.normalizarId(mensagem.get('medico_id'));
    const consultorioId = UtilsNotificacao.normalizarId(mensagem.get('consultorio_id'));
    const mensagemTexto = mensagem.get('mensagem') || mensagem.get('texto');

    // Validar dados necessários
    if (!medicoId || !consultorioId || !mensagemTexto) {
      UtilsNotificacao.log(`MensagemFila afterSave - Dados inválidos: medicoId=${medicoId}, consultorioId=${consultorioId}, mensagem=${mensagemTexto ? 'ok' : 'faltando'}`, 'warn');
      return;
    }

    // Get all patients in this doctor's queue with optimized query
    const filaQuery = new Parse.Query('Fila');
    filaQuery.equalTo('medico', Parse.Object.createWithoutData('Medico', medicoId));
    filaQuery.equalTo('consultorio', Parse.Object.createWithoutData('consultorio', consultorioId));
    filaQuery.equalTo('status', 'aguardando');
    
    // Otimizar a query para buscar apenas o campo necessário
    filaQuery.select('idPaciente');
    
    // Limite de busca para evitar problemas de desempenho
    filaQuery.limit(1000);

    const filaResults = await filaQuery.find({ useMasterKey: true });
    
    UtilsNotificacao.log(`Encontrados ${filaResults.length} pacientes na fila para enviar mensagem`);

    // Processar pacientes em lotes para melhor desempenho
    const BATCH_SIZE = 50;
    const pacientesIds = filaResults
      .map(fila => UtilsNotificacao.normalizarId(fila.get('idPaciente')))
      .filter(Boolean);

    // Agrupar em lotes e enviar para cada lote
    for (let i = 0; i < pacientesIds.length; i += BATCH_SIZE) {
      const lotePacientes = pacientesIds.slice(i, i + BATCH_SIZE);
      const canaisLote = lotePacientes.map(idPaciente => `patient_${idPaciente}`);
      
      // Enviar para todos pacientes do lote de uma vez
      if (canaisLote.length > 0) {
        await Parse.Cloud.run("enviarNotificacaoPush", {
          canais: canaisLote,
          titulo: mensagem.get('titulo') || 'Mensagem do consultório',
          mensagem: mensagemTexto,
          tipo: 'mensagem_fila',
          dadosAdicionais: {
            badge: 1,
            sound: 'default',
            category: 'QUEUE_MESSAGE',
            mensagem_id: mensagem.id,
            ambiente: ambiente
          }
        }, { useMasterKey: true });
        
        UtilsNotificacao.log(`Mensagem enviada para lote ${i/BATCH_SIZE + 1} com ${canaisLote.length} pacientes`);
      }
    }

    UtilsNotificacao.log(`Mensagem enviada para todos os ${pacientesIds.length} pacientes`);
  } catch (error) {
    UtilsNotificacao.log(`Error sending push notification in MensagemFila afterSave: ${error.message}`, 'error');
    
    // Registrar erro em sistema de logs
    try {
      const errorLog = new Parse.Object("NotificacaoErroLog");
      errorLog.set("tipo", "trigger_mensagem_fila");
      errorLog.set("objeto_id", mensagem.id);
      errorLog.set("mensagem_erro", error.message);
      errorLog.set("stack", error.stack);
      errorLog.set("ambiente", ambiente);
      await errorLog.save(null, { useMasterKey: true });
    } catch (logError) {
      UtilsNotificacao.log(`Erro ao salvar log de erro: ${logError.message}`, 'error');
    }
  }
});

// Cloud Job para limpeza de logs antigos - para evitar crescimento descontrolado do banco
Parse.Cloud.job("limparLogsNotificacoesAntigos", async (request) => {
  const { params, headers, log, message } = request;
  const diasRetencao = params.diasRetencao || 30; // padrão: 30 dias
  
  try {
    log(`Iniciando limpeza de logs de notificações mais antigos que ${diasRetencao} dias`);
    
    const dataLimite = new Date();
    dataLimite.setDate(dataLimite.getDate() - diasRetencao);
    
    // Limpar logs de notificação
    const queryLogs = new Parse.Query("NotificacaoLog");
    queryLogs.lessThan("data_envio", dataLimite);
    queryLogs.limit(1000);
    
    const logsAntigos = await queryLogs.find({ useMasterKey: true });
    log(`Encontrados ${logsAntigos.length} logs de notificação para excluir`);
    
    if (logsAntigos.length > 0) {
      await Parse.Object.destroyAll(logsAntigos, { useMasterKey: true });
      log(`${logsAntigos.length} logs de notificação excluídos com sucesso`);
    }
    
    // Limpar logs de erro
    const queryErros = new Parse.Query("NotificacaoErroLog");
    queryErros.lessThan("createdAt", dataLimite);
    queryErros.limit(1000);
    
    const errosAntigos = await queryErros.find({ useMasterKey: true });
    log(`Encontrados ${errosAntigos.length} logs de erro para excluir`);
    
    if (errosAntigos.length > 0) {
      await Parse.Object.destroyAll(errosAntigos, { useMasterKey: true });
      log(`${errosAntigos.length} logs de erro excluídos com sucesso`);
    }
    
    return {
      success: true,
      logsExcluidos: logsAntigos.length + errosAntigos.length
    };
  } catch (error) {
    log(`Erro ao limpar logs antigos: ${error.message}`);
    return {
      success: false,
      error: error.message
    };
  }
});

