/**
 * Funções Cloud Code para gerenciamento de redefinição de senha
 * Fila App - Sistema simplificado usando o método nativo do Parse
 */

/**
 * Solicita redefinição de senha para um usuário
 *
 * @param {Object} request - Objeto de requisição Parse
 * @param {String} request.params.email - Email do usuário
 * @param {String} request.params.userType - Tipo de usuário (opcional)
 * @returns {Object} Resposta com sucesso/erro e mensagem
 */
Parse.Cloud.define("requestPasswordReset", async (request) => {
    try {
      const { email, userType } = request.params;
  
      // Verificação básica
      if (!email) {
        return { success: false, message: "Email não fornecido" };
      }
  
      console.log(`Solicitação de redefinição de senha para email: ${email}, tipo: ${userType || 'não especificado'}`);
  
      // Buscar o usuário pelo email
      const query = new Parse.Query(Parse.User);
      query.equalTo("email", email);
  
      // Se tivermos o tipo de usuário, refinar a busca
      if (userType) {
        query.equalTo("tipo", userType);
      }
  
      const user = await query.first({ useMasterKey: true });
  
      if (!user) {
        console.log(`Usuário não encontrado para o email: ${email}`);
        // Por segurança, não revelamos se o email existe ou não
        return {
          success: true,
          message: "Se o email estiver cadastrado, um link de redefinição será enviado."
        };
      }
  
      console.log(`Usuário encontrado: ${user.get("username") || user.get("email")}`);
  
      // Usar o método nativo do Parse para redefinição de senha
      try {
        const tempUser = new Parse.User();
        tempUser.setEmail(email);
        await tempUser.requestPasswordReset({ useMasterKey: true });
  
        console.log(`Email de redefinição enviado com sucesso para ${email} via método nativo`);
  
        // Registrar o sucesso no log de segurança
        try {
          const securityLog = new Parse.Object("SecurityLog");
          securityLog.set("action", "password_reset_email_sent");
          securityLog.set("user", user);
          securityLog.set("details", `Email de redefinição enviado para ${email} via método nativo`);
          await securityLog.save(null, { useMasterKey: true });
        } catch (logError) {
          console.error("Erro ao registrar log de segurança:", logError);
        }
  
        return {
          success: true,
          message: "Se o email estiver cadastrado, um link de redefinição será enviado."
        };
      } catch (error) {
        console.error("Erro ao enviar email de redefinição via método nativo:", error);
  
        // Registrar o erro no log de segurança
        try {
          const securityLog = new Parse.Object("SecurityLog");
          securityLog.set("action", "password_reset_email_error");
          securityLog.set("user", user);
          securityLog.set("details", `Erro ao enviar email para ${email}: ${error.message}`);
          await securityLog.save(null, { useMasterKey: true });
        } catch (logError) {
          console.error("Erro ao registrar falha de email:", logError);
        }
  
        return {
          success: false,
          message: "Ocorreu um erro ao enviar o email de redefinição. Por favor, tente novamente mais tarde."
        };
      }
    } catch (error) {
      console.error("Erro ao processar solicitação de redefinição de senha:", error);
      return {
        success: false,
        message: "Ocorreu um erro ao processar sua solicitação."
      };
    }
  });
  
  /**
   * Função específica para administradores gerarem links de redefinição
   * para usuários de consultórios e secretárias
   *
   * @param {Object} request - Objeto de requisição Parse
   * @param {String} request.params.userId - ID do usuário para gerar o link
   * @param {String} request.params.userType - Tipo de usuário (consultorio ou secretaria)
   * @returns {Object} Resposta com o link gerado ou mensagem de erro
   */
  Parse.Cloud.define("generateResetLinkForUser", async (request) => {
    // Verificar se o usuário atual é um administrador
    if (!request.user || request.user.get("tipo") !== "admin") {
      return {
        success: false,
        message: "Apenas administradores podem gerar links de redefinição para outros usuários"
      };
    }
  
    try {
      const { userId, userType } = request.params;
  
      if (!userId || !userType) {
        return {
          success: false,
          message: "ID do usuário e tipo são obrigatórios"
        };
      }
  
      // Verificar se é um tipo válido
      if (userType !== "consultorio" && userType !== "secretaria") {
        return {
          success: false,
          message: "Tipo de usuário inválido. Deve ser 'consultorio' ou 'secretaria'"
        };
      }
  
      // Buscar o usuário
      const user = await new Parse.Query(Parse.User)
        .equalTo("objectId", userId)
        .equalTo("tipo", userType)
        .first({ useMasterKey: true });
  
      if (!user) {
        return {
          success: false,
          message: `Usuário do tipo ${userType} não encontrado com o ID fornecido`
        };
      }
  
      // Usar o método nativo do Parse para redefinição de senha
      try {
        const tempUser = new Parse.User();
        tempUser.setEmail(user.get("email"));
        await tempUser.requestPasswordReset({ useMasterKey: true });
  
        // Registrar log da ação
        const log = new Parse.Object("AdminActionLog");
        log.set("admin", request.user);
        log.set("action", "generate_reset_link");
        log.set("targetUser", user);
        log.set("details", `Link de redefinição gerado para usuário do tipo ${userType}`);
        await log.save(null, { useMasterKey: true });
  
        return {
          success: true,
          message: "Email de redefinição de senha enviado com sucesso",
          email: user.get("email"),
          info: "Usando método nativo do Parse para redefinição de senha"
        };
      } catch (error) {
        console.error("Erro ao enviar email de redefinição:", error);
        return {
          success: false,
          message: "Ocorreu um erro ao enviar o email de redefinição. Por favor, tente novamente mais tarde."
        };
      }
    } catch (error) {
      console.error("Erro ao gerar link de redefinição:", error);
      return {
        success: false,
        message: "Ocorreu um erro ao gerar o link de redefinição de senha"
      };
    }
  });
  
  /**
   * Função específica para consultórios gerarem links de redefinição
   * para suas secretárias
   *
   * @param {Object} request - Objeto de requisição Parse
   * @param {String} request.params.secretariaId - ID da secretária
   * @returns {Object} Resposta com o link gerado ou mensagem de erro
   */
  Parse.Cloud.define("generateResetLinkForSecretaria", async (request) => {
    // Verificar se o usuário atual é um consultório
    if (!request.user || request.user.get("tipo") !== "consultorio") {
      return {
        success: false,
        message: "Apenas consultórios podem gerar links de redefinição para suas secretárias"
      };
    }
  
    try {
      const { secretariaId } = request.params;
  
      if (!secretariaId) {
        return {
          success: false,
          message: "ID da secretária é obrigatório"
        };
      }
  
      // Buscar o consultório do usuário atual
      const consultorioQuery = new Parse.Query("consultorio");
      consultorioQuery.equalTo("user_consultorio", request.user);
      const consultorio = await consultorioQuery.first({ useMasterKey: true });
  
      if (!consultorio) {
        return {
          success: false,
          message: "Consultório não encontrado para o usuário atual"
        };
      }
  
      // Buscar a secretária e confirmar que pertence a este consultório
      const secretariaQuery = new Parse.Query("Secretaria");
      secretariaQuery.equalTo("objectId", secretariaId);
      secretariaQuery.equalTo("consultorio", consultorio);
      secretariaQuery.include("user_secretaria");
  
      const secretaria = await secretariaQuery.first({ useMasterKey: true });
  
      if (!secretaria) {
        return {
          success: false,
          message: "Secretária não encontrada ou não pertence a este consultório"
        };
      }
  
      const userSecretaria = secretaria.get("user_secretaria");
  
      if (!userSecretaria) {
        return {
          success: false,
          message: "Esta secretária não possui um usuário associado"
        };
      }
  
      // Usar o método nativo do Parse para redefinição de senha
      try {
        const tempUser = new Parse.User();
        tempUser.setEmail(userSecretaria.get("email"));
        await tempUser.requestPasswordReset({ useMasterKey: true });
  
        // Registrar log da ação
        const log = new Parse.Object("ConsultorioActionLog");
        log.set("consultorio", consultorio);
        log.set("consultorioUser", request.user);
        log.set("action", "generate_reset_link_secretaria");
        log.set("targetSecretaria", secretaria);
        log.set("targetUser", userSecretaria);
        log.set("details", "Link de redefinição gerado para secretária");
        await log.save(null, { useMasterKey: true });
  
        return {
          success: true,
          message: "Email de redefinição de senha enviado com sucesso",
          email: userSecretaria.get("email"),
          info: "Usando método nativo do Parse para redefinição de senha"
        };
      } catch (error) {
        console.error("Erro ao enviar email de redefinição:", error);
        return {
          success: false,
          message: "Ocorreu um erro ao enviar o email de redefinição. Por favor, tente novamente mais tarde."
        };
      }
    } catch (error) {
      console.error("Erro ao gerar link de redefinição para secretária:", error);
      return {
        success: false,
        message: "Ocorreu um erro ao gerar o link de redefinição de senha"
      };
    }
  });
  