/**
 * Sistema de Notificações Push para Produção
 * 
 * Este arquivo contém funções otimizadas para o gerenciamento de notificações
 * push em ambiente de produção, garantindo entrega confiável e rastreamento.
 */

// Função para registrar dispositivo para notificações em produção
Parse.Cloud.define("registerDeviceForPush", async (request) => {
  try {
    const { 
      deviceId,    // ID único do dispositivo (obrigatório)
      userId,      // ID do usuário logado (obrigatório)
      token,       // Token FCM (obrigatório)
      deviceType,  // "ios" ou "android" (opcional, detectado automaticamente)
      userType     // "paciente", "medico", "secretaria", "hospital", "admin" (obrigatório)
    } = request.params;
    
    // Validação de parâmetros obrigatórios
    if (!deviceId || !userId || !token || !userType) {
      throw new Error("Parâmetros obrigatórios: deviceId, userId, token, userType");
    }
    
    console.log(`Registrando dispositivo: ${deviceId} (${userType}) com token: ${token.substring(0, 10)}...`);
    
    // Determinar tipo de dispositivo
    const finalDeviceType = deviceType || (request.params.deviceType === "ios" ? "ios" : "android");
    
    // Buscar instalação existente
    const query = new Parse.Query(Parse.Installation);
    query.equalTo("installationId", deviceId);
    let installation = await query.first({ useMasterKey: true });
    
    // Criar nova instalação se não existir
    if (!installation) {
      installation = new Parse.Installation();
      installation.set("installationId", deviceId);
      installation.set("deviceType", finalDeviceType);
      console.log(`Criando nova instalação para dispositivo: ${deviceId}`);
    } else {
      console.log(`Atualizando instalação existente: ${installation.id}`);
    }
    
    // Configurar dados da instalação
    installation.set("deviceToken", token);
    installation.set("userId", userId);
    installation.set("userType", userType);
    
    // Configurar canais de notificação baseados no tipo de usuário
    const channels = ["global", "allDevices"];
    
    // Adicionar canais específicos por tipo de usuário
    channels.push(`user_${userId}`);
    channels.push(`${userType}_${userId}`);
    
    // Canais específicos por tipo
    switch (userType) {
      case "paciente":
        channels.push("pacientes");
        channels.push(`patient_${userId}`);
        break;
      case "medico":
        channels.push("medicos");
        channels.push(`doctor_${userId}`);
        break;
      case "secretaria":
        channels.push("secretarias");
        break;
      case "hospital":
        channels.push("hospitais");
        channels.push(`hospital_${userId}`);
        break;
      case "admin":
        channels.push("admins");
        break;
    }
    
    // Remover duplicatas
    const uniqueChannels = [...new Set(channels)];
    installation.set("channels", uniqueChannels);
    
    // Salvar instalação
    await installation.save(null, { useMasterKey: true });
    
    console.log(`Dispositivo registrado com sucesso: ${deviceId}`);
    console.log(`Canais configurados: ${uniqueChannels.join(", ")}`);
    
    // Registrar log de sucesso
    try {
      const registrationLog = new Parse.Object("NotificationRegistrationLog");
      registrationLog.set("deviceId", deviceId);
      registrationLog.set("userId", userId);
      registrationLog.set("userType", userType);
      registrationLog.set("deviceType", finalDeviceType);
      registrationLog.set("channels", uniqueChannels);
      registrationLog.set("success", true);
      registrationLog.set("timestamp", new Date());
      await registrationLog.save(null, { useMasterKey: true });
    } catch (logError) {
      console.warn("Erro ao salvar log de registro:", logError.message);
    }
    
    return {
      success: true,
      message: "Dispositivo registrado com sucesso",
      installationId: installation.id,
      channels: uniqueChannels
    };
  } catch (error) {
    console.error("Erro ao registrar dispositivo:", error);
    
    // Registrar log de erro
    try {
      const errorLog = new Parse.Object("NotificationErrorLog");
      errorLog.set("deviceId", request.params.deviceId);
      errorLog.set("userId", request.params.userId);
      errorLog.set("userType", request.params.userType);
      errorLog.set("error", error.message);
      errorLog.set("timestamp", new Date());
      await errorLog.save(null, { useMasterKey: true });
    } catch (logError) {
      console.warn("Erro ao salvar log de erro:", logError.message);
    }
    
    return {
      success: false,
      error: error.message
    };
  }
});

// Função centralizada para envio de notificações push em produção
Parse.Cloud.define("enviarNotificacaoPush", async (request) => {
  try {
    const {
      destinatarios,     // Array com IDs dos usuários destinatários
      canais,            // Array com nomes dos canais
      titulo,            // Título da notificação
      mensagem,          // Texto da notificação
      tipo,              // Tipo para processamento no cliente
      dadosAdicionais,   // Dados extras para a notificação
      tipoDestinatario,  // Tipo dos usuários destinatários (paciente, medico, etc.)
      prioridade,        // Prioridade da notificação (high, normal)
      silenciosa = false // Se a notificação deve ser silenciosa (sem som/vibração)
    } = request.params;

    // Validação básica dos parâmetros
    if ((!destinatarios || destinatarios.length === 0) && (!canais || canais.length === 0)) {
      throw new Error("É necessário fornecer pelo menos um canal ou um destinatário");
    }

    console.log(`Enviando notificação: "${titulo}" para ${destinatarios?.length || 0} destinatários / ${canais?.length || 0} canais`);

    // Dados da notificação otimizados para iOS e Android
    const pushData = {
      // Campos comuns
      title: titulo,
      alert: mensagem,
      badge: "Increment",
      tipo: tipo || "geral",
      
      // Dados adicionais
      ...dadosAdicionais,
      
      // Android
      android: {
        notification: {
          title: titulo,
          body: mensagem,
          click_action: "FLUTTER_NOTIFICATION_CLICK",
          priority: prioridade === 'high' ? 'high' : 'normal',
          channel_id: "saude_sem_espera_channel"
        }
      },
      
      // iOS
      ios: {
        alert: {
          title: titulo,
          body: mensagem
        },
        badge: "Increment",
        sound: silenciosa ? null : "default",
        content_available: silenciosa,
        category: tipo || "DEFAULT_CATEGORY"
      }
    };

    // Adicionar timestamp para evitar duplicação
    pushData.timestamp = Date.now();

    // Estratégia de envio
    let targetQuery;
    let pushStatus;

    if (canais && canais.length > 0) {
      // Enviar para canais específicos
      console.log(`Enviando para canais: ${canais.join(", ")}`);
      pushStatus = await Parse.Push.send({
        channels: canais,
        data: pushData
      }, { useMasterKey: true });
    } else if (destinatarios && destinatarios.length > 0) {
      // Criar query de instalações para estes destinatários
      targetQuery = new Parse.Query(Parse.Installation);
      targetQuery.containedIn("userId", destinatarios);

      // Se foi especificado um tipo de destinatário, adicionar à query
      if (tipoDestinatario) {
        targetQuery.equalTo("userType", tipoDestinatario);
      }

      console.log(`Enviando para ${destinatarios.length} destinatários do tipo ${tipoDestinatario || "qualquer"}`);
      pushStatus = await Parse.Push.send({
        where: targetQuery,
        data: pushData
      }, { useMasterKey: true });
    }

    console.log("Notificação enviada com sucesso:", pushStatus);

    // Registrar a notificação para fins de auditoria
    try {
      const logNotificacao = new Parse.Object("NotificacaoLog");
      logNotificacao.set("titulo", titulo);
      logNotificacao.set("mensagem", mensagem);
      logNotificacao.set("tipo", tipo);
      logNotificacao.set("destinatarios_count", destinatarios?.length || 0);
      logNotificacao.set("canais_count", canais?.length || 0);
      logNotificacao.set("data_envio", new Date());
      logNotificacao.set("sucesso", true);
      await logNotificacao.save(null, { useMasterKey: true });
    } catch (logError) {
      // Apenas registrar o erro, não impedir o fluxo principal
      console.warn("Erro ao registrar log de notificação:", logError.message);
    }

    return {
      success: true,
      message: "Notificação enviada com sucesso"
    };
  } catch (error) {
    console.error("Erro ao enviar notificação:", error);
    
    // Registrar o erro
    try {
      const errorLog = new Parse.Object("NotificationErrorLog");
      errorLog.set("titulo", request.params.titulo);
      errorLog.set("mensagem", request.params.mensagem);
      errorLog.set("tipo", request.params.tipo);
      errorLog.set("error", error.message);
      errorLog.set("timestamp", new Date());
      await errorLog.save(null, { useMasterKey: true });
    } catch (logError) {
      console.warn("Erro ao salvar log de erro:", logError.message);
    }
    
    return {
      success: false,
      error: error.message
    };
  }
});

// Função para validar instalação e corrigir problemas
Parse.Cloud.define("validatePushToken", async (request) => {
  try {
    const { deviceId, token } = request.params;
    
    if (!deviceId) {
      throw new Error("É necessário fornecer o ID do dispositivo");
    }
    
    console.log(`Validando instalação para dispositivo: ${deviceId}`);
    
    // Buscar instalação
    const query = new Parse.Query(Parse.Installation);
    query.equalTo("installationId", deviceId);
    const installation = await query.first({ useMasterKey: true });
    
    if (!installation) {
      console.log(`Nenhuma instalação encontrada para dispositivo: ${deviceId}`);
      return {
        success: true,
        isValid: false,
        reason: "installation_not_found"
      };
    }
    
    // Verificar token
    const currentToken = installation.get("deviceToken");
    const tokenMatch = token && currentToken === token;
    
    // Verificar canais
    const channels = installation.get("channels") || [];
    const hasRequiredChannels = channels.includes(`user_${deviceId}`) && 
                               (channels.includes(`patient_${deviceId}`) || 
                                channels.includes(`doctor_${deviceId}`) || 
                                channels.includes(`hospital_${deviceId}`));
    
    // Corrigir canais se necessário
    let channelsFixed = false;
    if (!hasRequiredChannels) {
      // Adicionar canais necessários
      const userType = installation.get("userType") || "paciente";
      const userId = installation.get("userId") || deviceId;
      
      const updatedChannels = [...channels];
      
      if (!updatedChannels.includes("global")) updatedChannels.push("global");
      if (!updatedChannels.includes("allDevices")) updatedChannels.push("allDevices");
      if (!updatedChannels.includes(`user_${userId}`)) updatedChannels.push(`user_${userId}`);
      
      switch (userType) {
        case "paciente":
          if (!updatedChannels.includes(`patient_${userId}`)) updatedChannels.push(`patient_${userId}`);
          if (!updatedChannels.includes("pacientes")) updatedChannels.push("pacientes");
          break;
        case "medico":
          if (!updatedChannels.includes(`doctor_${userId}`)) updatedChannels.push(`doctor_${userId}`);
          if (!updatedChannels.includes("medicos")) updatedChannels.push("medicos");
          break;
        case "hospital":
          if (!updatedChannels.includes(`hospital_${userId}`)) updatedChannels.push(`hospital_${userId}`);
          if (!updatedChannels.includes("hospitais")) updatedChannels.push("hospitais");
          break;
      }
      
      // Remover duplicatas
      const uniqueChannels = [...new Set(updatedChannels)];
      
      // Atualizar canais
      installation.set("channels", uniqueChannels);
      await installation.save(null, { useMasterKey: true });
      
      channelsFixed = true;
      console.log(`Canais corrigidos para dispositivo: ${deviceId}`);
      console.log(`Novos canais: ${uniqueChannels.join(", ")}`);
    }
    
    return {
      success: true,
      isValid: true,
      tokenMatch: tokenMatch,
      channels: channels,
      channelsFixed: channelsFixed,
      warning: channelsFixed ? "channels_fixed" : null
    };
  } catch (error) {
    console.error("Erro ao validar instalação:", error);
    
    return {
      success: false,
      error: error.message
    };
  }
});

// Triggers para envio automático de notificações

// Notificação quando a posição na fila muda
Parse.Cloud.afterSave("Fila", async (request) => {
  const filaObject = request.object;

  // Pular se não for uma atualização ou se for um objeto novo
  if (!request.original) return;

  const posicaoAtual = filaObject.get("posicao");
  const posicaoAnterior = request.original.get("posicao");
  const idPaciente = filaObject.get("idPaciente");
  const status = filaObject.get("status");
  const statusAnterior = request.original.get("status");

  try {
    console.log(`Processando alterações na fila: paciente ${idPaciente}, posição ${posicaoAnterior} -> ${posicaoAtual}, status ${statusAnterior} -> ${status}`);

    // Caso 1: Posição mudou na fila
    if (posicaoAnterior && posicaoAtual !== posicaoAnterior) {
      // Usar função centralizada para enviar notificação
      await Parse.Cloud.run("enviarNotificacaoPush", {
        canais: [`patient_${idPaciente}`],
        titulo: "Atualização na fila",
        mensagem: `Sua posição na fila mudou de ${posicaoAnterior} para ${posicaoAtual}.`,
        tipo: "posicao_alterada",
        dadosAdicionais: {
          fila_id: filaObject.id,
          posicao_anterior: posicaoAnterior,
          posicao_atual: posicaoAtual
        },
        prioridade: "high"
      }, { useMasterKey: true });
      
      console.log(`Notificação enviada: posição alterada de ${posicaoAnterior} para ${posicaoAtual}`);
    }

    // Caso 2: Quase sua vez (posição <= 3)
    if (posicaoAtual <= 3 && posicaoAnterior > 3) {
      await Parse.Cloud.run("enviarNotificacaoPush", {
        canais: [`patient_${idPaciente}`],
        titulo: "Quase sua vez!",
        mensagem: `Sua posição na fila agora é ${posicaoAtual}. Prepare-se para ser atendido!`,
        tipo: "quase_sua_vez",
        dadosAdicionais: {
          fila_id: filaObject.id,
          posicao_atual: posicaoAtual
        },
        prioridade: "high"
      }, { useMasterKey: true });
      
      console.log(`Notificação enviada: quase sua vez (posição ${posicaoAtual})`);
    }

    // Caso 3: Sua vez (posição = 1)
    if (posicaoAtual === 1 && posicaoAnterior > 1) {
      await Parse.Cloud.run("enviarNotificacaoPush", {
        canais: [`patient_${idPaciente}`],
        titulo: "É a sua vez!",
        mensagem: "Você é o próximo a ser atendido. Por favor, dirija-se ao consultório.",
        tipo: "sua_vez",
        dadosAdicionais: {
          fila_id: filaObject.id
        },
        prioridade: "high"
      }, { useMasterKey: true });
      
      console.log(`Notificação enviada: é a sua vez`);
    }

    // Caso 4: Status mudou para "em_atendimento"
    if (status === "em_atendimento" && statusAnterior !== "em_atendimento") {
      await Parse.Cloud.run("enviarNotificacaoPush", {
        canais: [`patient_${idPaciente}`],
        titulo: "Atendimento iniciado",
        mensagem: "Seu atendimento foi iniciado. Por favor, dirija-se ao consultório imediatamente.",
        tipo: "atendimento_iniciado",
        dadosAdicionais: {
          fila_id: filaObject.id
        },
        prioridade: "high"
      }, { useMasterKey: true });
      
      console.log(`Notificação enviada: atendimento iniciado`);
    }

    // Caso 5: Status mudou para "finalizado"
    if (status === "finalizado" && statusAnterior !== "finalizado") {
      await Parse.Cloud.run("enviarNotificacaoPush", {
        canais: [`patient_${idPaciente}`],
        titulo: "Atendimento finalizado",
        mensagem: "Seu atendimento foi finalizado. Obrigado por utilizar nosso serviço.",
        tipo: "atendimento_finalizado",
        dadosAdicionais: {
          fila_id: filaObject.id
        }
      }, { useMasterKey: true });
      
      console.log(`Notificação enviada: atendimento finalizado`);
    }
  } catch (error) {
    console.error("Erro ao enviar notificação de alteração na fila:", error);
  }
});

// Notificação quando uma solicitação de entrada na fila é criada
Parse.Cloud.afterSave("FilaSolicitacao", async (request) => {
  const solicitacao = request.object;

  // Executar apenas para objetos novos com status "pendente"
  if (request.object.existed() || solicitacao.get("status") !== "pendente") return;

  try {
    // Obter IDs do médico e hospital
    const medicoId = solicitacao.get("medicoId")?.id;
    const hospitalId = solicitacao.get("hospitalId")?.id;

    if (!medicoId || !hospitalId) return;

    console.log(`Nova solicitação de fila: médico ${medicoId}, hospital ${hospitalId}`);

    // Notificar médicos e secretárias do hospital
    await Parse.Cloud.run("enviarNotificacaoPush", {
      canais: [`doctor_${medicoId}`, `hospital_${hospitalId}`],
      titulo: "Nova solicitação",
      mensagem: "Um paciente solicitou entrada na fila de atendimento.",
      tipo: "nova_solicitacao",
      dadosAdicionais: {
        solicitacao_id: solicitacao.id,
        medico_id: medicoId,
        hospital_id: hospitalId
      },
      prioridade: "high"
    }, { useMasterKey: true });
    
    console.log(`Notificação enviada: nova solicitação de fila`);
  } catch (error) {
    console.error("Erro ao enviar notificação de nova solicitação:", error);
  }
});

// Notificação quando uma mensagem é enviada para a fila
Parse.Cloud.afterSave("MensagemFila", async (request) => {
  const mensagem = request.object;

  // Executar apenas para mensagens novas
  if (request.object.existed()) return;

  try {
    const filaId = mensagem.get("filaId")?.id;
    const mensagemTexto = mensagem.get("mensagem");

    if (!filaId || !mensagemTexto) return;

    console.log(`Nova mensagem para fila ${filaId}: ${mensagemTexto.substring(0, 30)}...`);

    // Buscar todos os pacientes nesta fila
    const filaQuery = new Parse.Query("Fila");
    filaQuery.equalTo("objectId", filaId);
    const filaResults = await filaQuery.find({ useMasterKey: true });

    if (filaResults.length === 0) return;

    // Enviar mensagem para todos os pacientes nesta fila
    for (const fila of filaResults) {
      const idPaciente = fila.get("idPaciente");
      if (!idPaciente) continue;

      await Parse.Cloud.run("enviarNotificacaoPush", {
        canais: [`patient_${idPaciente}`],
        titulo: mensagem.get("titulo") || "Mensagem do consultório",
        mensagem: mensagemTexto,
        tipo: "mensagem_fila",
        dadosAdicionais: {
          mensagem_id: mensagem.id,
          fila_id: filaId
        }
      }, { useMasterKey: true });
    }
    
    console.log(`Mensagem enviada para ${filaResults.length} pacientes`);
  } catch (error) {
    console.error("Erro ao enviar notificação de mensagem:", error);
  }
});
