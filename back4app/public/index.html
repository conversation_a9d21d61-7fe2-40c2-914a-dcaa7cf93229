<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fila App - Sistema de Gerenciamento de Filas Médicas</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        :root {
          --primary-color: #4a90e2;
          --primary-dark: #3a7bc8;
          --secondary-color: #34cceb;
        }

        body {
          font-family: 'Roboto', sans-serif;
          margin: 0;
          padding: 0;
          background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
          min-height: 100vh;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          color: #333;
        }

        .container {
          width: 90%;
          max-width: 800px;
          background: white;
          padding: 40px;
          border-radius: 10px;
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
          text-align: center;
        }

        h1 {
          color: var(--primary-color);
          margin-top: 0;
          font-size: 2.5rem;
        }

        p {
          font-size: 1.1rem;
          line-height: 1.6;
          color: #555;
          margin-bottom: 25px;
        }

        .features {
          display: flex;
          flex-wrap: wrap;
          justify-content: center;
          gap: 20px;
          margin: 40px 0;
        }

        .feature {
          flex: 1 1 250px;
          padding: 20px;
          border: 1px solid #eee;
          border-radius: 8px;
          text-align: center;
        }

        .feature h3 {
          color: var(--primary-color);
          margin-top: 0;
        }

        .cta {
          margin-top: 40px;
        }

        .button {
          display: inline-block;
          background-color: var(--primary-color);
          color: white;
          padding: 12px 25px;
          border-radius: 5px;
          text-decoration: none;
          font-weight: 500;
          transition: background-color 0.2s;
          margin: 10px;
        }

        .button:hover {
          background-color: var(--primary-dark);
        }

        .button.secondary {
          background-color: transparent;
          border: 2px solid var(--primary-color);
          color: var(--primary-color);
        }

        .button.secondary:hover {
          background-color: rgba(74, 144, 226, 0.1);
        }

        .footer {
          margin-top: 40px;
          color: #666;
          font-size: 0.9rem;
        }

        @media (max-width: 600px) {
          .container {
            padding: 25px;
          }

          h1 {
            font-size: 2rem;
          }
        }
    </style>
</head>
<body>
<div class="container">
    <h1>Bem-vindo ao Fila App</h1>
    <p>Sistema de gerenciamento de filas médicas para consultórios e hospitais.</p>

    <div class="features">
        <div class="feature">
            <h3>Gestão de Filas</h3>
            <p>Organize o atendimento de pacientes de forma eficiente, reduzindo o tempo de espera.</p>
        </div>

        <div class="feature">
            <h3>Notificações</h3>
            <p>Envie alertas automáticos para pacientes sobre o andamento da fila e seu momento de atendimento.</p>
        </div>

        <div class="feature">
            <h3>Dashboard</h3>
            <p>Monitore o desempenho do atendimento com relatórios e estatísticas em tempo real.</p>
        </div>
    </div>

    <div class="cta">
        <a href="https://play.google.com/store" class="button" target="_blank">Baixar o App</a>
        <a href="reset-password.html" class="button secondary">Redefinir Senha</a>
    </div>

    <div class="footer">
        <p>&copy; 2023 Fila App - Todos os direitos reservados.</p>
    </div>
</div>
</body>
</html>