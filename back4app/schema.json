[{"className": "_User", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "username": {"type": "String"}, "password": {"type": "String"}, "email": {"type": "String"}, "emailVerified": {"type": "Boolean"}, "authData": {"type": "Object"}, "user_medico": {"type": "Relation", "targetClass": "Medico", "required": false}, "dataCadastro": {"type": "Date", "required": true}, "tipo": {"type": "String"}, "consultorio_user": {"type": "Relation", "targetClass": "consultorio"}, "user_consultorio": {"type": "Relation", "targetClass": "consultorio", "required": false}, "medico_user": {"type": "Relation", "targetClass": "Medico"}, "isAdmin": {"type": "Boolean", "defaultValue": false}, "user_secretaria": {"type": "Relation", "targetClass": "Secretaria"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}, "username_1": {"username": 1}, "case_insensitive_username": {"username": 1}, "email_1": {"email": 1}, "case_insensitive_email": {"email": 1}}}, {"className": "_Role", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "name": {"type": "String"}, "users": {"type": "Relation", "targetClass": "_User"}, "roles": {"type": "Relation", "targetClass": "_Role"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}, "name_1": {"name": 1}}}, {"className": "FilaFeedback", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "filaId": {"type": "Pointer", "targetClass": "<PERSON><PERSON>"}, "demoraAtendimento": {"type": "Boolean"}, "imprevistos": {"type": "Boolean"}, "atendimentoOutroLocal": {"type": "Boolean"}, "filaGrande": {"type": "Boolean"}, "semPrevisaoAtendimento": {"type": "Boolean"}, "semResposta": {"type": "Boolean"}, "outroMotivo": {"type": "String"}, "dataFeedback": {"type": "Date"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}}, {"className": "SolicitacaoCancelamento", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "motivo": {"type": "String"}, "solicitacao_id": {"type": "String"}, "tempo_espera": {"type": "Number"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}}, {"className": "QRCodeGerado", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "qr_id": {"type": "String"}, "sequencial": {"type": "String"}, "medico_id": {"type": "Pointer", "targetClass": "Medico"}, "consultorio_id": {"type": "Pointer", "targetClass": "consultorio"}, "valido": {"type": "Boolean"}, "impresso": {"type": "Boolean"}, "data_impressao": {"type": "Date"}, "data_expiracao": {"type": "Date"}, "versao": {"type": "String"}, "status": {"type": "String"}, "ultimo_acesso": {"type": "Date"}, "data_criacao": {"type": "Date"}, "data_invalidacao": {"type": "Date"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "Paciente", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "nome": {"type": "String", "required": true}, "telefone": {"type": "String", "required": false}, "paciente_atendimento": {"type": "Relation", "targetClass": "Atendimento", "required": false}, "paciente_notificacao": {"type": "Relation", "targetClass": "Notificacao", "required": false}, "notificado": {"type": "Boolean"}, "pushToken": {"type": "String"}, "dataCadastro": {"type": "Date"}, "ultimoAcesso": {"type": "Date"}, "userId": {"type": "String"}, "deviceInfo": {"type": "Object"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "QRCodeLog", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "action": {"type": "String"}, "qr_code": {"type": "Pointer", "targetClass": "QRCodeGerado"}, "details": {"type": "String"}, "timestamp": {"type": "Date"}, "medico_id": {"type": "Pointer", "targetClass": "Medico"}, "consultorio_id": {"type": "Pointer", "targetClass": "consultorio"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "MedicoConsultorio", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "medicoConsultorio_atendimento": {"type": "Relation", "targetClass": "Atendimento", "required": false}, "medicoConsultorio_medico": {"type": "Relation", "targetClass": "Medico", "required": false}, "medicoConsultorio_consultorio": {"type": "Relation", "targetClass": "consultorio", "required": false}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "FilaSolicitacao", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "medicoId": {"type": "Pointer", "targetClass": "Medico"}, "hospitalId": {"type": "Pointer", "targetClass": "consultorio"}, "status": {"type": "String"}, "timestamp": {"type": "Date"}, "idPaciente": {"type": "String"}, "solicitacao_id": {"type": "String", "required": true}, "telefone": {"type": "String"}, "nome": {"type": "String"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "MetricasAtendimento", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "medico_id": {"type": "Pointer", "targetClass": "Medico", "required": true}, "consultorio_id": {"type": "Pointer", "targetClass": "consultorio", "required": true}, "data": {"type": "Date", "required": true}, "tempo_medio_espera": {"type": "Number", "defaultValue": 0}, "tempo_medio_atendimento": {"type": "Number", "defaultValue": 0}, "total_pacientes": {"type": "Number", "defaultValue": 0}, "pacientes_atendidos": {"type": "Number", "defaultValue": 0}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}}, {"className": "Medico", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "especialidade": {"type": "String", "required": true}, "crm": {"type": "String", "required": true}, "medico_medicoConsultorio": {"type": "Relation", "targetClass": "MedicoConsultorio", "required": false}, "medico_user": {"type": "Relation", "targetClass": "_User", "required": false}, "cpf": {"type": "Number", "required": true}, "user_medico": {"type": "Pointer", "targetClass": "_User"}, "ativo": {"type": "Boolean"}, "dataCadastro": {"type": "Date"}, "nome": {"type": "String", "required": true}, "email": {"type": "String"}, "telefone": {"type": "String"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "<PERSON><PERSON>", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "status": {"type": "String", "defaultValue": "a<PERSON>ando", "required": true}, "data_entrada": {"type": "Date", "required": true}, "data_inicio_atendimento": {"type": "Date"}, "data_fim_atendimento": {"type": "Date"}, "data_saida": {"type": "Date"}, "tempo_estimado_minutos": {"type": "Number"}, "posicao": {"type": "Number", "required": true}, "medico": {"type": "Pointer", "targetClass": "Medico", "required": true}, "consultorio": {"type": "Pointer", "targetClass": "consultorio", "required": true}, "nome": {"type": "String", "required": true}, "telefone": {"type": "String"}, "idPaciente": {"type": "String", "required": true}, "solicitacao": {"type": "Pointer", "targetClass": "FilaSolicitacao"}, "tempo_atendimento": {"type": "Number"}, "data_atendimento": {"type": "Date"}, "usuario": {"type": "Pointer", "targetClass": "Usuario"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "Secretaria", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "nome": {"type": "String", "required": true}, "email": {"type": "String", "required": true}, "telefone": {"type": "String"}, "user_secretaria": {"type": "Pointer", "targetClass": "_User", "required": true}, "consultorio": {"type": "Pointer", "targetClass": "consultorio", "required": true}, "ativo": {"type": "Boolean", "defaultValue": true}, "dataCadastro": {"type": "Date"}, "cpf": {"type": "String"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "MensagemFila", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "mensagem": {"type": "String", "required": true}, "tipo": {"type": "String"}, "medico_id": {"type": "Pointer", "targetClass": "Medico", "required": true}, "consultorio_id": {"type": "Pointer", "targetClass": "consultorio", "required": true}, "secretaria_id": {"type": "Pointer", "targetClass": "Secretaria"}, "data_envio": {"type": "Date", "required": true}, "ativa": {"type": "Boolean", "defaultValue": true}, "consultorio": {"type": "Pointer", "targetClass": "consultorio"}, "titulo": {"type": "String"}, "icone": {"type": "String"}, "medico": {"type": "Pointer", "targetClass": "Medico"}, "texto": {"type": "String"}, "prioridade": {"type": "String"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}}, {"className": "Usuario", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "deviceId": {"type": "String", "required": true}, "nome": {"type": "String", "required": true}, "telefone": {"type": "String"}, "ultimoAcesso": {"type": "Date"}, "data_cadastro": {"type": "Date"}, "ultima_atualizacao": {"type": "Date"}, "em_fila": {"type": "Boolean"}, "ultimo_qrcode": {"type": "Pointer", "targetClass": "QRCodeGerado"}, "ultima_fila": {"type": "Date"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "Notificacao", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "tipo": {"type": "String"}, "solicitacao_id": {"type": "Pointer", "targetClass": "FilaSolicitacao"}, "fila_id": {"type": "Pointer", "targetClass": "<PERSON><PERSON>"}, "medico_id": {"type": "Pointer", "targetClass": "Medico"}, "consultorio_id": {"type": "Pointer", "targetClass": "consultorio"}, "lida": {"type": "Boolean", "defaultValue": false}, "created_at": {"type": "Date"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}}, {"className": "consultorio", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "localizacao": {"type": "GeoPoint"}, "latitude": {"type": "Number", "required": true}, "longitude": {"type": "Number", "required": true}, "consultorio_medicoConsultorio": {"type": "Relation", "targetClass": "MedicoConsultorio"}, "telefone": {"type": "String"}, "consultorio_user": {"type": "Pointer", "targetClass": "_User"}, "tipo": {"type": "String"}, "dataCadastro": {"type": "Date"}, "cnpj": {"type": "String", "required": true}, "nome": {"type": "String"}, "user_consultorio": {"type": "Pointer", "targetClass": "_User"}, "ativo": {"type": "Boolean"}, "medicos_vinculados": {"type": "Array"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "MensagemPredefinida", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "titulo": {"type": "String"}, "texto": {"type": "String"}, "consultorio": {"type": "Pointer", "targetClass": "consultorio"}, "prioridade": {"type": "String"}, "icone": {"type": "String"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "QRCodeImpressaoLog", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "qr_code": {"type": "Pointer", "targetClass": "QRCodeGerado"}, "data_impressao": {"type": "Date"}, "medico_id": {"type": "String"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}}, {"className": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "fila_id": {"type": "Pointer", "targetClass": "<PERSON><PERSON>"}, "motivo_saida": {"type": "String"}, "observacao": {"type": "String"}, "created_at": {"type": "Date"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "_Session", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "user": {"type": "Pointer", "targetClass": "_User"}, "installationId": {"type": "String"}, "sessionToken": {"type": "String"}, "expiresAt": {"type": "Date"}, "createdWith": {"type": "Object"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "Doctor", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "cpf": {"type": "String"}, "ativo": {"type": "Boolean"}, "especializacao": {"type": "String"}, "crm": {"type": "String"}, "nome": {"type": "String"}, "user": {"type": "Pointer", "targetClass": "_User"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "PasswordResetToken", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "token": {"type": "String"}, "user": {"type": "Pointer", "targetClass": "_User"}, "expiresAt": {"type": "Date"}, "used": {"type": "Boolean"}}, "classLevelPermissions": {"find": {"requiresAuthentication": true}, "count": {"requiresAuthentication": true}, "get": {"requiresAuthentication": true}, "create": {"requiresAuthentication": true}, "update": {"requiresAuthentication": true}, "delete": {"requiresAuthentication": true}, "addField": {"requiresMasterKey": true}, "protectedFields": {"*": ["token"]}}, "indexes": {"_id_": {"_id": 1}, "token_1": {"token": 1}, "expiresAt_1": {"expiresAt": 1}, "used_expiresAt_1": {"used": 1, "expiresAt": 1}}}, {"className": "LogDebug", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "tipo": {"type": "String"}, "data": {"type": "Date"}, "dados": {"type": "Object"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "Notification", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "body": {"type": "String"}, "read": {"type": "Boolean"}, "title": {"type": "String"}, "data": {"type": "Object"}, "sent": {"type": "Boolean"}, "userId": {"type": "String"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "SecurityLog", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "user": {"type": "Pointer", "targetClass": "_User"}, "action": {"type": "String"}, "details": {"type": "String"}, "ip": {"type": "String"}}, "classLevelPermissions": {"find": {"requiresMasterKey": true}, "count": {"requiresMasterKey": true}, "get": {"requiresMasterKey": true}, "create": {"requiresAuthentication": true}, "update": {"requiresMasterKey": true}, "delete": {"requiresMasterKey": true}, "addField": {"requiresMasterKey": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}, "user_1": {"user": 1}, "action_1": {"action": 1}, "createdAt_1": {"createdAt": 1}}}, {"className": "AdminActionLog", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "admin": {"type": "Pointer", "targetClass": "_User"}, "action": {"type": "String"}, "details": {"type": "String"}, "ip": {"type": "String"}}, "classLevelPermissions": {"find": {"requiresAuthentication": true}, "count": {"requiresAuthentication": true}, "get": {"requiresAuthentication": true}, "create": {"requiresAuthentication": true}, "update": {"requiresAuthentication": true}, "delete": {"requiresAuthentication": true}, "addField": {"requiresMasterKey": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}, "admin_1": {"admin": 1}, "action_1": {"action": 1}, "createdAt_1": {"createdAt": 1}}}, {"className": "NotificacaoLog", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "titulo": {"type": "String"}, "mensagem": {"type": "String"}, "tipo": {"type": "String"}, "destinatarios_count": {"type": "Number"}, "canais_count": {"type": "Number"}, "data_envio": {"type": "Date"}, "sucesso": {"type": "Boolean"}, "erro": {"type": "String"}, "data_erro": {"type": "Date"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}, "sucesso_1": {"sucesso": 1}, "tipo_1": {"tipo": 1}, "data_envio_1": {"data_envio": 1}}}, {"className": "InstallationLog", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "deviceId": {"type": "String"}, "userId": {"type": "String"}, "userType": {"type": "String"}, "channels": {"type": "Array"}, "data_registro": {"type": "Date"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}, "deviceId_1": {"deviceId": 1}, "userId_1": {"userId": 1}, "userType_1": {"userType": 1}}}]