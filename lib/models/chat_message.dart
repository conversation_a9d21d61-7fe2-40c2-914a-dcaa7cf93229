enum ChatMessageType {
  user,
  bot,
  system,
}

class ChatMessage {
  final String text;
  final ChatMessageType type;
  final DateTime timestamp;
  final bool isTyping;

  ChatMessage({
    required this.text,
    required this.type,
    DateTime? timestamp,
    this.isTyping = false,
  }) : timestamp = timestamp ?? DateTime.now();

  bool get isUser => type == ChatMessageType.user;
  bool get isBot => type == ChatMessageType.bot;

  // Método para garantir que o texto seja formatado corretamente
  String get formattedText {
    if (text.isEmpty) return '';

    // Formatar parágrafos corretamente e garantir que as quebras de linha sejam preservadas
    return text.trim();
  }

  static List<ChatMessage> getInitialMessages() {
    return [
      ChatMessage(
        text:
            'Olá! Sou o assistente virtual potencializado por IA do Saúde Sem Espera. Como posso ajudar você hoje?',
        type: ChatMessageType.bot,
      ),
    ];
  }

  // Lista de perguntas frequentes para sugestões
  static List<String> getSuggestedQuestions() {
    return [
      'Como entro na fila de atendimento?',
      'Como atualizar meus dados?',
      'Posso cancelar minha consulta?',
      'Quanto tempo vou esperar?',
      'Como funciona o aplicativo?',
    ];
  }
}
