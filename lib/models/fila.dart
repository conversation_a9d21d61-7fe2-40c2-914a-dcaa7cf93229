import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'package:flutter/foundation.dart';

class Fila extends ParseObject implements ParseCloneable {
  static const String _keyTableName = 'Fila';

  Fila() : super(_keyTableName);
  Fila.clone() : this();

  @override
  clone(Map<String, dynamic> map) => Fila.clone()..fromJson(map);

  // Getters
  String get nome => get<String>('nome') ?? '';
  String get telefone => get<String>('telefone') ?? '';
  String get idPaciente => get<String>('idPaciente') ?? '';
  String get status => get<String>('status') ?? '';
  int get posicao => get<int>('posicao') ?? 0;
  DateTime get dataEntrada => get<DateTime>('data_entrada') ?? DateTime.now();
  ParseObject? get medico => get<ParseObject>('medico');
  ParseObject? get consultorio => get<ParseObject>('consultorio');
  ParseObject? get solicitacao => get<ParseObject>('solicitacao');

  // Setters
  set nome(String value) => set('nome', value);
  set telefone(String value) => set('telefone', value);
  set idPaciente(String value) => set('idPaciente', value);
  set status(String value) => set('status', value);
  set posicao(int value) => set('posicao', value);
  set dataEntrada(DateTime value) => set('data_entrada', value);
  set medico(ParseObject? value) => set('medico', value);
  set consultorio(ParseObject? value) => set('consultorio', value);
  set solicitacao(ParseObject? value) => set('solicitacao', value);

  // Create a new Fila with public ACL
  static Future<Fila> createFila({
    required String nome,
    required String telefone,
    required String idPaciente,
    required int posicao,
    required ParseObject medico,
    required ParseObject consultorio,
    ParseObject? solicitacao,
  }) async {
    // Se o usuário existir, obter nome e telefone
    String nomeFinal = nome;
    String telefoneFinal = telefone;

    if (idPaciente.isNotEmpty) {
      try {
        // Buscar informações do usuário primeiro
        final queryUsuario = QueryBuilder<ParseObject>(ParseObject('Usuario'))
          ..whereEqualTo('deviceId', idPaciente);

        final usuarioResponse = await queryUsuario.query();

        if (usuarioResponse.success &&
            usuarioResponse.results != null &&
            usuarioResponse.results!.isNotEmpty) {
          final usuario = usuarioResponse.results!.first;

          // Verificar se há nome e telefone no usuario
          final usuarioNome = usuario.get<String>('nome');
          final usuarioTelefone = usuario.get<String>('telefone');

          if (usuarioNome != null && usuarioNome.isNotEmpty) {
            nomeFinal = usuarioNome;
            debugPrint("Nome atualizado do usuário: $nomeFinal");
          }

          if (usuarioTelefone != null && usuarioTelefone.isNotEmpty) {
            telefoneFinal = usuarioTelefone;
            debugPrint("Telefone atualizado do usuário: $telefoneFinal");
          }

          // Atualizar status do usuário para em_fila = true
          usuario.set('em_fila', true);
          usuario.set('ultima_fila', DateTime.now());

          // Configurar ACL pública
          final aclUsuario = ParseACL();
          aclUsuario.setPublicReadAccess(allowed: true);
          aclUsuario.setPublicWriteAccess(allowed: true);
          usuario.setACL(aclUsuario);

          final usuarioSaveResponse = await usuario.save();
          if (usuarioSaveResponse.success) {
            debugPrint("Status do usuário atualizado para em_fila=true");
          } else {
            debugPrint(
                "Erro ao atualizar status do usuário: ${usuarioSaveResponse.error?.message}");
          }
        } else {
          // Se não encontrar na tabela Usuario, buscar na tabela Paciente
          final queryPaciente =
              QueryBuilder<ParseObject>(ParseObject('Paciente'))
                ..whereEqualTo('userId', idPaciente);

          final pacienteResponse = await queryPaciente.query();

          if (pacienteResponse.success &&
              pacienteResponse.results != null &&
              pacienteResponse.results!.isNotEmpty) {
            final paciente = pacienteResponse.results!.first;

            // Verificar se há nome e telefone no paciente
            final pacienteNome = paciente.get<String>('nome');
            final pacienteTelefone = paciente.get<String>('telefone');

            if (pacienteNome != null && pacienteNome.isNotEmpty) {
              nomeFinal = pacienteNome;
              debugPrint("Nome atualizado do paciente: $nomeFinal");
            }

            if (pacienteTelefone != null && pacienteTelefone.isNotEmpty) {
              telefoneFinal = pacienteTelefone;
              debugPrint("Telefone atualizado do paciente: $telefoneFinal");
            }

            // Atualizar status do paciente para em_fila = true
            paciente.set('em_fila', true);
            paciente.set('ultima_fila', DateTime.now());

            // Configurar ACL pública
            final aclPaciente = ParseACL();
            aclPaciente.setPublicReadAccess(allowed: true);
            aclPaciente.setPublicWriteAccess(allowed: true);
            paciente.setACL(aclPaciente);

            final pacienteSaveResponse = await paciente.save();
            if (pacienteSaveResponse.success) {
              debugPrint("Status do paciente atualizado para em_fila=true");
            } else {
              debugPrint(
                  "Erro ao atualizar status do paciente: ${pacienteSaveResponse.error?.message}");
            }
          }
        }
      } catch (e) {
        debugPrint("Erro ao buscar informações do usuário: $e");
        // Continuar com as informações atuais
      }
    }

    final fila = Fila()
      ..nome = nomeFinal
      ..telefone = telefoneFinal
      ..idPaciente = idPaciente
      ..status = 'aguardando'
      ..posicao = posicao
      ..dataEntrada = DateTime.now()
      ..medico = medico
      ..consultorio = consultorio;

    if (solicitacao != null) {
      fila.solicitacao = solicitacao;
    }

    // Configure public ACL to avoid permission issues
    final acl = ParseACL();
    acl.setPublicReadAccess(allowed: true);
    acl.setPublicWriteAccess(allowed: true);

    // Add current user permissions
    final currentUser = await ParseUser.currentUser() as ParseUser?;
    if (currentUser != null) {
      acl.setReadAccess(userId: currentUser.objectId!, allowed: true);
      acl.setWriteAccess(userId: currentUser.objectId!, allowed: true);
    }

    fila.setACL(acl);
    return fila;
  }

  // Atualizar status de uma fila e garantir que o status do usuário também seja atualizado
  static Future<bool> updateStatus({
    required String filaId,
    required String status,
    required String idPaciente,
  }) async {
    try {
      // Parâmetros para a Cloud Function
      final params = <String, dynamic>{
        'filaId': filaId,
        'status': status,
        'pacienteId': idPaciente,
      };

      // Chamada à Cloud Function que garante a sincronização do status
      final response = await ParseCloudFunction('atualizarStatusPacienteFila')
          .execute(parameters: params);

      if (response.success) {
        return true;
      } else {
        throw Exception(
            response.error?.message ?? 'Erro ao atualizar status da fila');
      }
    } catch (e) {
      print('Erro ao atualizar status: $e');
      return false;
    }
  }

  // Sair da fila - implementação melhorada
  static Future<bool> leaveFila({
    required String filaId,
    required String idPaciente,
  }) async {
    try {
      debugPrint(
          "[Fila.leaveFila] Iniciando processo para sair da fila $filaId");

      // 1. Buscar a fila para confirmar que existe
      final filaQuery = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('objectId', filaId);

      final filaResponse = await filaQuery.query();

      if (!filaResponse.success ||
          filaResponse.results == null ||
          filaResponse.results!.isEmpty) {
        debugPrint("[Fila.leaveFila] Fila não encontrada com ID $filaId");
        return false;
      }

      final fila = filaResponse.results!.first;
      debugPrint(
          "[Fila.leaveFila] Fila encontrada. Status atual: ${fila.get<String>('status')}");

      // 2. Registrar saída na tabela FilaSaida
      try {
        final filaSaida = ParseObject('FilaSaida')
          ..set('fila_id', fila)
          ..set('motivo_saida', 'saida_voluntaria')
          ..set('observacao', 'Paciente optou por sair da fila')
          ..set('created_at', DateTime.now());

        final aclSaida = ParseACL();
        aclSaida.setPublicReadAccess(allowed: true);
        aclSaida.setPublicWriteAccess(allowed: true);
        filaSaida.setACL(aclSaida);

        final saidaResponse = await filaSaida.save();
        if (!saidaResponse.success) {
          debugPrint(
              "[Fila.leaveFila] Erro ao registrar saída: ${saidaResponse.error?.message}");
        } else {
          debugPrint(
              "[Fila.leaveFila] Saída registrada com sucesso na tabela FilaSaida");
        }
      } catch (e) {
        debugPrint("[Fila.leaveFila] Erro ao criar registro de saída: $e");
        // Continuamos mesmo com erro aqui
      }

      // 3. Atualizar status da fila
      fila.set('status', 'removido');
      fila.set('data_saida', DateTime.now());

      // Configurar ACL pública para a fila
      final aclFila = ParseACL();
      aclFila.setPublicReadAccess(allowed: true);
      aclFila.setPublicWriteAccess(allowed: true);
      fila.setACL(aclFila);

      final filaSaveResponse = await fila.save();
      if (!filaSaveResponse.success) {
        debugPrint(
            "[Fila.leaveFila] Erro ao salvar fila: ${filaSaveResponse.error?.message}");
        return false;
      }
      debugPrint("[Fila.leaveFila] Status da fila atualizado com sucesso");

      // 4. Atualizar o status de em_fila do usuário
      if (idPaciente.isNotEmpty) {
        try {
          await _atualizarStatusPaciente(idPaciente);
        } catch (e) {
          debugPrint(
              "[Fila.leaveFila] Erro ao atualizar status do paciente: $e");
          // Não interrompemos o fluxo principal se houver erro aqui
        }
      }

      // 5. Reajustar as posições na fila
      await _reajustarPosicoes(fila);

      return true;
    } catch (e) {
      debugPrint("[Fila.leaveFila] Erro ao sair da fila: $e");
      return false;
    }
  }

  // Método para atualizar o status em_fila do usuário/paciente
  static Future<void> _atualizarStatusPaciente(String idPaciente) async {
    debugPrint(
        "[Fila._atualizarStatusPaciente] Atualizando status do paciente $idPaciente");

    try {
      // Verificar na tabela Usuario primeiro
      final usuarioQuery = QueryBuilder<ParseObject>(ParseObject('Usuario'))
        ..whereEqualTo('deviceId', idPaciente);

      final usuarioResult = await usuarioQuery.query();
      if (usuarioResult.success &&
          usuarioResult.results != null &&
          usuarioResult.results!.isNotEmpty) {
        final usuario = usuarioResult.results!.first;
        usuario.set('em_fila', false);
        usuario.set('ultima_fila', DateTime.now());

        // Configurar ACL pública para o usuário
        final aclUsuario = ParseACL();
        aclUsuario.setPublicReadAccess(allowed: true);
        aclUsuario.setPublicWriteAccess(allowed: true);
        usuario.setACL(aclUsuario);

        final usuarioSaveResponse = await usuario.save();
        if (usuarioSaveResponse.success) {
          debugPrint(
              "[Fila._atualizarStatusPaciente] Status do usuário atualizado com sucesso");
        } else {
          debugPrint(
              "[Fila._atualizarStatusPaciente] Erro ao atualizar usuário: ${usuarioSaveResponse.error?.message}");
        }
      }

      // Verificar na tabela Paciente como fallback
      final pacienteQuery = QueryBuilder<ParseObject>(ParseObject('Paciente'))
        ..whereEqualTo('userId', idPaciente);

      final pacienteResult = await pacienteQuery.query();
      if (pacienteResult.success &&
          pacienteResult.results != null &&
          pacienteResult.results!.isNotEmpty) {
        final paciente = pacienteResult.results!.first;
        paciente.set('em_fila', false);
        paciente.set('ultima_fila', DateTime.now());

        // Configurar ACL pública para o paciente
        final aclPaciente = ParseACL();
        aclPaciente.setPublicReadAccess(allowed: true);
        aclPaciente.setPublicWriteAccess(allowed: true);
        paciente.setACL(aclPaciente);

        final pacienteSaveResponse = await paciente.save();
        if (pacienteSaveResponse.success) {
          debugPrint(
              "[Fila._atualizarStatusPaciente] Status do paciente atualizado com sucesso");
        } else {
          debugPrint(
              "[Fila._atualizarStatusPaciente] Erro ao atualizar paciente: ${pacienteSaveResponse.error?.message}");
        }
      }
    } catch (e) {
      debugPrint(
          "[Fila._atualizarStatusPaciente] Erro ao atualizar status: $e");
      // Não lançamos exceção aqui para não interromper o fluxo
    }
  }

  // Método para reajustar posições
  static Future<void> _reajustarPosicoes(ParseObject fila) async {
    try {
      final posicaoRemovida = fila.get<int>('posicao') ?? 0;
      final medico = fila.get<ParseObject>('medico');
      final consultorio = fila.get<ParseObject>('consultorio');

      if (medico != null && consultorio != null && posicaoRemovida > 0) {
        debugPrint("Reajustando posições. Posição removida: $posicaoRemovida");

        // Encontrar pacientes com posições maiores para reajustar
        final queryPacientes = QueryBuilder<ParseObject>(ParseObject('Fila'))
          ..whereEqualTo('medico', medico)
          ..whereEqualTo('consultorio', consultorio)
          ..whereEqualTo('status', 'aguardando')
          ..whereGreaterThan('posicao', posicaoRemovida)
          ..orderByAscending('posicao');

        final pacientesResponse = await queryPacientes.query();

        if (pacientesResponse.success && pacientesResponse.results != null) {
          for (var paciente in pacientesResponse.results!) {
            final posicaoAtual = paciente.get<int>('posicao') ?? 0;
            paciente.set('posicao', posicaoAtual - 1);

            // Configurar ACL pública
            final acl = ParseACL();
            acl.setPublicReadAccess(allowed: true);
            acl.setPublicWriteAccess(allowed: true);
            paciente.setACL(acl);

            await paciente.save();
            debugPrint(
                "Paciente reposicionado: de $posicaoAtual para ${posicaoAtual - 1}");
          }
        }
      }
    } catch (e) {
      debugPrint("Erro ao reajustar posições: $e");
    }
  }
}
