import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Classe de controle para gerenciar o estado da fila
class FilaStateController {
  // Métodos para salvar e recuperar o estado da fila entre sessões
  static Future<bool> salvarEstadoFila({
    required String filaId,
    required String medicoNome,
    required String especialidade,
    required int posicao,
    String status = 'aguardando',
  }) async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();

      await prefs.setString('filaId', filaId);
      await prefs.setString('medicoNome', medicoNome);
      await prefs.setString('especialidade', especialidade);
      await prefs.setInt('posicao', posicao);
      await prefs.setString('status', status);

      return true;
    } catch (e) {
      debugPrint('Erro ao salvar estado da fila: $e');
      return false;
    }
  }

  static Future<Map<String, dynamic>?> recuperarEstadoFila() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();

      final filaId = prefs.getString('filaId');
      final medicoNome = prefs.getString('medicoNome');
      final especialidade = prefs.getString('especialidade');
      final posicao = prefs.getInt('posicao');
      final status = prefs.getString('status') ?? 'aguardando';

      if (filaId == null ||
          medicoNome == null ||
          especialidade == null ||
          posicao == null) {
        return null;
      }

      return {
        'filaId': filaId,
        'medicoNome': medicoNome,
        'especialidade': especialidade,
        'posicao': posicao,
        'status': status,
      };
    } catch (e) {
      debugPrint('Erro ao recuperar estado da fila: $e');
      return null;
    }
  }

  static Future<bool> limparEstadoFila() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();

      await prefs.remove('filaId');
      await prefs.remove('medicoNome');
      await prefs.remove('especialidade');
      await prefs.remove('posicao');
      await prefs.remove('status');

      return true;
    } catch (e) {
      debugPrint('Erro ao limpar estado da fila: $e');
      return false;
    }
  }
}
