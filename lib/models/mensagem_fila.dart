class MensagemFila {
  final String id;
  final String titulo;
  final String texto;
  final DateTime dataEnvio;
  final String medicoId;
  final String prioridade;
  final String icone;

  MensagemFila({
    this.id = 'local_message',
    required this.titulo,
    required this.texto,
    required this.dataEnvio,
    this.medicoId = 'local',
    this.prioridade = 'media',
    this.icone = 'notification',
  });

  factory MensagemFila.fromParse(dynamic parseObject) {
    return MensagemFila(
      id: parseObject.objectId,
      titulo: parseObject.get<String>('titulo') ?? 'Aviso',
      texto: parseObject.get<String>('texto') ?? '',
      dataEnvio: parseObject.get<DateTime>('createdAt') ?? DateTime.now(),
      medicoId: parseObject.get<String>('medicoId') ?? '',
      prioridade: parseObject.get<String>('prioridade') ?? 'media',
      icone: parseObject.get<String>('icone') ?? 'notification',
    );
  }
}
