// lib/app.dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fila_app/controllers/login_controller.dart';
import 'package:fila_app/controllers/notification_controller.dart';
import 'package:fila_app/routes/app_routes.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

class MyApp extends StatelessWidget {
  final String initialRoute;
  final Map<String, dynamic>? initialFilaState;

  const MyApp({super.key, this.initialRoute = '/', this.initialFilaState});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'Saúde Sem Espera',
      debugShowCheckedModeBanner: false,
      locale: const Locale('pt', 'BR'),
      fallbackLocale: const Locale('pt', 'BR'),
      supportedLocales: const [
        Locale('pt', 'BR'),
      ],
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
        // Adicionar suporte otimizado para telas de 120Hz
        pageTransitionsTheme: const PageTransitionsTheme(
          builders: {
            TargetPlatform.android: ZoomPageTransitionsBuilder(
              allowEnterRouteSnapshotting:
                  false, // Melhora performance em telas de alta taxa de atualização
            ),
            TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
          },
        ),
      ),
      initialRoute: initialRoute,
      initialBinding: BindingsBuilder(() {
        Get.put(LoginController());
        Get.put(NotificationController());
      }),
      getPages: AppRoutes.getPages(
        initialRoute: initialRoute,
        initialFilaState: initialFilaState,
      ),
    );
  }
}
