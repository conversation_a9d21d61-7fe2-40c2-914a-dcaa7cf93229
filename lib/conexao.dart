// lib/conexao.dart
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:fila_app/services/throttling_service.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

class Conexao {
  static Future<void> initialize() async {
    await dotenv.load();

    final String? applicationId = dotenv.env['PARSE_APPLICATION_ID'];
    final String? clientKey = dotenv.env['PARSE_CLIENT_KEY'];
    final String? serverUrl = dotenv.env['PARSE_SERVER_URL'];

    if (applicationId == null || clientKey == null || serverUrl == null) {
      throw Exception('Missing Parse configuration');
    }

    // Extrair base do serverUrl para o LiveQuery
    // No formato https://parseapi.back4app.com/
    Uri serverUri = Uri.parse(serverUrl);
    String liveQueryUrl = "${serverUri.scheme}://${serverUri.host}";
    
    // Configurar porta específica para o LiveQuery (geralmente 1337)
    // Se estiver usando Back4App, use a porta padrão ou conforme documentação
    final liveQueryPort = dotenv.env['PARSE_LIVEQUERY_PORT'] ?? "1337";
    
    debugPrint('Inicializando Parse com LiveQuery: $liveQueryUrl:$liveQueryPort');

    await Parse().initialize(
      applicationId,
      serverUrl,
      clientKey: clientKey,
      debug: true,
      autoSendSessionId: true,
      liveQueryUrl: "$liveQueryUrl:$liveQueryPort"
      // Removendo a configuração incompatível do WebSocketSettings
    );

    // Verificar se a inicialização foi bem-sucedida
    final response = await Parse().healthCheck();
    if (!response.success) {
      debugPrint(
          'Erro na conexão com o Parse Server: ${response.error!.message}');
    } else {
      debugPrint('Parse Server conectado com sucesso!');
    }
  }

  /// Executa uma operação de consulta no Parse Server com throttling
  static Future<ParseResponse> throttledQuery(
    Future<ParseResponse> Function() queryFunction, {
    String operationName = 'query',
    bool isPriority = false,
  }) async {
    try {
      // Usar o serviço de throttling para controlar a taxa de requisições
      return await Get.find<ThrottlingService>().throttle(
        action: queryFunction,
        operationName: operationName,
        isPriority: isPriority,
      );
    } catch (e) {
      debugPrint('Erro na consulta throttled: $e');
      rethrow;
    }
  }

  /// Executa uma função Cloud com throttling
  static Future<ParseCloudFunction> throttledCloudFunction(
      String functionName) async {
    final cloudFunction = ParseCloudFunction(functionName);
    return cloudFunction;
  }

  /// Executa uma função Cloud com throttling
  static Future<ParseResponse> throttledExecuteCloudFunction(
    ParseCloudFunction cloudFunction, {
    Map<String, dynamic>? parameters,
    bool isPriority = false,
  }) async {
    try {
      // Usar o serviço de throttling para controlar a taxa de requisições
      return await Get.find<ThrottlingService>().throttle(
        action: () => cloudFunction.execute(parameters: parameters),
        operationName: 'cloud_${cloudFunction.functionName}',
        isPriority: isPriority,
      );
    } catch (e) {
      debugPrint('Erro na execução de função cloud throttled: $e');
      rethrow;
    }
  }

  /// Executa uma operação de salvamento com throttling
  static Future<ParseResponse> throttledSave(
    ParseObject object, {
    bool isPriority = false,
  }) async {
    try {
      // Usar o serviço de throttling para controlar a taxa de requisições
      return await Get.find<ThrottlingService>().throttle(
        action: () => object.save(),
        operationName: 'save_${object.parseClassName}',
        isPriority: isPriority,
      );
    } catch (e) {
      debugPrint('Erro no salvamento throttled: $e');
      rethrow;
    }
  }

  /// Executa uma operação de atualização com throttling
  static Future<ParseResponse> throttledUpdate(
    ParseObject object, {
    bool isPriority = false,
  }) async {
    try {
      // Usar o serviço de throttling para controlar a taxa de requisições
      return await Get.find<ThrottlingService>().throttle(
        action: () => object.update(),
        operationName: 'update_${object.parseClassName}',
        isPriority: isPriority,
      );
    } catch (e) {
      debugPrint('Erro na atualização throttled: $e');
      rethrow;
    }
  }

  /// Executa uma operação de exclusão com throttling
  static Future<ParseResponse> throttledDelete(
    ParseObject object, {
    bool isPriority = false,
  }) async {
    try {
      // Usar o serviço de throttling para controlar a taxa de requisições
      return await Get.find<ThrottlingService>().throttle(
        action: () => object.delete(),
        operationName: 'delete_${object.parseClassName}',
        isPriority: isPriority,
      );
    } catch (e) {
      debugPrint('Erro na exclusão throttled: $e');
      rethrow;
    }
  }

  /// Executa uma operação de busca por ID com throttling
  static Future<ParseResponse> throttledGetById(
    String className,
    String objectId, {
    bool isPriority = false,
    List<String> includes = const [],
  }) async {
    try {
      final query = QueryBuilder<ParseObject>(ParseObject(className))
        ..whereEqualTo('objectId', objectId);

      // Adicionar includes se fornecidos
      for (final include in includes) {
        query.includeObject([include]);
      }

      // Usar o serviço de throttling para controlar a taxa de requisições
      return await Get.find<ThrottlingService>().throttle(
        action: () => query.query(),
        operationName: 'get_by_id_$className',
        isPriority: isPriority,
      );
    } catch (e) {
      debugPrint('Erro na busca por ID throttled: $e');
      rethrow;
    }
  }

  /// Obter o número de requisições na fila
  static int get requestQueueSize {
    try {
      return Get.find<ThrottlingService>().queueSize;
    } catch (e) {
      return 0;
    }
  }

  /// Adiciona um listener para receber atualizações do tamanho da fila
  static void addQueueSizeListener(Function(int queueSize) listener) {
    try {
      Get.find<ThrottlingService>().addQueueSizeListener(listener);
    } catch (e) {
      debugPrint('Erro ao adicionar listener de tamanho de fila: $e');
    }
  }

  /// Remove um listener de atualizações do tamanho da fila
  static void removeQueueSizeListener(Function(int queueSize) listener) {
    try {
      Get.find<ThrottlingService>().removeQueueSizeListener(listener);
    } catch (e) {
      debugPrint('Erro ao remover listener de tamanho de fila: $e');
    }
  }
}
