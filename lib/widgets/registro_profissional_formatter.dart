import 'package:flutter/services.dart';

class RegistroProfissionalFormatter extends TextInputFormatter {
  final String tipo;
  
  RegistroProfissionalFormatter({this.tipo = 'CRM'});
  
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    final String text = newValue.text;
    
    // Remover caracteres não permitidos com base no tipo de registro
    String cleanedText;
    
    switch (tipo) {
      case 'CRM':
      case 'CRO':
      case 'CRP':
      case 'CRN':
      case 'CRBM':
      case 'CRTR':
        // Formato padrão: XXXXX-UF (onde UF é opcional)
        cleanedText = text.replaceAll(RegExp(r'[^0-9A-Z\-]'), '');
        if (cleanedText.length <= 5) {
          return TextEditingValue(
            text: cleanedText,
            selection: TextSelection.collapsed(offset: cleanedText.length),
          );
        } else if (cleanedText.length > 5 && !cleanedText.contains('-')) {
          // Adiciona hífen após os 5 primeiros dígitos
          String formatted = '${cleanedText.substring(0, 5)}-${cleanedText.substring(5)}';
          return TextEditingValue(
            text: formatted,
            selection: TextSelection.collapsed(offset: formatted.length),
          );
        }
        break;
        
      case 'CREFITO':
      case 'COREN':
      case 'CREFONO':
        // Formato para CREFITO: XXXXX-X/UF
        cleanedText = text.replaceAll(RegExp(r'[^0-9A-Z\-/]'), '');
        if (cleanedText.length <= 5) {
          return TextEditingValue(
            text: cleanedText,
            selection: TextSelection.collapsed(offset: cleanedText.length),
          );
        } else if (cleanedText.length > 5 && !cleanedText.contains('-')) {
          int dashPosition = 5;
          String formatted = '';
          
          if (cleanedText.length <= dashPosition) {
            formatted = cleanedText;
          } else {
            formatted = '${cleanedText.substring(0, dashPosition)}-${cleanedText.substring(dashPosition)}';
          }
          
          if (formatted.contains('-') && !formatted.contains('/') && formatted.indexOf('-') < formatted.length - 2) {
            int slashPosition = formatted.indexOf('-') + 2;
            if (formatted.length > slashPosition) {
              formatted = '${formatted.substring(0, slashPosition)}/${formatted.substring(slashPosition)}';
            }
          }
          
          return TextEditingValue(
            text: formatted,
            selection: TextSelection.collapsed(offset: formatted.length),
          );
        }
        break;
        
      default:
        // Formato padrão para outros tipos
        cleanedText = text.replaceAll(RegExp(r'[^0-9A-Z\-/]'), '');
    }
    
    return TextEditingValue(
      text: cleanedText,
      selection: TextSelection.collapsed(offset: cleanedText.length),
    );
  }
}
