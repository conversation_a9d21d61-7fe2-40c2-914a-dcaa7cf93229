import 'package:flutter/material.dart';

class FloatingActionButtonMenu extends StatefulWidget {
  final Function()? onAddPressed;
  final Function()? onAlertPressed;
  final Function()? onMessagePressed;
  final Function()? onRepairPressed;

  const FloatingActionButtonMenu({
    super.key,
    this.onAddPressed,
    this.onAlertPressed,
    this.onMessagePressed,
    this.onRepairPressed,
  });

  @override
  _FloatingActionButtonMenuState createState() =>
      _FloatingActionButtonMenuState();
}

class _FloatingActionButtonMenuState extends State<FloatingActionButtonMenu>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animateIcon;
  late Animation<double> _translateButton;
  final Curve _curve = Curves.easeOut;
  final double _fabHeight = 56.0;
  bool _isOpened = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    )..addListener(() {
        setState(() {});
      });

    _animateIcon = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_animationController);

    _translateButton = Tween<double>(
      begin: _fabHeight,
      end: -16.0,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Interval(
          0.0,
          0.75,
          curve: _curve,
        ),
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void animate() {
    if (!_isOpened) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
    _isOpened = !_isOpened;
  }

  Widget _buildAddButton() {
    return AnimatedOpacity(
      opacity: _isOpened ? 1.0 : 0.0,
      duration: const Duration(milliseconds: 300),
      child: Transform.translate(
        offset: Offset(0.0, _translateButton.value * 4),
        child: FloatingActionButton(
          heroTag: 'adicionar',
          onPressed: () {
            if (widget.onAddPressed != null) {
              widget.onAddPressed!();
            }
            animate();
          },
          backgroundColor: Colors.teal.shade600,
          tooltip: 'Adicionar paciente',
          child: const Icon(Icons.person_add, color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildRepairButton() {
    return AnimatedOpacity(
      opacity: _isOpened ? 1.0 : 0.0,
      duration: const Duration(milliseconds: 300),
      child: Transform.translate(
        offset: Offset(0.0, _translateButton.value * 3),
        child: FloatingActionButton.extended(
          heroTag: 'reparar',
          onPressed: () {
            if (widget.onRepairPressed != null) {
              widget.onRepairPressed!();
            }
            animate();
          },
          backgroundColor: Colors.amber.shade700,
          tooltip: 'Verificar e reparar problemas na fila',
          label: const Text('Verificar Fila',
              style:
                  TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
          icon: const Icon(Icons.healing, color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildMessageButton() {
    return AnimatedOpacity(
      opacity: _isOpened ? 1.0 : 0.0,
      duration: const Duration(milliseconds: 300),
      child: Transform.translate(
        offset: Offset(0.0, _translateButton.value * 2),
        child: FloatingActionButton.extended(
          heroTag: 'mensagem',
          onPressed: () {
            if (widget.onMessagePressed != null) {
              widget.onMessagePressed!();
            }
            animate();
          },
          backgroundColor: Colors.blue.shade700,
          tooltip: 'Enviar mensagem personalizada',
          label: const Text('Mensagem Personalizada',
              style:
                  TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
          icon: const Icon(Icons.edit_notifications, color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildAlertButton() {
    return AnimatedOpacity(
      opacity: _isOpened ? 1.0 : 0.0,
      duration: const Duration(milliseconds: 300),
      child: Transform.translate(
        offset: Offset(0.0, _translateButton.value),
        child: FloatingActionButton.extended(
          heroTag: 'alerta',
          onPressed: () {
            if (widget.onAlertPressed != null) {
              widget.onAlertPressed!();
            }
            animate();
          },
          backgroundColor: Colors.orange.shade700,
          tooltip: 'Enviar alerta para todos os pacientes na fila',
          label: const Text('Enviar Alerta',
              style:
                  TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
          icon: const Icon(Icons.notifications_active, color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildToggleButton() {
    return FloatingActionButton(
      heroTag: 'toggle',
      backgroundColor: Colors.teal.shade700,
      onPressed: animate,
      elevation: 8,
      tooltip: 'Abrir menu de ações',
      child: AnimatedIcon(
        icon: AnimatedIcons.menu_close,
        progress: _animateIcon,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: <Widget>[
        // O widget AnimatedOpacity faz aparecer/desaparecer os botões
        // O widget Transform.translate anima a posição dos botões
        _buildAddButton(),
        const SizedBox(height: 12),
        if (widget.onRepairPressed != null) _buildRepairButton(),
        if (widget.onRepairPressed != null) const SizedBox(height: 12),
        _buildMessageButton(),
        const SizedBox(height: 12),
        _buildAlertButton(),
        const SizedBox(height: 12),
        _buildToggleButton(),
      ],
    );
  }
}
