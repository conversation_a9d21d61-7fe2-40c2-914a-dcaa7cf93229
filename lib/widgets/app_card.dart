// In widgets/app_card.dart
import 'package:flutter/material.dart';
import 'package:fila_app/theme/constants.dart';

class AppCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final double? elevation;
  final Color? backgroundColor;

  const AppCard({
    super.key,
    required this.child,
    this.padding,
    this.elevation,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.only(bottom: AppSpacing.md),
      elevation: elevation ?? 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSpacing.borderRadius),
      ),
      color: backgroundColor ?? Colors.white,
      child: Padding(
        padding: padding ?? EdgeInsets.all(AppSpacing.cardPadding),
        child: child,
      ),
    );
  }
}