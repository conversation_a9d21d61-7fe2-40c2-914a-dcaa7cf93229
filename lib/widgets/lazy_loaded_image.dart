import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

class LazyLoadedImage extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  
  const LazyLoadedImage({
    super.key, 
    required this.imageUrl, 
    this.width, 
    this.height
  });
  
  @override
  Widget build(BuildContext context) {
    return CachedNetworkImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      placeholder: (context, url) => Container(
        color: Colors.grey[300],
        child: const Center(
          child: SizedBox(
            width: 20, 
            height: 20, 
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
        ),
      ),
      errorWidget: (context, url, error) => const Icon(Icons.error),
      fit: BoxFit.cover,
    );
  }
}