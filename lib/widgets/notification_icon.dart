import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/notification_controller.dart';

class NotificationIcon extends StatelessWidget {
  final String medicoId;
  final VoidCallback onTap;

  const NotificationIcon({
    super.key,
    required this.medicoId,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final notificationController = Get.find<NotificationController>();

    return Obx(() {
      final count = notificationController.notificacoes[medicoId] ?? 0;
      final isAnimating = notificationController.isAnimating.value;

      return Stack(
        clipBehavior: Clip.none,
        children: [
          TweenAnimationBuilder<double>(
            duration: const Duration(milliseconds: 300),
            tween: Tween(begin: 1.0, end: isAnimating ? 1.2 : 1.0),
            builder: (context, value, child) {
              return Transform.scale(
                scale: value,
                child: Transform.rotate(
                  angle: isAnimating ? 0.1 : 0.0,
                  child: Icon<PERSON>utton(
                    icon: const Icon(
                      Icons.notifications_outlined,
                      size: 28,
                    ),
                    onPressed: onTap,
                  ),
                ),
              );
            },
          ),
          if (count > 0)
            Positioned(
              right: 0,
              top: 0,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                constraints: const BoxConstraints(
                  minWidth: 16,
                  minHeight: 16,
                ),
                child: Text(
                  count > 99 ? '99+' : count.toString(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
        ],
      );
    });
  }
}