import 'package:flutter/material.dart';

class BackArrowWidget extends StatelessWidget {
  final Color? iconColor;
  final double iconSize;
  final VoidCallback? onPressed;
  final String? label;
  final EdgeInsetsGeometry padding;

  const BackArrowWidget({
    super.key,
    this.iconColor,
    this.iconSize = 24.0,
    this.onPressed,
    this.label,
    this.padding = const EdgeInsets.all(8.0),
  });

  @override
  Widget build(BuildContext context) {
    final VoidCallback effectiveOnPressed =
        onPressed ?? () => Navigator.of(context).pop();
    final Color effectiveColor = iconColor ?? Theme.of(context).primaryColor;

    return Padding(
      padding: padding,
      child: InkWell(
        onTap: effectiveOnPressed,
        borderRadius: BorderRadius.circular(50),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.arrow_back_ios,
              color: effectiveColor,
              size: iconSize,
              semanticLabel: 'Voltar',
            ),
            if (label != null)
              Padding(
                padding: const EdgeInsets.only(left: 4.0),
                child: Text(
                  label!,
                  style: TextStyle(
                    color: effectiveColor,
                    fontSize: iconSize * 0.75,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
