import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fila_app/services/auto_refresh_service.dart';

/// Widget de RefreshIndicator que respeita o limite de requisições
/// Implementa pull-to-refresh com throttling para evitar sobrecarga do servidor
class ThrottledRefreshIndicator extends StatelessWidget {
  /// ID único da tela (usado para rastrear atualizações)
  final String screenId;
  
  /// Função a ser executada quando o usuário puxa para atualizar
  final Future<void> Function() onRefresh;
  
  /// Conteúdo a ser exibido dentro do indicador de atualização
  final Widget child;
  
  /// Cor do indicador de atualização
  final Color? color;
  
  /// Cor de fundo do indicador de atualização
  final Color? backgroundColor;
  
  /// Deslocamento do indicador de atualização
  final double displacement;
  
  /// Tamanho do indicador de atualização
  final double? edgeOffset;
  
  /// Serviço de atualização automática
  final AutoRefreshService _autoRefreshService = AutoRefreshService();

  ThrottledRefreshIndicator({
    Key? key,
    required this.screenId,
    required this.onRefresh,
    required this.child,
    this.color,
    this.backgroundColor,
    this.displacement = 40.0,
    this.edgeOffset,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: () async {
        // Usar o serviço de atualização para respeitar o limite de requisições
        final success = await _autoRefreshService.manualRefresh(
          screenId: screenId,
          refreshAction: onRefresh,
        );
        
        // Se a atualização foi ignorada (muito frequente), mostrar feedback
        if (!success) {
          // Mostrar snackbar informando que a atualização foi muito frequente
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Aguarde um momento antes de atualizar novamente'),
              duration: const Duration(seconds: 2),
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      },
      color: color,
      backgroundColor: backgroundColor,
      displacement: displacement,
      edgeOffset: edgeOffset ?? 0.0,
      child: Stack(
        children: [
          // Conteúdo principal
          child,
          
          // Indicador de carregamento quando estiver atualizando automaticamente
          Obx(() {
            final isRefreshing = _autoRefreshService.getRefreshingState(screenId).value;
            
            if (isRefreshing) {
              return Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: Container(
                  height: 2,
                  child: LinearProgressIndicator(
                    backgroundColor: Colors.transparent,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      color ?? Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ),
              );
            } else {
              return const SizedBox.shrink();
            }
          }),
        ],
      ),
    );
  }
}

