// lib/widgets/user_data_dialog.dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fila_app/controllers/user_data_controller.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';

class UserDataDialog extends StatefulWidget {
  final UserDataController controller;
  final VoidCallback onComplete;
  final bool isEditing;

  const UserDataDialog({
    super.key,
    required this.controller,
    required this.onComplete,
    this.isEditing = false,
  });

  @override
  State<UserDataDialog> createState() => _UserDataDialogState();
}

class _UserDataDialogState extends State<UserDataDialog> {
  final _formKey = GlobalKey<FormState>();
  PhoneNumber _phoneNumber = PhoneNumber(isoCode: 'BR');
  late TextEditingController _phoneDisplayController;
  bool _isUIReady = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _phoneDisplayController = TextEditingController();

    // Use a microtask to delay the initialization of the phone number
    // to prevent UI lag during dialog opening
    Future.microtask(() {
      if (widget.controller.telefoneController.text.isNotEmpty) {
        _initializePhoneNumber();
      }
      if (mounted) {
        setState(() {
          _isUIReady = true;
        });
      }
    });
  }

  void _initializePhoneNumber() {
    try {
      final rawPhone = widget.controller.telefoneController.text.trim();

      if (rawPhone.isNotEmpty) {
        // Use compute function to move expensive parsing to a background isolate
        // if this continues to be slow
        PhoneNumber.getRegionInfoFromPhoneNumber(rawPhone, 'BR').then((value) {
          if (mounted) {
            setState(() {
              _phoneNumber = value;
              _phoneDisplayController.text =
                  rawPhone.replaceAll(RegExp(r'^\+\d+'), '');
            });
          }
        }).catchError((e) {
          // Fallback if parsing fails
          if (mounted) {
            setState(() {
              _phoneDisplayController.text = rawPhone;
            });
          }
        });
      }
    } catch (e) {
      debugPrint('Erro ao inicializar número de telefone: $e');
      if (mounted) {
        setState(() {
          _phoneDisplayController.text =
              widget.controller.telefoneController.text;
        });
      }
    }
  }

  @override
  void dispose() {
    _phoneDisplayController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bottomInset = MediaQuery.of(context).viewInsets.bottom;

    return WillPopScope(
      onWillPop: () async => widget.isEditing, // Prevents back on initial setup
      child: Dialog(
        insetPadding: const EdgeInsets.symmetric(horizontal: 16),
        backgroundColor: Colors.transparent,
        child: Container(
          constraints: const BoxConstraints(maxWidth: 400),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 10,
                spreadRadius: 1,
              ),
            ],
          ),
          child: Form(
            key: _formKey,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    widget.isEditing ? 'Editar Dados' : 'Bem-vindo!',
                    style: const TextStyle(
                      fontFamily: 'Georgia',
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.teal,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    widget.isEditing
                        ? 'Atualize suas informações:'
                        : 'Para uma melhor experiência, precisamos de algumas informações básicas:',
                    style: const TextStyle(
                      fontFamily: 'Georgia',
                      fontSize: 16,
                      color: Colors.black87,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  _buildNameField(),
                  const SizedBox(height: 20),
                  _isUIReady
                      ? _buildPhoneField()
                      : const SizedBox(
                          height: 60,
                          child: Center(
                            child: CircularProgressIndicator(
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.teal),
                              strokeWidth: 2,
                            ),
                          ),
                        ),
                  const SizedBox(height: 32),
                  _buildSubmitButton(),
                  if (bottomInset == 0)
                    Padding(
                      padding: const EdgeInsets.only(top: 16),
                      child: _buildInfoSection(),
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNameField() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: widget.controller.nomeController,
        decoration: InputDecoration(
          labelText: 'Nome completo',
          hintText: 'Digite seu nome completo',
          prefixIcon: const Icon(
            Icons.person_outline,
            color: Colors.teal,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.teal.shade200),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.teal.shade200),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Colors.teal, width: 2),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.red.shade300),
          ),
          filled: true,
          fillColor: Colors.white,
          contentPadding:
              const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
          helperText: 'Nome que aparecerá para o médico e secretária',
          helperStyle: TextStyle(fontSize: 12, color: Colors.grey.shade600),
        ),
        validator: widget.controller.validateNome,
        textCapitalization: TextCapitalization.words,
        autovalidateMode: AutovalidateMode.onUserInteraction,
        style: const TextStyle(
          fontFamily: 'Georgia',
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildPhoneField() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: Colors.teal.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 16, top: 12, bottom: 4),
            child: Text(
              'Telefone (WhatsApp)',
              style: TextStyle(
                fontSize: 12,
                color: Colors.teal,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          InternationalPhoneNumberInput(
            onInputChanged: (PhoneNumber number) {
              _phoneNumber = number;
              widget.controller.telefoneController.text =
                  number.phoneNumber ?? '';
            },
            selectorConfig: const SelectorConfig(
              selectorType: PhoneInputSelectorType.DROPDOWN,
              leadingPadding: 16,
              trailingSpace: false,
              showFlags: true,
            ),
            ignoreBlank: false,
            autoValidateMode: AutovalidateMode
                .disabled, // Change to disabled to improve performance
            initialValue: _phoneNumber,
            formatInput: true,
            inputDecoration: InputDecoration(
              hintText: '(11) 99999-9999',
              hintStyle: TextStyle(
                color: Colors.grey.shade400,
                fontFamily: 'Georgia',
                fontSize: 15,
              ),
              filled: true,
              fillColor: Colors.white,
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
              border: InputBorder.none,
              floatingLabelBehavior: FloatingLabelBehavior.never,
              prefixIcon: const Icon(Icons.phone_outlined, color: Colors.teal),
              helperText: 'Telefone para contato em caso de emergências',
              helperStyle: TextStyle(fontSize: 12, color: Colors.grey.shade600),
            ),
            textFieldController: _phoneDisplayController,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Por favor, digite seu telefone';
              }
              return widget.controller.validateTelefone(value);
            },
            spaceBetweenSelectorAndTextField: 0,
            textStyle: const TextStyle(
              fontFamily: 'Georgia',
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      height: 54,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          backgroundColor: Colors.teal,
          elevation: 2,
          shadowColor: Colors.teal.withOpacity(0.3),
        ),
        onPressed: _isLoading ? null : () => _handleSubmit(context),
        child: _isLoading
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  strokeWidth: 2,
                ),
              )
            : Text(
                widget.isEditing ? 'Atualizar' : 'Confirmar',
                style: const TextStyle(
                  fontFamily: 'Georgia',
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
      ),
    );
  }

  Widget _buildInfoSection() {
    return Column(
      children: [
        const Divider(color: Colors.teal),
        const SizedBox(height: 10),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(
              Icons.info_outline,
              size: 18,
              color: Colors.teal.shade700,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'Estas informações serão usadas para identificar você em todas as filas de atendimento.',
                style: TextStyle(
                  fontFamily: 'Georgia',
                  fontSize: 13,
                  color: Colors.grey.shade700,
                ),
              ),
            ),
          ],
        ),
        if (!widget.isEditing) ...[
          const SizedBox(height: 8),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                Icons.check_circle_outline,
                size: 18,
                color: Colors.green.shade700,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Você não precisará inserir esses dados novamente ao entrar em outras filas.',
                  style: TextStyle(
                    fontFamily: 'Georgia',
                    fontSize: 13,
                    color: Colors.grey.shade700,
                  ),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  void _handleSubmit(BuildContext context) {
    if (_formKey.currentState?.validate() ?? false) {
      setState(() => _isLoading = true);

      final completePhoneNumber =
          _phoneNumber.phoneNumber ?? widget.controller.telefoneController.text;

      widget.controller
          .saveUserData(
        widget.controller.nomeController.text.trim(),
        completePhoneNumber.trim(),
        isEditing: widget.isEditing,
      )
          .then((result) {
        if (result) {
          _salvarDadosUsuario(
            widget.controller.nomeController.text.trim(),
            completePhoneNumber.trim(),
          );
        } else {
          setState(() => _isLoading = false);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Erro ao salvar dados. Tente novamente.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }).catchError((error) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro: ${error.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      });
    }
  }

  Future<void> _salvarDadosUsuario(String nome, String telefone) async {
    try {
      final userData = await widget.controller.getUserData();
      if (userData == null || !userData.containsKey('userId')) {
        debugPrint(
            'Erro: não foi possível obter o userId para salvar no Usuario');
        setState(() => _isLoading = false);
        return;
      }

      final deviceId = userData['userId'];
      debugPrint(
          'Salvando/atualizando dados para usuário com deviceId: $deviceId');

      // Implementação direta de salvamento
      debugPrint('Usando implementação direta para salvar usuário');

      // 1. Tentar encontrar o usuário existente
      final query = QueryBuilder<ParseObject>(ParseObject('Usuario'))
        ..whereEqualTo('deviceId', deviceId);

      ParseResponse response = await query.query();
      ParseObject user;

      if (response.success &&
          response.results != null &&
          response.results!.isNotEmpty) {
        // 2a. Atualizar usuário existente
        debugPrint('Usuário existente encontrado, atualizando...');
        user = response.results!.first;
        user.set('nome', nome);
        user.set('telefone', telefone);
        user.set('ultima_atualizacao', DateTime.now());
        user.set('ultimoAcesso', DateTime.now());
      } else {
        // 2b. Criar novo usuário
        debugPrint('Criando novo usuário...');
        user = ParseObject('Usuario')
          ..set('deviceId', deviceId)
          ..set('nome', nome)
          ..set('telefone', telefone)
          ..set('data_cadastro', DateTime.now())
          ..set('ultima_atualizacao', DateTime.now())
          ..set('ultimoAcesso', DateTime.now())
          ..set('em_fila', false);

        // Configure public ACL
        final acl = ParseACL();
        acl.setPublicReadAccess(allowed: true);
        acl.setPublicWriteAccess(allowed: true);
        user.setACL(acl);
      }

      // 3. Salvar o objeto
      response = await user.save();

      if (response.success) {
        debugPrint('Usuário salvo com sucesso! ID: ${user.objectId}');
        
        // Garantir que os dados mostrem imediatamente na interface
        widget.controller.userDataExists.value = true;
        
        // Fechar o diálogo SEM chamar o callback onComplete
        // Isso é crucial para evitar redirecionamento
        if (mounted) {
          Navigator.of(context).pop();
        }
      } else {
        debugPrint('Erro ao salvar usuário: ${response.error?.message}');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro ao salvar dados: ${response.error?.message}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      debugPrint('Erro ao processar dados do usuário: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erro ao processar dados: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
