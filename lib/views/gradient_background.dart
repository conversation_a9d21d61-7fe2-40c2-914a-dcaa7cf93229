import 'package:flutter/material.dart';
import 'package:fila_app/utils/ui_utils.dart';

class GradientBackground extends StatelessWidget {
  final Widget child; // Propriedade para o widget filho
  final bool applyStatusBarStyles;

  const GradientBackground({
    super.key,
    required this.child,
    this.applyStatusBarStyles = true,
  }); // Use super.key for concise syntax

  @override
  Widget build(BuildContext context) {
    // Apply status bar styling if enabled
    if (applyStatusBarStyles) {
      UiUtils.setTranslucentStatusBar();
    }

    return Container(
      width: double.infinity, // Ocupa toda a largura disponível
      height: double.infinity, // Ocupa toda a altura disponível
      decoration: BoxDecoration(
        gradient: _buildGradient(), // Usa o gradiente fixo
      ),
      child: child, // Usa o child passado
    );
  }

  // Gradiente fixo
  LinearGradient _buildGradient() {
    return const LinearGradient(
      colors: [Color(0x90dcf8ec), Color(0xFF75CBBB)],
      stops: [0.01, 1.0],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    );
  }
}
