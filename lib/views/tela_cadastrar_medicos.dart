import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:fila_app/controllers/secretaria_controller.dart';
import 'package:fila_app/views/gradient_background.dart';
import 'package:fila_app/widgets/app_header.dart';
import 'package:fila_app/widgets/registro_profissional_formatter.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';

class CPFInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    final String text = newValue.text;

    // Remove todos os caracteres não numéricos
    String numericOnly = text.replaceAll(RegExp(r'[^0-9]'), '');

    if (numericOnly.length <= 3) {
      return TextEditingValue(
        text: numericOnly,
        selection: TextSelection.collapsed(offset: numericOnly.length),
      );
    } else if (numericOnly.length <= 6) {
      return TextEditingValue(
        text: '${numericOnly.substring(0, 3)}.${numericOnly.substring(3)}',
        selection: TextSelection.collapsed(offset: numericOnly.length + 1),
      );
    } else if (numericOnly.length <= 9) {
      return TextEditingValue(
        text:
            '${numericOnly.substring(0, 3)}.${numericOnly.substring(3, 6)}.${numericOnly.substring(6)}',
        selection: TextSelection.collapsed(offset: numericOnly.length + 2),
      );
    } else {
      return TextEditingValue(
        text:
            '${numericOnly.substring(0, 3)}.${numericOnly.substring(3, 6)}.${numericOnly.substring(6, 9)}-${numericOnly.substring(9, min(numericOnly.length, 11))}',
        selection:
            TextSelection.collapsed(offset: min(numericOnly.length, 11) + 3),
      );
    }
  }

  int min(int a, int b) => a < b ? a : b;
}

class TelaCadastrarMedicos extends StatefulWidget {
  const TelaCadastrarMedicos({super.key});

  @override
  State<TelaCadastrarMedicos> createState() => _TelaCadastrarMedicosState();
}

class _TelaCadastrarMedicosState extends State<TelaCadastrarMedicos> {
  final SecretariaController controller = Get.find<SecretariaController>();

  // Form controllers
  final _formKey = GlobalKey<FormState>();
  final nomeController = TextEditingController();
  final crmController = TextEditingController();
  final emailController = TextEditingController();
  final telefoneController = TextEditingController();
  final cpfController = TextEditingController();
  // Novo controlador para senha
  final senhaController = TextEditingController();

  // Form state
  final RxBool isLoading = false.obs;
  final RxBool medicoAdicionado = false.obs;
  final RxMap<String, dynamic> medicoCriado = <String, dynamic>{}.obs;
  final RxString cpfErrorText = ''.obs;
  final RxBool isValidatingCPF = false.obs;
  final RxBool mostrarSenha = false.obs; // Controlar visibilidade da senha

  // Área profissional e especialidade
  String _selectedAreaProfissional = 'Medicina';
  String _selectedEspecialidade = 'Selecione uma especialidade';
  String _registroLabel = 'CRM';
  
  // Pesquisa de satisfação
  bool _pesquisaSatisfacaoAtiva = false;
  List<String> _perguntasPersonalizadas = [];
  final TextEditingController _novaPerguntaController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Gerar a senha automaticamente ao iniciar a tela
    senhaController.text = _gerarSenhaAleatoria(10);
  }

  // Mapeamento das áreas profissionais para seus conselhos
  final Map<String, String> _conselhosPorArea = {
    'Medicina': 'CRM',
    'Odontologia': 'CRO',
    'Fisioterapia': 'CREFITO',
    'Psicologia': 'CRP',
    'Nutrição': 'CRN',
    'Biomedicina': 'CRBM',
    'Enfermagem': 'COREN',
    'Fonoaudiologia': 'CREFONO',
    'Técnico em Radiologia': 'CRTR',
  };

  // Lista das áreas profissionais disponíveis
  final List<String> _areasProfissionais = [
    'Medicina',
    'Odontologia',
    'Fisioterapia',
    'Psicologia',
    'Nutrição',
    'Biomedicina',
    'Enfermagem',
    'Fonoaudiologia',
    'Técnico em Radiologia',
  ];

  // Mapeamento das especialidades por área profissional
  final Map<String, List<String>> _especialidadesPorArea = {
    'Medicina': [
      'Selecione uma especialidade',
      'Alergologia',
      'Cardiologia',
      'Clínico Geral',
      'Dermatologia',
      'Endocrinologia',
      'Gastroenterologia',
      'Geriatria',
      'Ginecologia',
      'Hematologia',
      'Infectologia',
      'Nefrologia',
      'Neurologia',
      'Oftalmologia',
      'Oncologia',
      'Ortopedia',
      'Otorrinolaringologia',
      'Pediatria',
      'Pneumologia',
      'Psiquiatria',
      'Reumatologia',
      'Urologia',
      'Acupuntura',
      'Anestesiologia',
      'Angiologia',
      'Cirurgia Cardiovascular',
      'Cirurgia Geral',
      'Cirurgia Plástica',
      'Cirurgia Torácica',
      'Cirurgia Vascular',
      'Coloproctologia',
      'Emergência Médica',
      'Genética Médica',
      'Homeopatia',
      'Imunologia',
      'Medicina de Família e Comunidade',
      'Medicina do Trabalho',
      'Medicina Esportiva',
      'Medicina Física e Reabilitação',
      'Medicina Intensiva',
      'Medicina Legal',
      'Medicina Nuclear',
      'Nutrigenética',
      'Nutrigenômica',
      'Nutrologia',
      'Patologia',
      'Patologia Clínica',
      'Radiologia',
      'Radioterapia',
      'Terapia Intensiva',
      'Tricologia',
    ],
    'Odontologia': [
      'Selecione uma especialidade',
      'Dentista Geral',
      'Ortodontia',
      'Endodontia',
      'Periodontia',
      'Implantodontia',
      'Odontopediatria',
      'Cirurgia Bucomaxilofacial',
      'Prótese Dentária',
      'Estomatologia',
      'Radiologia Odontológica',
    ],
    'Fisioterapia': [
      'Selecione uma especialidade',
      'Fisioterapia Ortopédica',
      'Fisioterapia Neurológica',
      'Fisioterapia Respiratória',
      'Fisioterapia Pediátrica',
      'Fisioterapia Esportiva',
      'Fisioterapia Aquática',
      'Fisioterapia Geriátrica',
      'Fisioterapia Dermatofuncional',
      'Fisioterapia Cardiovascular',
      'Acupuntura',
    ],
    'Psicologia': [
      'Selecione uma especialidade',
      'Psicologia Clínica',
      'Psicologia Infantil',
      'Neuropsicologia',
      'Psicologia Organizacional',
      'Psicologia Esportiva',
      'Psicologia Educacional',
      'Psicologia Hospitalar',
      'Psicologia Jurídica',
      'Psicologia Social',
      'Avaliação Psicológica',
    ],
    'Nutrição': [
      'Selecione uma especialidade',
      'Nutrição Clínica',
      'Nutrição Esportiva',
      'Nutrição Materno-Infantil',
      'Nutrição Funcional',
      'Nutrição Hospitalar',
      'Nutrição Comportamental',
      'Nutrição Vegetariana/Vegana',
      'Consultoria em Alimentação',
      'Gastronomia Funcional',
    ],
    'Biomedicina': [
      'Selecione uma especialidade',
      'Análises Clínicas',
      'Imagenologia',
      'Acupuntura',
      'Citologia Oncótica',
      'Estética',
      'Reprodução Humana',
      'Genética',
      'Microbiologia',
      'Parasitologia',
      'Biologia Molecular',
    ],
    'Enfermagem': [
      'Selecione uma especialidade',
      'Enfermagem Clínica',
      'Enfermagem Obstétrica',
      'Enfermagem Pediátrica',
      'Enfermagem em Saúde Mental',
      'Enfermagem do Trabalho',
      'Enfermagem em Hemodiálise',
      'Enfermagem em Oncologia',
      'Enfermagem em UTI',
      'Enfermagem em Home Care',
      'Estomaterapia',
    ],
    'Fonoaudiologia': [
      'Selecione uma especialidade',
      'Audiologia',
      'Linguagem',
      'Motricidade Orofacial',
      'Voz',
      'Disfagia',
      'Fonoaudiologia Educacional',
      'Fonoaudiologia Neurofuncional',
      'Gerontologia',
      'Fonoaudiologia do Trabalho',
      'Neuropsicologia',
    ],
    'Técnico em Radiologia': [
      'Selecione uma especialidade',
      'Radiologia Geral',
      'Tomografia Computadorizada',
      'Ressonância Magnética',
      'Mamografia',
      'Densitometria Óssea',
      'Medicina Nuclear',
      'Hemodinâmica',
      'Radiologia Intervencionista',
      'Radioterapia',
      'Ultrassonografia',
    ],
  };

  @override
  void dispose() {
    nomeController.dispose();
    crmController.dispose();
    emailController.dispose();
    telefoneController.dispose();
    cpfController.dispose();
    senhaController.dispose();
    super.dispose();
  }

  Future<void> _cadastrarMedico() async {
    if (!_formKey.currentState!.validate()) {
      _mostrarMensagem('Por favor, corrija os erros no formulário',
          isError: true);
      return;
    }

    if (_selectedEspecialidade == 'Selecione uma especialidade') {
      _mostrarMensagem('Por favor, selecione uma especialidade', isError: true);
      return;
    }

    try {
      isLoading.value = true;

      // Verificar CPF válido
      final cpfNumerico = cpfController.text.replaceAll(RegExp(r'[^0-9]'), '');
      final cpfValido = await _verificarCPFValido(cpfNumerico);
      
      if (!cpfValido) {
        _mostrarMensagem(cpfErrorText.value, isError: true);
        return;
      }

      // Verificar se o médico já existe por CRM/registro profissional
      final queryRegistro = QueryBuilder<ParseObject>(ParseObject('Medico'))
        ..whereEqualTo('crm', crmController.text.trim());

      final registroResponse = await queryRegistro.query();

      if (registroResponse.success &&
          registroResponse.results != null &&
          registroResponse.results!.isNotEmpty) {
        _mostrarMensagem(
            'Já existe um profissional cadastrado com este $_registroLabel',
            isError: true);
        return;
      }

      // Criar usuário para o médico/profissional
      final username = emailController.text.trim();
      // Usar a senha atual no campo em vez de gerar uma nova
      final password = senhaController.text;
      final email = emailController.text.trim();

      final userMedico = ParseUser(username, password, email)
        ..set('tipo', 'medico') // Sempre usar 'medico' como tipo
        ..set('areaProfissional', _selectedAreaProfissional) // Armazenar a área profissional como campo separado
        ..set('nome', nomeController.text.trim()) // Armazenar o nome como campo separado
        ..set('dataCadastro', DateTime.now())
        ..set('ativo', true)
        ..set('roles', ['role:medico']); // Adicionar role de médico

      final userResponse =
          await userMedico.signUp(doNotSendInstallationID: true);

      if (!userResponse.success) {
        throw Exception(
            'Erro ao criar usuário: ${userResponse.error?.message}');
      }

      // Criar perfil do médico/profissional
      final medico = ParseObject('Medico')
        ..set('nome', nomeController.text.trim())
        ..set('crm', crmController.text.trim())
        ..set('especialidade', _selectedEspecialidade)
        ..set('cpf',
            int.parse(cpfController.text.replaceAll(RegExp(r'[^0-9]'), '')))
        ..set('email', email)
        ..set('telefone', telefoneController.text.trim())
        ..set('ativo', true)
        ..set('dataCadastro', DateTime.now())
        ..set('user_medico', userMedico.toPointer())
        // Dados da pesquisa de satisfação
        ..set('pesquisaSatisfacaoAtiva', _pesquisaSatisfacaoAtiva)
        ..set('perguntasPersonalizadas', _perguntasPersonalizadas);

      final medicoResponse = await medico.save();

      if (!medicoResponse.success) {
        // Se falhar, tentar excluir o usuário criado para evitar inconsistências
        await userMedico.destroy();
        throw Exception(
            'Erro ao criar perfil: ${medicoResponse.error?.message}');
      }

      // Vincular médico ao consultório
      final medicoConsultorio = ParseObject('MedicoConsultorio');

      final medicoRelation = medicoConsultorio
          .getRelation<ParseObject>('medicoConsultorio_medico');
      medicoRelation.add(medico);

      final consultorioRelation = medicoConsultorio
          .getRelation<ParseObject>('medicoConsultorio_consultorio');
      consultorioRelation.add(controller.currentHospital!);

      final medicoConsultorioResponse = await medicoConsultorio.save();

      if (!medicoConsultorioResponse.success) {
        throw Exception(
            'Erro ao vincular profissional ao consultório: ${medicoConsultorioResponse.error?.message}');
      }

      // Adicionar à lista atual de médicos no consultório
      if (controller.currentHospital != null) {
        final medicosVinculados =
            controller.currentHospital!.get<List>('medicos_vinculados') ?? [];
        medicosVinculados.add(medico);
        controller.currentHospital!
            .set('medicos_vinculados', medicosVinculados);
        await controller.currentHospital!.save();
      }

      // Atualizar lista de médicos no controller
      await controller.carregarMedicos();

      // Guardar dados para exibição
      medicoCriado.value = {
        'nome': nomeController.text.trim(),
        'crm': crmController.text.trim(),
        'registro': _registroLabel,
        'especialidade': _selectedEspecialidade,
        'areaProfissional': _selectedAreaProfissional,
        'email': email,
        'username': username,
        'password': password,
      };

      medicoAdicionado.value = true;

      // Limpar o formulário
      _formKey.currentState!.reset();
      nomeController.clear();
      crmController.clear();
      emailController.clear();
      telefoneController.clear();
      cpfController.clear();
      // Manter a senha atual para exibição
      // senhaController.text = password; - NÃO modificar a senha aqui
      setState(() {
        _selectedEspecialidade = 'Selecione uma especialidade';
        _pesquisaSatisfacaoAtiva = false;
        _perguntasPersonalizadas = [];
      });

      // Enviar e-mail ao médico com credenciais
      _enviarEmailCredenciais(email, username, password, nomeController.text.trim());
      
    } catch (e) {
      _mostrarMensagem('Erro ao cadastrar profissional: $e', isError: true);
    } finally {
      isLoading.value = false;
    }
  }

  String _gerarSenhaAleatoria(int tamanho) {
    // Definir os grupos de caracteres
    const maiusculas = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const minusculas = 'abcdefghijklmnopqrstuvwxyz';
    const numeros = '0123456789';
    const especiais = '!@#\$%^&*()';
    
    // Garantir caracteres de cada grupo para requisitos mínimos de segurança
    String senha = '';
    senha += maiusculas[_gerarNumeroAleatorio(maiusculas.length)];
    senha += minusculas[_gerarNumeroAleatorio(minusculas.length)];
    senha += numeros[_gerarNumeroAleatorio(numeros.length)];
    senha += especiais[_gerarNumeroAleatorio(especiais.length)];
    
    // Juntar todos os caracteres para compor o restante da senha
    const todosCaracteres = maiusculas + minusculas + numeros + especiais;
    
    // Completar a senha até o tamanho desejado
    for (int i = 4; i < tamanho; i++) {
      senha += todosCaracteres[_gerarNumeroAleatorio(todosCaracteres.length)];
    }
    
    // Embaralhar os caracteres da senha para não deixar sempre o mesmo padrão
    final List<String> caracteresEmbaralhados = senha.split('')..shuffle();
    return caracteresEmbaralhados.join('');
  }
  
  // Gerar números verdadeiramente aleatórios para a senha
  int _gerarNumeroAleatorio(int max) {
    // Usar uma combinação de DateTime e math.Random para aumentar aleatoriedade
    final seed = DateTime.now().microsecondsSinceEpoch * DateTime.now().millisecondsSinceEpoch;
    return (seed % max).abs();
  }

  void _mostrarMensagem(String mensagem, {bool isError = false}) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(mensagem),
        backgroundColor: isError ? Colors.red : Colors.teal,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  Future<bool> _verificarCPFValido(String cpf) async {
    cpf = cpf.replaceAll(RegExp(r'[^0-9]'), '');
    if (cpf.length != 11 || !_isValidCPF(cpf)) {
      cpfErrorText.value = 'CPF inválido';
      return false;
    }

    isValidatingCPF.value = true;
    cpfErrorText.value = '';

    try {
      // Em um cenário real, você faria uma verificação com uma API apropriada
      await Future.delayed(const Duration(milliseconds: 500));

      // Verificar se o CPF já existe no sistema
      final queryCPF = QueryBuilder<ParseObject>(ParseObject('Medico'))
        ..whereEqualTo('cpf', int.parse(cpf));

      final cpfResponse = await queryCPF.query();

      if (cpfResponse.success &&
          cpfResponse.results != null &&
          cpfResponse.results!.isNotEmpty) {
        cpfErrorText.value = 'Este CPF já está cadastrado no sistema';
        return false;
      }

      cpfErrorText.value = '';
      return true;
    } catch (e) {
      cpfErrorText.value = 'Erro ao validar CPF: ${e.toString()}';
      return false;
    } finally {
      isValidatingCPF.value = false;
    }
  }

  bool _isValidCPF(String cpf) {
    // Implementação da validação de CPF
    if (cpf.isEmpty) return false;

    // Remover caracteres não numéricos
    cpf = cpf.replaceAll(RegExp(r'[^0-9]'), '');

    // Verificar se tem 11 dígitos
    if (cpf.length != 11) return false;

    // Verificar se todos os dígitos são iguais
    if (RegExp(r'^(\d)\1*$').hasMatch(cpf)) return false;

    // Calcular o primeiro dígito verificador
    int soma = 0;
    for (int i = 0; i < 9; i++) {
      soma += int.parse(cpf[i]) * (10 - i);
    }
    int digito1 = 11 - (soma % 11);
    if (digito1 > 9) digito1 = 0;

    // Calcular o segundo dígito verificador
    soma = 0;
    for (int i = 0; i < 10; i++) {
      soma += int.parse(cpf[i]) * (11 - i);
    }
    int digito2 = 11 - (soma % 11);
    if (digito2 > 9) digito2 = 0;

    // Verificar se os dígitos calculados são iguais aos informados
    return (digito1 == int.parse(cpf[9]) && digito2 == int.parse(cpf[10]));
  }

  Future<void> _enviarEmailCredenciais(String email, String username, String password, String nomeMedico) async {
    try {
      // Criar objeto para enviar o email com as credenciais
      final emailParams = {
        'destinatario': email,
        'assunto': 'Suas credenciais de acesso ao sistema',
        'corpo': '''
Olá $nomeMedico,

Seu cadastro no sistema foi realizado com sucesso. Abaixo estão suas credenciais de acesso:

Usuário: $username
Senha: $password

Recomendamos que você altere sua senha no primeiro acesso.

Atenciosamente,
Equipe do App
'''
      };

      // Chamar função do Parse Server para enviar email
      final cloudFunction = ParseCloudFunction('enviarEmailCredenciais');
      final result = await cloudFunction.execute(parameters: emailParams);

      if (result.success) {
        print('Email com credenciais enviado com sucesso');
      } else {
        print('Falha ao enviar email: ${result.error?.message}');
        // Não bloqueamos o fluxo se falhar o envio de email
      }
    } catch (e) {
      print('Erro ao enviar email: $e');
      // Não bloqueamos o fluxo se falhar o envio de email
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientBackground(
        child: SafeArea(
          child: Column(
            children: [
              AppHeader(
                title: 'Cadastrar Profissionais',
                centerTitle: true,
                onBackPressed: () => Get.back(),
              ),
              Expanded(
                child: Obx(() => medicoAdicionado.value
                    ? _buildSucessoAdicionado()
                    : _buildFormularioCadastro()),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFormularioCadastro() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Título do formulário
            Container(
              margin: const EdgeInsets.only(bottom: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Novo Profissional',
                    style: TextStyle(
                      fontFamily: 'Georgia',
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.teal.shade800,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Preencha os dados para cadastrar um novo profissional no consultório',
                    style: TextStyle(
                      fontFamily: 'Georgia',
                      fontSize: 14,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ],
              ),
            ),

            // Informações do consultório
            if (controller.currentHospital != null)
              Container(
                margin: const EdgeInsets.only(bottom: 16),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.teal.withAlpha(25),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.local_hospital, color: Colors.teal.shade700),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Consultório: ${controller.currentHospital!.get<String>('nome') ?? 'Nome não disponível'}',
                        style: TextStyle(
                          fontFamily: 'Georgia',
                          fontWeight: FontWeight.bold,
                          color: Colors.teal.shade700,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

            // Área Profissional
            _buildAreaProfissionalDropdown(),
            const SizedBox(height: 16),

            // Campo Nome
            TextFormField(
              controller: nomeController,
              decoration: InputDecoration(
                labelText: 'Nome completo',
                hintText: 'Digite o nome completo do profissional',
                prefixIcon: const Icon(Icons.person),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: Colors.white,
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Por favor, digite o nome do profissional';
                }
                if (value.trim().length < 5) {
                  return 'O nome deve ter pelo menos 5 caracteres';
                }
                return null;
              },
              textCapitalization: TextCapitalization.words,
            ),

            const SizedBox(height: 16),

            // Campo CPF
            TextFormField(
              controller: cpfController,
              decoration: InputDecoration(
                labelText: 'CPF',
                hintText: 'Digite o CPF do profissional',
                prefixIcon: const Icon(Icons.badge),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: Colors.white,
                errorText:
                    cpfErrorText.value.isEmpty ? null : cpfErrorText.value,
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Por favor, digite o CPF do profissional';
                }

                // Validar formato do CPF
                final numeros = value.replaceAll(RegExp(r'[^0-9]'), '');
                if (numeros.length != 11) {
                  return 'CPF deve conter 11 dígitos';
                }

                if (!_isValidCPF(numeros)) {
                  return 'CPF inválido';
                }

                if (cpfErrorText.value.isNotEmpty) {
                  return cpfErrorText.value;
                }

                return null;
              },
              keyboardType: TextInputType.number,
              inputFormatters: [
                CPFInputFormatter(),
                LengthLimitingTextInputFormatter(14),
              ],
              onChanged: (value) {
                cpfErrorText.value = '';

                String cpfNumerico = value.replaceAll(RegExp(r'[^0-9]'), '');
                if (cpfNumerico.length == 11) {
                  _verificarCPFValido(cpfNumerico);
                }
              },
            ),

            Obx(() => isValidatingCPF.value
                ? Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Row(
                      children: const [
                        SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                        SizedBox(width: 10),
                        Text(
                          'Validando CPF...',
                          style: TextStyle(fontFamily: 'Georgia', fontSize: 14),
                        ),
                      ],
                    ),
                  )
                : const SizedBox.shrink()),

            const SizedBox(height: 16),

            // Campo Registro Profissional (CRM, CRO, etc.)
            TextFormField(
              controller: crmController,
              decoration: InputDecoration(
                labelText: _registroLabel,
                hintText: 'Digite o número do $_registroLabel',
                prefixIcon: const Icon(Icons.assignment_ind),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: Colors.white,
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Por favor, digite o $_registroLabel do profissional';
                }
                return null;
              },
              inputFormatters: [
                RegistroProfissionalFormatter(tipo: _registroLabel),
              ],
            ),

            const SizedBox(height: 16),

            // Campo Especialidade
            _buildEspecialidadeDropdown(),

            const SizedBox(height: 16),

            // Campo Email
            TextFormField(
              controller: emailController,
              decoration: InputDecoration(
                labelText: 'Email',
                hintText: 'Digite o email do profissional',
                prefixIcon: const Icon(Icons.email),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: Colors.white,
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Por favor, digite o email do profissional';
                }
                if (!GetUtils.isEmail(value.trim())) {
                  return 'Digite um email válido';
                }
                return null;
              },
              keyboardType: TextInputType.emailAddress,
            ),

            const SizedBox(height: 16),
            
            // Campo para exibir a senha gerada
            Obx(() {
              // Verificar se temos uma senha gerada disponível
              if (medicoAdicionado.value && senhaController.text.isEmpty) {
                senhaController.text = medicoCriado['password'] ?? '';
              }
              
              return TextFormField(
                controller: senhaController,
                decoration: InputDecoration(
                  labelText: 'Senha Gerada',
                  // Não usar hintText para evitar confusão com o valor real
                  prefixIcon: const Icon(Icons.lock_open), // Mudando para lock_open para indicar que pode ser visualizado
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  filled: true,
                  fillColor: Colors.white,
                  suffixIcon: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Botão para gerar nova senha
                      IconButton(
                        icon: Icon(
                          Icons.refresh,
                          color: Colors.teal,
                        ),
                        tooltip: 'Gerar nova senha',
                        onPressed: () {
                          senhaController.text = _gerarSenhaAleatoria(10);
                        },
                      ),
                      // Botão para alternar visibilidade
                      IconButton(
                        icon: Icon(
                          mostrarSenha.value ? Icons.visibility : Icons.visibility_off,
                          color: mostrarSenha.value ? Colors.teal : Colors.grey,
                        ),
                        tooltip: mostrarSenha.value ? 'Ocultar senha' : 'Mostrar senha',
                        onPressed: () {
                          mostrarSenha.value = !mostrarSenha.value;
                        },
                      ),
                    ],
                  ),
                ),
                obscureText: !mostrarSenha.value,
                readOnly: true,
                style: TextStyle(
                  fontWeight: mostrarSenha.value ? FontWeight.bold : FontWeight.normal,
                ),
              );
            }),

            const SizedBox(height: 16),

            // Campo Telefone
            TextFormField(
              controller: telefoneController,
              decoration: InputDecoration(
                labelText: 'Telefone',
                hintText: 'Digite o telefone do profissional',
                prefixIcon: const Icon(Icons.phone),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: Colors.white,
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Por favor, digite o telefone do profissional';
                }
                final cleanPhone = value.replaceAll(RegExp(r'[^0-9]'), '');
                if (cleanPhone.length < 10 || cleanPhone.length > 11) {
                  return 'Digite um número de telefone válido';
                }
                return null;
              },
              keyboardType: TextInputType.phone,
            ),

            const SizedBox(height: 24),
            
            // Seção de Pesquisa de Satisfação
            _buildPesquisaSatisfacaoSection(),

            const SizedBox(height: 24),

            // Botão de cadastro
            Center(
              child: Obx(
                () => isLoading.value
                    ? const CircularProgressIndicator()
                    : SizedBox(
                        width: double.infinity,
                        height: 48,
                        child: ElevatedButton(
                          onPressed: _cadastrarMedico,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.teal,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text(
                            'Cadastrar Profissional',
                            style: TextStyle(
                              fontFamily: 'Georgia',
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAreaProfissionalDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Área Profissional',
          style: TextStyle(
            fontFamily: 'Georgia',
            fontSize: 16,
            fontWeight: FontWeight.normal,
            color: Colors.black87,
          ),
        ),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 5),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.shade400),
          ),
          child: DropdownButton<String>(
            value: _selectedAreaProfissional,
            isExpanded: true,
            underline: Container(),
            style: const TextStyle(
              fontFamily: 'Georgia',
              fontSize: 16,
              color: Colors.black87,
            ),
            items: _areasProfissionais.map((String value) {
              return DropdownMenuItem<String>(
                value: value,
                child: Text(value),
              );
            }).toList(),
            onChanged: (String? newValue) {
              if (newValue != null) {
                setState(() {
                  _selectedAreaProfissional = newValue;
                  _selectedEspecialidade = 'Selecione uma especialidade';
                  _registroLabel = _conselhosPorArea[newValue] ?? 'CRM';
                });
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildEspecialidadeDropdown() {
    final especialidades =
        _especialidadesPorArea[_selectedAreaProfissional] ?? [];

    return DropdownButtonFormField<String>(
      decoration: const InputDecoration(
        labelText: 'Especialidade',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.medical_services_outlined),
      ),
      value: especialidades.contains(_selectedEspecialidade)
          ? _selectedEspecialidade
          : 'Selecione uma especialidade',
      onChanged: (String? newValue) {
        if (newValue != null) {
          setState(() {
            _selectedEspecialidade = newValue;
          });
        }
      },
      validator: (value) {
        if (value == null || value == 'Selecione uma especialidade') {
          return 'Por favor, selecione uma especialidade';
        }
        return null;
      },
      items: especialidades.map<DropdownMenuItem<String>>((String value) {
        return DropdownMenuItem<String>(
          value: value,
          child: Text(value),
        );
      }).toList(),
    );
  }

  Widget _buildSucessoAdicionado() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.check_circle_outline,
            color: Colors.teal,
            size: 80,
          ),
          const SizedBox(height: 20),
          Text(
            'Profissional adicionado com sucesso!',
            style: TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: Colors.teal.withAlpha(200),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 10),
          Text(
            'Nome: ${medicoCriado['nome'] ?? ''}',
            style: const TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 5),
          Text(
            'Especialidade: ${medicoCriado['especialidade'] ?? ''}',
            style: const TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 5),
          Text(
            '$_registroLabel: ${medicoCriado['crm'] ?? ''}',
            style: const TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 30),
          ElevatedButton.icon(
            onPressed: () {
              setState(() {
                nomeController.clear();
                crmController.clear();
                emailController.clear();
                telefoneController.clear();
                cpfController.clear();
                medicoAdicionado.value = false;
                cpfErrorText.value = '';
              });
            },
            icon: const Icon(Icons.add),
            label: const Text('Adicionar Outro Profissional'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
          const SizedBox(height: 15),
          TextButton.icon(
            onPressed: () {
              Get.back();
            },
            icon: const Icon(Icons.arrow_back),
            label: const Text('Voltar para Gerenciamento'),
            style: TextButton.styleFrom(
              foregroundColor: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPesquisaSatisfacaoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Pesquisa de Satisfação',
          style: TextStyle(
            fontFamily: 'Georgia',
            fontSize: 16,
            fontWeight: FontWeight.normal,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Switch(
              value: _pesquisaSatisfacaoAtiva,
              onChanged: (value) {
                setState(() {
                  _pesquisaSatisfacaoAtiva = value;
                });
              },
            ),
            const Text(
              'Ativar pesquisa de satisfação',
              style: TextStyle(
                fontFamily: 'Georgia',
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        if (_pesquisaSatisfacaoAtiva) ...[
          const SizedBox(height: 16),
          const Text(
            'Perguntas Personalizadas',
            style: TextStyle(
              fontFamily: 'Georgia',
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _perguntasPersonalizadas.length,
            itemBuilder: (context, index) {
              return ListTile(
                title: Text(_perguntasPersonalizadas[index]),
                trailing: IconButton(
                  icon: const Icon(Icons.delete, color: Colors.red),
                  onPressed: () {
                    setState(() {
                      _perguntasPersonalizadas.removeAt(index);
                    });
                  },
                ),
              );
            },
          ),
          const SizedBox(height: 8),
          TextFormField(
            controller: _novaPerguntaController,
            decoration: InputDecoration(
              labelText: 'Nova Pergunta',
              hintText: 'Digite uma nova pergunta',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          ElevatedButton.icon(
            onPressed: () {
              if (_novaPerguntaController.text.trim().isNotEmpty) {
                setState(() {
                  _perguntasPersonalizadas
                      .add(_novaPerguntaController.text.trim());
                  _novaPerguntaController.clear();
                });
              }
            },
            icon: const Icon(Icons.add),
            label: const Text('Adicionar Pergunta'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ],
    );
  }
}