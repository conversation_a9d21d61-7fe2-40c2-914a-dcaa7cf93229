import 'package:fila_app/theme/theme.dart';
import 'package:fila_app/widgets/app_card.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:fila_app/controllers/fila_atendimento_controller.dart';
import 'package:fila_app/views/gradient_background.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'package:fila_app/services/connectivity_service.dart';

class TelaFilaAtendimento extends GetView<FilaAtendimentoController> {
  const TelaFilaAtendimento({super.key});

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        // Mostrar diálogo de confirmação antes de sair
        return await _confirmarSaida(context);
      },
      child: DefaultTabController(
        length: 2,
        child: Scaffold(
          appBar: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            leading: Icon<PERSON>utton(
              icon: const Icon(Icons.arrow_back, color: Colors.teal),
              onPressed: () => _confirmarSaida(context),
            ),
            title: const Text(
              'Gerenciamento de Fila',
              style: TextStyle(
                color: Colors.teal,
                fontFamily: 'Georgia',
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          body: GradientBackground(
            child: SafeArea(
              child: Column(
                children: [
                  // Cabeçalho centralizado
                  Container(
                    padding: const EdgeInsets.symmetric(
                        vertical: 16, horizontal: 16),
                    alignment: Alignment.center,
                    child: const Text(
                      'Filas ativas',
                      style: TextStyle(
                        fontFamily: 'Georgia',
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  const SizedBox(height: 20),
                  _buildMedicoInfo(),
                  const SizedBox(height: 10),
                  _buildDragInstructions(),
                  const SizedBox(height: 20),
                  _buildTabBar(),
                  Expanded(
                    child: TabBarView(
                      children: [
                        _buildFilaAguardando(),
                        _buildFilaEmAtendimento(),
                      ],
                    ),
                  ),
                  _buildBottomButtons(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Função para confirmar a saída da tela de atendimento
  Future<bool> _confirmarSaida(BuildContext context) async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(Icons.warning_amber_rounded, color: Colors.amber[700]),
              const SizedBox(width: 10),
              const Text(
                'Abandonar controle da fila?',
                style: TextStyle(
                  fontFamily: 'Georgia',
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          content: const Text(
            'Se você sair agora, todas as alterações não salvas serão perdidas. Pacientes em atendimento continuarão nesse estado até que você retorne e finalize ou transferir para outro profissional.',
            style: TextStyle(
              fontFamily: 'Georgia',
            ),
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text(
                'CANCELAR',
                style: TextStyle(
                  fontFamily: 'Georgia',
                  color: Colors.grey,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                // Adicione aqui qualquer lógica de limpeza necessária
                Navigator.of(context).pop(true);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'SAIR MESMO ASSIM',
                style: TextStyle(
                  fontFamily: 'Georgia',
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        );
      },
    );

    // Se o usuário confirmou a saída, retornar à tela anterior
    if (result == true) {
      Get.back();
    }

    // Retornar o resultado para o WillPopScope
    return result ?? false;
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      height: 45,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TabBar(
        tabs: [
          _buildTab("Aguardando"),
          _buildTab("Em Atendimento"),
        ],
        indicator: BoxDecoration(
          borderRadius: BorderRadius.circular(25),
          color: Colors.teal.withOpacity(0.2),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        labelPadding: EdgeInsets.zero,
        labelColor: Colors.teal,
        unselectedLabelColor: Colors.grey,
        labelStyle: const TextStyle(
          fontFamily: 'Georgia',
          fontWeight: FontWeight.bold,
        ),
        dividerColor: Colors.transparent,
      ),
    );
  }

  Widget _buildTab(String text) {
    return Tab(
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 14,
          fontFamily: 'Georgia',
        ),
      ),
    );
  }

  Widget _buildFilaAguardando() {
    return Obx(() {
      if (controller.isLoading.value) {
        return const Center(child: CircularProgressIndicator());
      }

      final pacientesAguardando = controller.pacientesNaFila
          .where((p) => p.get<String>('status') == 'aguardando')
          .toList();

      if (pacientesAguardando.isEmpty) {
        return const Center(
          child: Text(
            'Nenhum paciente aguardando',
            style: TextStyle(
              fontFamily: 'Georgia',
              fontSize: 18,
              color: Colors.grey,
            ),
          ),
        );
      }

      return ReorderableListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: pacientesAguardando.length,
        onReorder: controller.atualizarPosicoes,
        itemBuilder: (context, index) {
          return _buildPacienteCard(context, pacientesAguardando[index], index);
        },
      );
    });
  }

  Widget _buildFilaEmAtendimento() {
    return Obx(() {
      if (controller.isLoading.value) {
        return const Center(child: CircularProgressIndicator());
      }

      final pacientesEmAtendimento = controller.pacientesNaFila
          .where((p) => p.get<String>('status') == 'em_atendimento')
          .toList();

      if (pacientesEmAtendimento.isEmpty) {
        return const Center(
          child: Text(
            'Nenhum paciente em atendimento',
            style: TextStyle(
              fontFamily: 'Georgia',
              fontSize: 18,
              color: Colors.grey,
            ),
          ),
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: pacientesEmAtendimento.length,
        itemBuilder: (context, index) {
          return _buildPacienteEmAtendimentoCard(pacientesEmAtendimento[index]);
        },
      );
    });
  }

  Widget _buildMedicoInfo() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            'Dr. ${controller.medicoData['nome']}',
            style: const TextStyle(
              fontFamily: 'Georgia',
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            controller.medicoData['especialidade'] ?? '',
            style: const TextStyle(
              fontFamily: 'Georgia',
              fontSize: 18,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDragInstructions() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: Colors.teal.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.drag_indicator, color: Colors.teal.withOpacity(0.7)),
          const SizedBox(width: 8),
          const Text(
            'Arraste os pacientes para reordenar a fila',
            style: TextStyle(
              fontFamily: 'Georgia',
              fontSize: 14,
              fontStyle: FontStyle.italic,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPacienteCard(
      BuildContext context, ParseObject paciente, int index) {
    final nome = paciente.get<String>('nome') ?? 'Nome não informado';
    final telefone = paciente.get<String>('telefone');

    return AppCard(
      child: Column(
        children: [
          ListTile(
            contentPadding: EdgeInsets.zero,
            leading: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Center(
                child: Text(
                  '${index + 1}',
                  style: TextStyle(
                    fontFamily: AppTheme.fontFamily,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ),
            ),
            title: Text(
              nome,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.play_arrow),
                  onPressed: () => controller.iniciarAtendimento(paciente),
                  color: Colors.green,
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => _confirmarRemocao(paciente, index),
                  color: Colors.red,
                ),
                if (telefone != null && telefone.isNotEmpty)
                  IconButton(
                    icon: const FaIcon(
                      FontAwesomeIcons.whatsapp,
                      color: Colors.green,
                    ),
                    onPressed: () => controller.abrirWhatsApp(telefone),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPacienteEmAtendimentoCard(ParseObject paciente) {
    final nome = paciente.get<String>('nome') ?? 'Nome não informado';
    final telefone = paciente.get<String>('telefone');
    final inicioAtendimento = paciente.get<DateTime>('data_inicio_atendimento');
    final agora = DateTime.now();
    final duracaoAtendimento = inicioAtendimento != null
        ? agora.difference(inicioAtendimento)
        : const Duration();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                ElevatedButton.icon(
                  onPressed: () => controller.finalizarAtendimento(paciente),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.teal,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  icon: const Icon(Icons.check_circle),
                  label: const Text(
                    'Finalizar Atendimento',
                    style: TextStyle(
                      fontFamily: 'Georgia',
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                const Divider(height: 1),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: Colors.teal.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(25),
                      ),
                      child: const Center(
                        child: Icon(
                          Icons.person_outline,
                          color: Colors.teal,
                          size: 24,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            nome,
                            style: const TextStyle(
                              fontFamily: 'Georgia',
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.teal.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(
                                  Icons.timer,
                                  size: 16,
                                  color: Colors.teal,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'Em atendimento há ${duracaoAtendimento.inMinutes} minutos',
                                  style: const TextStyle(
                                    fontFamily: 'Georgia',
                                    fontSize: 14,
                                    color: Colors.teal,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          if (telefone != null && telefone.isNotEmpty)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
              child: TextButton.icon(
                onPressed: () => controller.abrirWhatsApp(telefone),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.green,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                    side: const BorderSide(color: Colors.green),
                  ),
                ),
                icon: const FaIcon(
                  FontAwesomeIcons.whatsapp,
                  size: 20,
                ),
                label: const Text(
                  'Contatar via WhatsApp',
                  style: TextStyle(
                    fontFamily: 'Georgia',
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  void _confirmarRemocao(ParseObject paciente, int index) {
    Get.dialog(
      AlertDialog(
        title: const Text(
          'Confirmar Remoção',
          style: TextStyle(fontFamily: 'Georgia'),
        ),
        content: Text(
          'Deseja realmente remover ${paciente.get<String>('nome')} da fila?',
          style: const TextStyle(fontFamily: 'Georgia'),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text(
              'Cancelar',
              style: TextStyle(color: Colors.grey),
            ),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              final connectivity = Get.find<ConnectivityService>();
              if (!connectivity.isConnected.value) {
                Get.snackbar('Erro',
                    'Você está offline. A operação será sincronizada posteriormente.');
                // Opcional: chamar método para enfileirar a operação offline
              } else {
                controller.removerPaciente(paciente, index);
              }
            },
            child: const Text(
              'Confirmar',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomButtons() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ElevatedButton.icon(
            icon: const Icon(Icons.history),
            label: const Text('Histórico'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(
                horizontal: 20,
                vertical: 12,
              ),
            ),
            onPressed: () => _mostrarHistoricoAtendimentos(),
          ),
        ],
      ),
    );
  }

  void _mostrarHistoricoAtendimentos() {
    Get.dialog(
      Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [
                Color.fromARGB(255, 217, 245, 243),
                Color.fromARGB(255, 156, 236, 217),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
            borderRadius: BorderRadius.circular(20),
          ),
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Pacientes Atendidos Hoje',
                style: TextStyle(
                  fontFamily: 'Georgia',
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              Container(
                constraints: BoxConstraints(
                  maxHeight: Get.height * 0.4,
                ),
                child: Obx(() {
                  if (controller.pacientesAtendidos.isEmpty) {
                    return const Center(
                      child: Text(
                        'Nenhum paciente atendido hoje',
                        style: TextStyle(
                          fontFamily: 'Georgia',
                          fontSize: 16,
                        ),
                      ),
                    );
                  }

                  return ListView.builder(
                    shrinkWrap: true,
                    itemCount: controller.pacientesAtendidos.length,
                    itemBuilder: (context, index) {
                      final paciente = controller.pacientesAtendidos[index];
                      return _buildPacienteAtendidoCard(paciente);
                    },
                  );
                }),
              ),
              const SizedBox(height: 20),
              ElevatedButton(
                onPressed: () => Get.back(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.teal,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 30,
                    vertical: 15,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                child: const Text(
                  'Fechar',
                  style: TextStyle(
                    fontFamily: 'Georgia',
                    fontSize: 16,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPacienteAtendidoCard(ParseObject paciente) {
    // Obter data_fim_atendimento ao invés de data_atendimento
    final dataAtendimento = paciente.get<DateTime>('data_fim_atendimento');

    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            paciente.get<String>('nome') ?? 'Nome não informado',
            style: const TextStyle(
              fontFamily: 'Georgia',
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 5),
          Text(
            dataAtendimento != null
                ? 'Atendido às ${_formatarHora(dataAtendimento)}'
                : 'Horário não registrado',
            style: const TextStyle(
              fontFamily: 'Georgia',
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          if (paciente.get<String>('telefone') != null)
            TextButton.icon(
              onPressed: () => controller.abrirWhatsApp(
                paciente.get<String>('telefone')!,
              ),
              icon: const Icon(
                FontAwesomeIcons.whatsapp,
                color: Colors.green,
              ),
              label: const Text(
                'Contatar',
                style: TextStyle(
                  color: Colors.green,
                ),
              ),
            ),
        ],
      ),
    );
  }

  String _formatarHora(DateTime? dataHora) {
    if (dataHora == null) return '--:--';
    return '${dataHora.hour.toString().padLeft(2, '0')}:${dataHora.minute.toString().padLeft(2, '0')}';
  }
}
