import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../controllers/registration_controller.dart';
import 'package:fila_app/views/gradient_background.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';

class CadastroHospitalScreen extends StatefulWidget {
  const CadastroHospitalScreen({super.key});

  @override
  State<CadastroHospitalScreen> createState() => _CadastroHospitalScreenState();
}

class _CadastroHospitalScreenState extends State<CadastroHospitalScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController cnpjController = TextEditingController();
  final TextEditingController tipoController = TextEditingController();
  final TextEditingController telefoneController = TextEditingController();
  bool _isLoading = false;
  Map<String, String>? _dadosCadastro;
  LatLng? _selectedLocation;
  Set<Marker> _markers = {};
  String? _locationError;

  @override
  void initState() {
    super.initState();
    _getCurrentLocation();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final args = Get.arguments;
    if (args != null) {
      _dadosCadastro = (args as Map<String, dynamic>).map(
        (key, value) => MapEntry(key.toString(), value.toString()),
      );
    }

    if (_dadosCadastro == null || _dadosCadastro!.isEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Navigator.of(context).pushReplacementNamed('/primeiroAcesso');
      });
    }
  }

  Future<void> _getCurrentLocation() async {
    try {
      final permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        final newPermission = await Geolocator.requestPermission();
        if (newPermission == LocationPermission.denied) {
          throw Exception('Permissão de localização negada');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw Exception('Permissão de localização permanentemente negada');
      }

      final position = await Geolocator.getCurrentPosition();
      setState(() {
        _selectedLocation = LatLng(position.latitude, position.longitude);
        _updateMarker();
        _locationError = null;
      });
    } catch (e) {
      setState(() {
        _locationError = e.toString();
      });
    }
  }

  void _updateMarker() {
    if (_selectedLocation == null) return;

    setState(() {
      _markers = {
        Marker(
          markerId: const MarkerId('hospital'),
          position: _selectedLocation!,
          draggable: true,
          onDragEnd: (newPosition) {
            setState(() {
              _selectedLocation = newPosition;
            });
          },
        ),
      };
    });
  }

  Future<void> _handleRegistration() async {
    if (!_formKey.currentState!.validate() ||
        _isLoading ||
        _dadosCadastro == null ||
        _selectedLocation == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Selecione a localização no mapa')),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final placemarks = await placemarkFromCoordinates(
        _selectedLocation!.latitude,
        _selectedLocation!.longitude,
      );

      if (placemarks.isEmpty) {
        throw Exception('Não foi possível obter o endereço');
      }

      await registerUser(
        username: _dadosCadastro!['username']!,
        email: _dadosCadastro!['email']!,
        senha: _dadosCadastro!['senha']!,
        tipo: 'consultorio',
        cnpj: cnpjController.text.trim(),
        tipoHospital: tipoController.text.trim(),
        telefone: telefoneController.text.trim(),
        latitude: _selectedLocation!.latitude,
        longitude: _selectedLocation!.longitude,
        ativo: true,
      );

      if (!mounted) return;

      await _showSuccessDialog();
    } catch (e) {
      if (!mounted) return;
      _showErrorDialog(e.toString());
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  Future<void> _showSuccessDialog() {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: const Text(
            'Cadastro realizado com sucesso!',
            style:
                TextStyle(fontFamily: 'Georgia', fontWeight: FontWeight.bold),
          ),
          content: const Text(
            'Por favor, valide seu e-mail clicando no link enviado para sua caixa de entrada e, em seguida, faça o login no aplicativo para continuar',
            style: TextStyle(fontFamily: 'Georgia'),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pushNamedAndRemoveUntil(
                  '/login',
                  (Route<dynamic> route) => false,
                );
              },
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Erro'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientBackground(
        child: SingleChildScrollView(
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    Image.asset(
                      'assets/logo-removebg-preview-1-28.png',
                      width: MediaQuery.of(context).size.width * 0.8,
                    ),
                    const SizedBox(height: 20),
                    const Text(
                      'Cadastro Hospital',
                      style: TextStyle(
                        fontFamily: 'Georgia',
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 30),
                    Container(
                      height: 300,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15),
                        border: Border.all(color: Colors.grey),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(15),
                        child: _buildMapWidget(),
                      ),
                    ),
                    if (_locationError != null)
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          _locationError!,
                          style: const TextStyle(color: Colors.red),
                        ),
                      ),
                    const SizedBox(height: 20),
                    _buildTextField(
                      label: "CNPJ",
                      hint: "Insira o CNPJ",
                      controller: cnpjController,
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      validator: _validateCNPJ,
                    ),
                    const SizedBox(height: 20),
                    _buildDropdownField(
                      label: "Tipo",
                      hint: "Selecione o tipo",
                      controller: tipoController,
                      items: const ['Público', 'Particular'],
                    ),
                    const SizedBox(height: 20),
                    _buildTextField(
                      label: "Telefone",
                      hint: "Insira o telefone",
                      controller: telefoneController,
                      keyboardType: TextInputType.phone,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      validator: _validateTelefone,
                    ),
                    const SizedBox(height: 40),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        _buildNavigationButton(
                          icon: Icons.arrow_back,
                          label: 'Voltar',
                          onTap: () => Navigator.maybePop(context),
                        ),
                        _buildNavigationButton(
                          icon: Icons.check,
                          label: 'Confirmar',
                          isLoading: _isLoading,
                          onTap: _handleRegistration,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMapWidget() {
    if (_selectedLocation == null) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return GoogleMap(
      initialCameraPosition: CameraPosition(
        target: _selectedLocation!,
        zoom: 15,
      ),
      markers: _markers,
      onTap: (position) {
        setState(() {
          _selectedLocation = position;
          _updateMarker();
        });
      },
    );
  }

  Widget _buildTextField({
    required String label,
    required String hint,
    required TextEditingController controller,
    required String? Function(String?) validator,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontFamily: 'Georgia',
            fontSize: 20,
            color: Colors.black,
          ),
        ),
        TextFormField(
          controller: controller,
          validator: validator,
          keyboardType: keyboardType,
          inputFormatters: inputFormatters,
          decoration: InputDecoration(
            hintText: hint,
            border: const UnderlineInputBorder(),
          ),
        ),
      ],
    );
  }

  Widget _buildDropdownField({
    required String label,
    required String hint,
    required TextEditingController controller,
    required List<String> items,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontFamily: 'Georgia',
            fontSize: 20,
            color: Colors.black,
          ),
        ),
        DropdownButtonFormField<String>(
          value: controller.text.isEmpty ? null : controller.text,
          hint: Text(hint),
          items: items.map((String value) {
            return DropdownMenuItem<String>(
              value: value,
              child: Text(value),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              controller.text = value;
            }
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Por favor, selecione o tipo';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildNavigationButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    bool isLoading = false,
  }) {
    return GestureDetector(
      onTap: isLoading ? null : onTap,
      child: Column(
        children: [
          if (isLoading)
            const SizedBox(
              width: 32,
              height: 32,
              child: CircularProgressIndicator(),
            )
          else
            Icon(icon, size: 32),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(
              fontFamily: 'Georgia',
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  String? _validateCNPJ(String? value) {
    if (value == null || value.isEmpty) {
      return 'Por favor, insira o CNPJ';
    }
    if (value.length != 14) {
      return 'CNPJ inválido';
    }
    return null;
  }

  String? _validateTelefone(String? value) {
    if (value == null || value.isEmpty) {
      return 'Por favor, insira o telefone';
    }
    if (value.length < 10 || value.length > 11) {
      return 'Telefone inválido';
    }
    return null;
  }
}
