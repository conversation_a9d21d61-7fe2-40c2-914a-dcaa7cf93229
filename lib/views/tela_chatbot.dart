import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fila_app/views/gradient_background.dart';
import '../controllers/chatbot_controller.dart';
import '../models/chat_message.dart';

class TelaChatbot extends StatelessWidget {
  final ChatbotController controller = Get.put(ChatbotController());

  TelaChatbot({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientBackground(
        child: SafeArea(
          child: Column(
            children: [
              // Header with back button
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.arrow_back, color: Colors.black87),
                      onPressed: () => Get.back(),
                    ),
                    const Expanded(
                      child: Text(
                        'Assistente Virtual',
                        style: TextStyle(
                          fontFamily: 'Georgia',
                          fontSize: 22,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(width: 48),
                  ],
                ),
              ),

              // Indicador de IA
              Obx(() => Container(
                    margin: const EdgeInsets.symmetric(horizontal: 20),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: controller.isConnectedToSupport.value
                          ? Colors.blue.withOpacity(0.1)
                          : Colors.teal.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                            controller.isConnectedToSupport.value
                                ? Icons.support_agent
                                : Icons.auto_awesome,
                            size: 16,
                            color: controller.isConnectedToSupport.value
                                ? Colors.blue
                                : Colors.teal),
                        SizedBox(width: 4),
                        Text(
                          controller.isConnectedToSupport.value
                              ? 'Atendimento humano: ${controller.supportAgentName}'
                              : 'Assistente potencializado por IA',
                          style: TextStyle(
                            fontFamily: 'Georgia',
                            fontSize: 12,
                            color: controller.isConnectedToSupport.value
                                ? Colors.blue
                                : Colors.teal,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        if (controller.isConnectedToSupport.value) ...[
                          SizedBox(width: 4),
                          InkWell(
                            onTap: controller.disconnectFromSupport,
                            child: Icon(
                              Icons.close_rounded,
                              size: 16,
                              color: Colors.blue,
                            ),
                          ),
                        ],
                      ],
                    ),
                  )),

              // Indicador de conexão com suporte
              Obx(
                () => controller.isConnectingToSupport.value
                    ? Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Colors.blue,
                              ),
                            ),
                            SizedBox(width: 8),
                            Text(
                              'Conectando ao suporte...',
                              style: TextStyle(
                                fontFamily: 'Georgia',
                                fontSize: 12,
                                color: Colors.blue,
                              ),
                            ),
                          ],
                        ),
                      )
                    : SizedBox.shrink(),
              ),

              // Chat messages
              Expanded(
                child: Container(
                  margin: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.9),
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Obx(() => ListView.builder(
                        controller: controller.scrollController,
                        padding: const EdgeInsets.all(16),
                        itemCount: controller.messages.length,
                        itemBuilder: (context, index) {
                          final message = controller.messages[index];
                          return _buildMessageBubble(message);
                        },
                      )),
                ),
              ),

              // Suggested questions carousel
              _buildSuggestedQuestions(),

              // Input field
              _buildInputField(),
              const SizedBox(height: 8),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMessageBubble(ChatMessage message) {
    if (message.isTyping) {
      return _buildTypingIndicator();
    }

    // Para mensagens de sistema
    if (message.type == ChatMessageType.system) {
      return Container(
        margin: const EdgeInsets.symmetric(vertical: 8),
        padding: const EdgeInsets.all(8),
        alignment: Alignment.center,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.grey.shade200,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            message.formattedText,
            style: TextStyle(
              fontFamily: 'Georgia',
              fontSize: 12,
              color: Colors.grey.shade700,
              fontStyle: FontStyle.italic,
            ),
          ),
        ),
      );
    }

    return Align(
      alignment: message.isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 8),
        padding: const EdgeInsets.all(12),
        constraints: BoxConstraints(
          maxWidth: Get.width * 0.75,
        ),
        decoration: BoxDecoration(
          color: message.isUser ? Colors.teal : Colors.grey.shade200,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              message.formattedText,
              style: TextStyle(
                fontFamily: 'Georgia',
                fontSize: 15,
                color: message.isUser
                    ? Colors.white
                    : Colors.black.withOpacity(0.8),
                height: 1.4,
              ),
            ),
            const SizedBox(height: 4),
            Align(
              alignment: Alignment.bottomRight,
              child: Text(
                _formatTime(message.timestamp),
                style: TextStyle(
                  fontFamily: 'Georgia',
                  fontSize: 11,
                  color: message.isUser
                      ? Colors.white.withOpacity(0.7)
                      : Colors.black.withOpacity(0.5),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTypingIndicator() {
    return Align(
      alignment: Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 8),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey.shade200,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildDot(1),
            _buildDot(2),
            _buildDot(3),
          ],
        ),
      ),
    );
  }

  Widget _buildDot(int index) {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.0, end: 1.0),
      duration: Duration(milliseconds: 400),
      curve: Interval(0.1 * index, 0.1 * index + 0.6, curve: Curves.easeInOut),
      builder: (context, value, child) {
        return Container(
          margin: const EdgeInsets.all(3),
          height: 8,
          width: 8,
          decoration: BoxDecoration(
            color: Colors.teal.withOpacity(value),
            shape: BoxShape.circle,
          ),
        );
      },
    );
  }

  Widget _buildSuggestedQuestions() {
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: ChatMessage.getSuggestedQuestions().length,
        itemBuilder: (context, index) {
          final question = ChatMessage.getSuggestedQuestions()[index];
          return Container(
            margin: const EdgeInsets.only(right: 8),
            child: ElevatedButton(
              onPressed: () => controller.sendMessage(question),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: Colors.teal,
                elevation: 1,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                  side: BorderSide(color: Colors.teal.withOpacity(0.3)),
                ),
              ),
              child: Text(
                question,
                style: const TextStyle(
                  fontFamily: 'Georgia',
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildInputField() {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: controller.textController,
              decoration: const InputDecoration(
                hintText: 'Digite sua pergunta...',
                hintStyle: TextStyle(
                  fontFamily: 'Georgia',
                  color: Colors.grey,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(horizontal: 20),
              ),
              style: const TextStyle(
                fontFamily: 'Georgia',
                fontSize: 16,
              ),
              textCapitalization: TextCapitalization.sentences,
              onSubmitted: controller.sendMessage,
            ),
          ),
          Container(
            margin: const EdgeInsets.all(4),
            decoration: const BoxDecoration(
              color: Colors.teal,
              shape: BoxShape.circle,
            ),
            child: IconButton(
              icon: const Icon(Icons.send, color: Colors.white, size: 20),
              onPressed: () {
                controller.sendMessage(controller.textController.text);
              },
            ),
          ),
        ],
      ),
    );
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }
}
