import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fila_app/views/gradient_background.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'package:flutter/services.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import '../controllers/paciente_controller.dart';
import 'package:fila_app/widgets/app_header.dart';

class InserirPacienteFila extends StatefulWidget {
  final String nomeMedico;
  final String especialidade;
  final String id;
  final String solicitacaoId;

  const InserirPacienteFila({
    super.key,
    required this.nomeMedico,
    required this.especialidade,
    required this.id,
    required this.solicitacaoId,
    required String idMedico,
  });

  @override
  State<InserirPacienteFila> createState() => _InserirPacienteFilaState();
}

class _InserirPacienteFilaState extends State<InserirPacienteFila> {
  final _nomeController = TextEditingController();
  final _telefoneController = TextEditingController();
  final _phoneDisplayController = TextEditingController();
  final PacienteController _pacienteController = Get.find<PacienteController>();
  bool _isLoading = false;
  String? _errorMessage;
  PhoneNumber _phoneNumber = PhoneNumber(isoCode: 'BR');
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _loadUserData();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        _isInitialized = true;
      });
    });
  }

  Future<void> _loadUserData() async {
    try {
      final userData =
          await _pacienteController.userDataController.getUserData();
      if (userData != null) {
        setState(() {
          _nomeController.text = userData['nome'] ?? '';
          if (userData['telefone'] != null && userData['telefone'].isNotEmpty) {
            _telefoneController.text = userData['telefone'];
            // This is a simplification - you might need to format the phone number properly
            _phoneDisplayController.text = userData['telefone'];
          }
        });
      }
    } catch (e) {
      debugPrint('Erro ao carregar dados do usuário: $e');
    }
  }

  @override
  void dispose() {
    _nomeController.dispose();
    _telefoneController.dispose();
    _phoneDisplayController.dispose();
    super.dispose();
  }

  Future<void> _confirmarInsercao() async {
    if (_nomeController.text.isEmpty || _telefoneController.text.isEmpty) {
      setState(() {
        _errorMessage = 'Por favor, preencha todos os campos';
      });
      return;
    }

    setState(() => _isLoading = true);

    // Criar ou atualizar o usuário
    try {
      final userData =
          await _pacienteController.userDataController.getUserData();
      if (userData == null || !userData.containsKey('userId')) {
        throw Exception('Não foi possível obter o ID do usuário');
      }

      final deviceId = userData['userId']!;

      debugPrint('Salvando/atualizando usuário com deviceId: $deviceId');

      // Implementação direta para criar/atualizar usuário
      final query = QueryBuilder<ParseObject>(ParseObject('Usuario'))
        ..whereEqualTo('deviceId', deviceId);

      ParseResponse response = await query.query();
      ParseObject userObj;

      if (response.success &&
          response.results != null &&
          response.results!.isNotEmpty) {
        // Atualizar usuário existente
        debugPrint('Usuário existente encontrado, atualizando...');
        userObj = response.results!.first;
        userObj.set('nome', _nomeController.text);
        userObj.set('telefone', _telefoneController.text);
        userObj.set('ultima_atualizacao', DateTime.now());
        userObj.set('ultimoAcesso', DateTime.now());
      } else {
        // Criar novo usuário
        debugPrint('Criando novo usuário...');
        userObj = ParseObject('Usuario')
          ..set('deviceId', deviceId)
          ..set('nome', _nomeController.text)
          ..set('telefone', _telefoneController.text)
          ..set('data_cadastro', DateTime.now())
          ..set('ultima_atualizacao', DateTime.now())
          ..set('ultimoAcesso', DateTime.now())
          ..set('em_fila', false);

        // Configurar ACL pública
        final acl = ParseACL();
        acl.setPublicReadAccess(allowed: true);
        acl.setPublicWriteAccess(allowed: true);
        userObj.setACL(acl);
      }

      // Salvar o objeto usuário
      response = await userObj.save();

      if (!response.success) {
        throw Exception('Erro ao salvar usuário: ${response.error?.message}');
      }

      debugPrint('Usuário salvo com sucesso! ID: ${userObj.objectId}');

      // Atualizar dados no UserDataController
      _pacienteController.userDataController.updateUserData(
        _nomeController.text,
        _telefoneController.text,
      );

      // Adicionar o paciente à fila
      await _adicionarPacienteNaFila(userObj);
    } catch (e) {
      debugPrint('Erro ao salvar usuário: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erro ao salvar usuário: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  // Adiciona o paciente à fila
  Future<void> _adicionarPacienteNaFila(ParseObject userObj) async {
    try {
      // Buscar a solicitação
      final querySolicitacao =
          QueryBuilder<ParseObject>(ParseObject('FilaSolicitacao'))
            ..whereEqualTo('objectId', widget.solicitacaoId)
            ..includeObject(['medicoId', 'hospitalId']);

      final solicitacaoResponse = await querySolicitacao.query();

      if (!solicitacaoResponse.success ||
          solicitacaoResponse.results == null ||
          solicitacaoResponse.results!.isEmpty) {
        throw Exception('Solicitação não encontrada');
      }

      final solicitacao = solicitacaoResponse.results!.first;

      // Determinar a próxima posição na fila
      final queryUltimaPosicao = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('medico', solicitacao.get<ParseObject>('medicoId'))
        ..whereEqualTo('status', 'aguardando')
        ..orderByDescending('posicao')
        ..setLimit(1);

      final posicaoResponse = await queryUltimaPosicao.query();
      final proximaPosicao = (posicaoResponse.results?.isEmpty ?? true)
          ? 1
          : (posicaoResponse.results!.first.get<int>('posicao') ?? 0) + 1;

      // Criar entrada na fila
      final fila = ParseObject('Fila')
        ..set('nome', _nomeController.text)
        ..set('telefone', _telefoneController.text)
        ..set('idPaciente', userObj.get<String>('deviceId'))
        ..set('status', 'aguardando')
        ..set('posicao', proximaPosicao)
        ..set('medico', solicitacao.get<ParseObject>('medicoId'))
        ..set('consultorio', solicitacao.get<ParseObject>('hospitalId'))
        ..set('solicitacao', solicitacao)
        ..set('data_entrada', DateTime.now())
        ..set('data_inicio_atendimento', DateTime.now());

      // Configurar ACL pública
      final acl = ParseACL();
      acl.setPublicReadAccess(allowed: true);
      acl.setPublicWriteAccess(allowed: true);
      fila.setACL(acl);

      final filaResponse = await fila.save();

      if (!filaResponse.success) {
        throw Exception('Erro ao inserir na fila');
      }

      // Criar notificação
      final notificacao = ParseObject('Notificacao')
        ..set('tipo', 'entrada_fila')
        ..set('solicitacao_id', solicitacao.get<String>('solicitacao_id'))
        ..set('fila_id', filaResponse.results!.first.objectId)
        ..set('medico_id', solicitacao.get<ParseObject>('medicoId')?.objectId)
        ..set('consultorio_id',
            solicitacao.get<ParseObject>('hospitalId')?.objectId)
        ..set('lida', false);

      // Configurar ACL pública para notificação
      notificacao.setACL(acl);
      await notificacao.save();

      // Atualizar status da solicitação
      solicitacao.set('status', 'finalizada');
      await solicitacao.save();

      if (!mounted) return;

      // Navegar de volta
      Navigator.of(context).pop(true);
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (Get.previousRoute != '') {
          Get.find<ScrollController>().animateTo(
            0.0,
            duration: const Duration(milliseconds: 100),
            curve: Curves.easeOut,
          );
        }
      });
    } catch (e) {
      debugPrint('Erro ao adicionar paciente na fila: $e');
      throw Exception('Erro ao adicionar paciente na fila: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppHeader(
        title: 'Adicionar à Fila',
        centerTitle: true,
        onBackPressed: () => Navigator.of(context).pop(),
      ),
      body: GradientBackground(
        child: SafeArea(
          bottom: false,
          child: SingleChildScrollView(
            physics: const ClampingScrollPhysics(),
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const SizedBox(height: 20),
                _buildMedicoCard(),
                const SizedBox(height: 20),
                _buildPacienteIdCard(),
                const SizedBox(height: 30),
                if (_errorMessage != null) _buildErrorMessage(),
                const SizedBox(height: 20),
                _buildTextField(
                  label: 'Nome do Paciente',
                  controller: _nomeController,
                  hint: 'Digite o nome completo',
                ),
                const SizedBox(height: 20),
                _buildPhoneField(),
                const SizedBox(height: 40),
                _buildButtons(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMedicoCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            'Dr. ${widget.nomeMedico}',
            style: const TextStyle(
              fontFamily: 'Georgia',
              fontSize: 22,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            widget.especialidade,
            style: const TextStyle(
              fontFamily: 'Georgia',
              fontSize: 18,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPacienteIdCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.person, color: Colors.teal),
          const SizedBox(width: 8),
          Text(
            'ID do Paciente: ${widget.id}',
            style: const TextStyle(
              fontFamily: 'Georgia',
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorMessage() {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: Colors.red),
      ),
      child: Text(
        _errorMessage!,
        style: const TextStyle(
          color: Colors.red,
          fontFamily: 'Georgia',
        ),
      ),
    );
  }

  Widget _buildTextField({
    required String label,
    required TextEditingController controller,
    required String hint,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: TextField(
        controller: controller,
        keyboardType: keyboardType,
        inputFormatters: inputFormatters,
        style: const TextStyle(
          fontFamily: 'Georgia',
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
        decoration: InputDecoration(
          labelText: label,
          labelStyle: const TextStyle(
            fontFamily: 'Georgia',
            fontSize: 15,
            color: Colors.teal,
            fontWeight: FontWeight.w500,
          ),
          hintText: hint,
          hintStyle: TextStyle(
            color: Colors.grey[400],
            fontFamily: 'Georgia',
            fontSize: 15,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.teal.shade200, width: 1),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.teal.shade200, width: 1),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Colors.teal, width: 2),
          ),
          filled: true,
          fillColor: Colors.white,
          contentPadding: const EdgeInsets.all(16),
          prefixIcon: label.contains('Nome')
              ? const Icon(Icons.person_outline, color: Colors.teal)
              : null,
        ),
      ),
    );
  }

  Widget _buildPhoneField() {
    if (!_isInitialized) {
      return Container(
        margin: const EdgeInsets.symmetric(vertical: 10),
        height: 58,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 6,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: const Center(
          child: SizedBox(
            width: 24,
            height: 24,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.teal),
            ),
          ),
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
        border: Border.all(color: Colors.teal.shade200),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: InternationalPhoneNumberInput(
          onInputChanged: (PhoneNumber number) {
            setState(() {
              _telefoneController.text = number.phoneNumber ?? '';
              _phoneNumber = number;
            });
          },
          selectorConfig: const SelectorConfig(
            selectorType: PhoneInputSelectorType.DROPDOWN,
            showFlags: true,
            setSelectorButtonAsPrefixIcon: true,
            leadingPadding: 12,
          ),
          ignoreBlank: false,
          autoValidateMode: AutovalidateMode.disabled,
          initialValue: _phoneNumber,
          formatInput: true,
          keyboardType: TextInputType.phone,
          inputDecoration: InputDecoration(
            labelText: 'Telefone/WhatsApp',
            filled: true,
            fillColor: Colors.white,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 8,
              vertical: 16,
            ),
            border: InputBorder.none,
            floatingLabelBehavior: FloatingLabelBehavior.auto,
            labelStyle: const TextStyle(
              fontFamily: 'Georgia',
              fontSize: 15,
              color: Colors.teal,
              fontWeight: FontWeight.w500,
            ),
            hintText: '(00) 00000-0000',
            hintStyle: TextStyle(
              color: Colors.grey[400],
              fontFamily: 'Georgia',
              fontSize: 15,
            ),
            prefixIcon: const Icon(Icons.phone_outlined, color: Colors.teal),
          ),
          textFieldController: _phoneDisplayController,
          textStyle: const TextStyle(
            fontFamily: 'Georgia',
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
          spaceBetweenSelectorAndTextField: 0,
        ),
      ),
    );
  }

  Widget _buildButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : () => Get.back(result: false),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            child: const Text(
              'Cancelar',
              style: TextStyle(
                fontFamily: 'Georgia',
                fontSize: 16,
                color: Colors.white,
              ),
            ),
          ),
        ),
        const SizedBox(width: 20),
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _confirmarInsercao,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text(
                    'Confirmar',
                    style: TextStyle(
                      fontFamily: 'Georgia',
                      fontSize: 16,
                      color: Colors.white,
                    ),
                  ),
          ),
        ),
      ],
    );
  }
}
