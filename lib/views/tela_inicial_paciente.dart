// lib/views/tela_inicial_paciente.dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:fila_app/views/gradient_background.dart';
import '../controllers/paciente_controller.dart';
import '../widgets/user_data_dialog.dart';
import '../services/push_notification_service.dart';
import 'dart:convert';

class TelaInicialPaciente extends StatelessWidget {
  final PacienteController controller = Get.find<PacienteController>();

  TelaInicialPaciente({super.key}) {
    // Usando Get.put para garantir que userDataController esteja disponível
    Get.put(controller.userDataController);

    // Verificar dados do usuário após o frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkUserData();
    });
  }

  Future<void> _checkUserData() async {
    final userDataController = controller.userDataController;
    await userDataController.checkUserData();

    if (!userDataController.userDataExists.value) {
      Get.dialog(
        UserDataDialog(
          controller: userDataController,
          onComplete: () {
            // Apenas fecha o diálogo e permanece na tela do paciente
            Get.back();
            // Atualiza a interface para mostrar os dados do usuário
            userDataController.userDataExists.value = true;
          },
        ),
        barrierDismissible: false,
      );
    }
  }

  void _showEditDialog() {
    final userDataController = controller.userDataController;
    Get.dialog(
      UserDataDialog(
        controller: userDataController,
        onComplete: () => Get.back(),
        isEditing: true,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: GradientBackground(
        child: GetX<PacienteController>(
          builder: (controller) => Stack(
            children: [
              // Centraliza todo o conteúdo na tela
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const SizedBox(height: 30),

                    // Exibir dados do usuário quando existirem
                    Obx(() {
                      if (controller.userDataController.userDataExists.value) {
                        return Container(
                          margin: const EdgeInsets.symmetric(horizontal: 30),
                          padding: const EdgeInsets.all(15),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.7),
                            borderRadius: BorderRadius.circular(15),
                            border: Border.all(
                                color: Colors.black.withOpacity(0.1)),
                          ),
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Flexible(
                                    child: Text(
                                      'Olá, ${controller.userDataController.nomeController.text}',
                                      style: const TextStyle(
                                        fontFamily: 'Georgia',
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.edit, size: 20),
                                    tooltip: 'Editar seus dados',
                                    onPressed: _showEditDialog,
                                  ),
                                ],
                              ),
                              const SizedBox(height: 5),
                              FutureBuilder<Map<String, dynamic>?>(
                                future:
                                    controller.userDataController.getUserData(),
                                builder: (context, snapshot) {
                                  if (snapshot.hasData &&
                                      snapshot.data != null) {
                                    final userId =
                                        snapshot.data!['userId'] ?? '';
                                    return Text(
                                      'ID: $userId',
                                      style: const TextStyle(
                                        fontFamily: 'Georgia',
                                        fontSize: 14,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    );
                                  }
                                  return const SizedBox.shrink();
                                },
                              ),
                              const SizedBox(height: 5),
                              const Text(
                                'Escaneie o QR Code para entrar na fila',
                                style: TextStyle(
                                  fontFamily: 'Georgia',
                                  fontSize: 16,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        );
                      }
                      return const SizedBox.shrink();
                    }),

                    const SizedBox(height: 20),

                    if (controller.errorMessage.isNotEmpty)
                      _buildErrorMessage()
                    else
                      _buildScannerButton(),
                    const SizedBox(height: 50),
                    _buildBackButton(),
                  ],
                ),
              ),
              if (controller.showScanner.value) _buildQRScanner(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildErrorMessage() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: Colors.red),
      ),
      child: Text(
        controller.errorMessage.value,
        style: const TextStyle(
          color: Colors.red,
          fontFamily: 'Georgia',
          fontSize: 16,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildScannerButton() {
    return GestureDetector(
      onTap: controller.toggleScanner,
      child: Container(
        width: Get.width * 0.8,
        padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.9),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              spreadRadius: 1,
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              'assets/scan-me-qr-code-1.png',
              width: 200,
              height: 200,
              fit: BoxFit.contain,
            ),
            const SizedBox(height: 20),
            const Text(
              'Toque para escanear',
              style: TextStyle(
                fontFamily: 'Georgia',
                fontSize: 24,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackButton() {
    return GestureDetector(
      onTap: () => Get.back(),
      child: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          color: Colors.white,
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              spreadRadius: 2,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Center(
          child: SizedBox(
            width: 24,
            height: 24,
            child: Image.asset(
              'assets/left.png',
              fit: BoxFit.contain,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildQRScanner() {
    return Positioned.fill(
      child: Container(
        color: Colors.black,
        child: Stack(
          children: [
            MobileScanner(
              controller: controller.scannerController,
              onDetect: (capture) {
                final List<Barcode> barcodes = capture.barcodes;
                for (final barcode in barcodes) {
                  if (barcode.rawValue == null) continue;

                  try {
                    final qrData = json.decode(barcode.rawValue!);
                    controller.toggleScanner();
                    controller.showQRDialog(qrData);
                    break;
                  } catch (e) {
                    controller.errorMessage.value =
                        'QR Code inválido. Tente novamente.';
                    controller.toggleScanner();
                  }
                }
              },
            ),
            Positioned(
              top: 40,
              left: 10,
              child: IconButton(
                icon: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 30,
                ),
                onPressed: controller.toggleScanner,
              ),
            ),
            const Positioned(
              bottom: 100,
              left: 0,
              right: 0,
              child: Text(
                'Posicione o QR Code dentro da área',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontFamily: 'Georgia',
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
