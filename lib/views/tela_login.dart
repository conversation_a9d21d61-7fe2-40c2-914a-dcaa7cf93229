import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:fila_app/views/gradient_background.dart';
import '../controllers/login_controller.dart';

class TelaLogin extends StatefulWidget {
  const TelaLogin({super.key});

  @override
  State<TelaLogin> createState() => _TelaLoginState();
}

class _TelaLoginState extends State<TelaLogin>
    with SingleTickerProviderStateMixin {
  final _emailController = TextEditingController();
  final _senhaController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  final LoginController _controller = Get.put(LoginController());
  late AnimationController _buttonController;
  final FocusNode _emailFocus = FocusNode();
  final FocusNode _senhaFocus = FocusNode();

  @override
  void initState() {
    super.initState();
    _buttonController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // Ensure smooth animations
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
      ),
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _controller.clearErrors();
    });
  }

  @override
  void dispose() {
    _emailController.dispose();
    _senhaController.dispose();
    _buttonController.dispose();
    _emailFocus.dispose();
    _senhaFocus.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientBackground(
        child: Form(
          key: _formKey,
          child: SafeArea(
            child: Center(
              child: SingleChildScrollView(
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildLoginHeader(),
                    const SizedBox(height: 30),
                    _buildLoginFormFields(),
                    const SizedBox(height: 5),
                    _buildForgotPassword(),
                    const SizedBox(height: 20),
                    _buildLoginButton(),
                    const SizedBox(height: 20),
                    _buildSignUpSection(),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoginHeader() {
    return Row(
      children: [
        IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black87),
          onPressed: () => Get.offAllNamed('/'),
        ),
        const Expanded(
          child: Center(
            child: Text(
              'Login',
              style: TextStyle(
                fontFamily: 'Georgia',
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ),
        ),
        // Add a spacer with the same width as the back button
        const SizedBox(width: 48),
      ],
    );
  }

  Widget _buildLoginFormFields() {
    return Column(
      children: [
        _buildEmailField(),
        const SizedBox(height: 20),
        _buildPasswordField(),
      ],
    );
  }

  Widget _buildEmailField() {
    return Obx(() => AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(30),
          ),
          child: TextFormField(
            controller: _emailController,
            focusNode: _emailFocus,
            keyboardType: TextInputType.emailAddress,
            textInputAction: TextInputAction.next,
            onFieldSubmitted: (_) =>
                FocusScope.of(context).requestFocus(_senhaFocus),
            cursorColor: Colors.teal,
            cursorWidth: 1.5,
            cursorRadius: const Radius.circular(4),
            style: const TextStyle(
              fontFamily: 'Georgia',
              fontSize: 16,
              color: Colors.black87,
            ),
            inputFormatters: [
              FilteringTextInputFormatter.deny(RegExp(r'\s+$')), // Evita espaços no final durante a digitação
            ],
            decoration: InputDecoration(
              filled: true,
              fillColor: Colors.white,
              hintText: 'Email',
              errorText: _controller.emailError.isEmpty
                  ? null
                  : _controller.emailError,
              prefixIcon: const Padding(
                padding: EdgeInsets.only(left: 16, right: 8),
                child: Icon(Icons.email_outlined, color: Colors.teal, size: 20),
              ),
              prefixIconConstraints:
                  const BoxConstraints(minWidth: 24, minHeight: 24),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(30),
                borderSide: BorderSide.none,
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(30),
                borderSide: const BorderSide(color: Colors.teal, width: 1.0),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(30),
                borderSide: BorderSide.none,
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(30),
                borderSide:
                    const BorderSide(color: Colors.redAccent, width: 1.0),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(30),
                borderSide:
                    const BorderSide(color: Colors.redAccent, width: 1.0),
              ),
              contentPadding: const EdgeInsets.fromLTRB(20, 16, 20, 16),
            ),
            onChanged: (_) => _controller.clearErrors(),
          ),
        ));
  }

  Widget _buildPasswordField() {
    return Obx(() => AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(30),
          ),
          child: TextFormField(
            controller: _senhaController,
            focusNode: _senhaFocus,
            obscureText: _controller.obscureText,
            textInputAction: TextInputAction.done,
            onFieldSubmitted: (_) => _attemptLogin(),
            cursorColor: Colors.teal,
            cursorWidth: 1.5,
            cursorRadius: const Radius.circular(4),
            style: const TextStyle(
              fontFamily: 'Georgia',
              fontSize: 16,
              color: Colors.black87,
            ),
            decoration: InputDecoration(
              filled: true,
              fillColor: Colors.white,
              hintText: 'Senha',
              errorText: _controller.passwordError.isEmpty
                  ? null
                  : _controller.passwordError,
              prefixIcon: const Padding(
                padding: EdgeInsets.only(left: 16, right: 8),
                child: Icon(Icons.lock_outline, color: Colors.teal, size: 20),
              ),
              prefixIconConstraints:
                  const BoxConstraints(minWidth: 24, minHeight: 24),
              suffixIcon: _buildVisibilityIcon(),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(30),
                borderSide: BorderSide.none,
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(30),
                borderSide: const BorderSide(color: Colors.teal, width: 1.0),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(30),
                borderSide: BorderSide.none,
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(30),
                borderSide:
                    const BorderSide(color: Colors.redAccent, width: 1.0),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(30),
                borderSide:
                    const BorderSide(color: Colors.redAccent, width: 1.0),
              ),
              contentPadding: const EdgeInsets.fromLTRB(20, 16, 20, 16),
            ),
            onChanged: (_) => _controller.clearErrors(),
          ),
        ));
  }

  Widget _buildVisibilityIcon() {
    return Material(
      color: Colors.transparent,
      shape: const CircleBorder(),
      clipBehavior: Clip.hardEdge,
      child: IconButton(
        icon: Obx(() => Icon(
              _controller.obscureText
                  ? Icons.visibility_off_outlined
                  : Icons.visibility_outlined,
              color: Colors.teal,
              size: 20,
            )),
        onPressed: _controller.togglePasswordVisibility,
        splashColor: Colors.teal.withValues(alpha: 0.2 * 255),
        tooltip: _controller.obscureText ? 'Mostrar senha' : 'Ocultar senha',
      ),
    );
  }

  Widget _buildForgotPassword() {
    return Align(
      alignment: Alignment.centerRight,
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        clipBehavior: Clip.hardEdge,
        child: InkWell(
          onTap: () => Get.toNamed('/esqueceu_senha'),
          splashColor: Colors.teal.withValues(alpha: 0.1 * 255),
          highlightColor: Colors.teal.withValues(alpha: 0.05 * 255),
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 8,
              vertical: 4,
            ),
            child: Text(
              'Esqueceu a senha?',
              style: TextStyle(
                fontFamily: 'Georgia',
                fontSize: 14,
                color: Colors.teal.shade700,
                decoration: TextDecoration.underline,
                decorationColor:
                    Colors.teal.shade700.withValues(alpha: 0.5 * 255),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoginButton() {
    return Center(
      child: Obx(
        () => _controller.isLoading
            ? Container(
                width: 50,
                height: 50,
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.teal.withValues(alpha: 0.2 * 255),
                  shape: BoxShape.circle,
                ),
                child: const CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.teal),
                  strokeWidth: 3,
                ),
              )
            : Container(
                width: double.infinity,
                height: 52,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(30),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.teal.withOpacity(0.25),
                      blurRadius: 4,
                      spreadRadius: 0,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Material(
                  color: Colors.teal,
                  borderRadius: BorderRadius.circular(30),
                  child: InkWell(
                    borderRadius: BorderRadius.circular(30),
                    splashColor: Colors.white.withValues(alpha: 0.1 * 255),
                    highlightColor: Colors.white.withValues(alpha: 0.05 * 255),
                    onTap: _attemptLogin,
                    child: const Center(
                      child: Text(
                        'Entrar',
                        style: TextStyle(
                          fontFamily: 'Georgia',
                          fontSize: 18,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          letterSpacing: 0.5,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
      ),
    );
  }

  Widget _buildSignUpSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Text(
          'Não tem uma conta? ',
          style: TextStyle(
            fontFamily: 'Georgia',
            fontSize: 14,
            color: Colors.black87,
          ),
        ),
        GestureDetector(
          onTap: () => Get.toNamed('/primeiroAcesso'),
          child: Text(
            'Cadastre-se',
            style: TextStyle(
              fontFamily: 'Georgia',
              fontSize: 14,
              color: Colors.teal.shade800,
              fontWeight: FontWeight.bold,
              decoration: TextDecoration.underline,
            ),
          ),
        ),
      ],
    );
  }

  void _attemptLogin() {
    if (!_controller.isLoading) {
      // Esconde o teclado antes de iniciar o login
      FocusScope.of(context).unfocus();
      
      // Remove espaços em branco do início e fim do email e senha
      final email = _emailController.text.trim();
      final senha = _senhaController.text.trim();
      
      _controller.handleLogin(email, senha);
    }
  }
}
