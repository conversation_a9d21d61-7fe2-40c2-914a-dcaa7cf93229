import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fila_app/views/gradient_background.dart';
import '../controllers/fila_paciente_controller.dart';
import 'dart:math' as math;
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class FilaPacienteScreen extends GetView<FilaPacienteController> {
  const FilaPacienteScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Usando WillPopScope para controlar o comportamento ao voltar
    return WillPopScope(
      onWillPop: () async {
        // Mostrar diálogo de confirmação em vez de simplesmente voltar
        final result = await _confirmarSaida(context);
        return result;
      },      child: Scaffold(        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          centerTitle: true, 
          automaticallyImplyLeading: false, // Remove o botão de voltar automático
          title: Text(
            'Acompanhamento da Fila',
            style: TextStyle(
              color: Colors.teal,
              fontFamily: 'Georgia',
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        body: GradientBackground(
          child: Obx(() {
            if (controller.isLoading.value) {
              return const Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.teal),
                    ),
                    SizedBox(height: 16),
                    Text(
                      'Carregando dados da fila...',
                      style: TextStyle(
                        fontFamily: 'Georgia',
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              );
            }

            // Verificar status para mostrar tela de atendimento em andamento
            final filaStatus = controller.statusAtual.value;

            // Se o paciente está em atendimento, mostrar a tela especial
            if (filaStatus == 'em_atendimento') {
              return _buildEmAtendimentoScreen(context);
            }

            return SafeArea(
              child: RefreshIndicator(
                onRefresh: () async {
                  // Criar método personalizado para atualização manual
                  await Future.wait([
                    controller.atualizarDados(),
                  ]);
                },
                color: Colors.teal,
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      children: [
                        const SizedBox(height: 30),
                        // Logo e título
                        _buildHeader(),
                        const SizedBox(height: 20),
                        // Mensagem da secretaria
                        _buildMensagemSecretaria(),
                        const SizedBox(height: 20),
                        // Card de informações principais
                        _buildInfoCard(),
                        const SizedBox(height: 30),
                        // Indicador visual da posição na fila
                        _buildFilaVisual(),
                        const SizedBox(height: 30),
                        // Tempo estimado
                        _buildTempoEstimado(),
                        const SizedBox(height: 30),
                        // Médico
                        _buildMedicoInfo(),
                        const SizedBox(height: 20),
                        // Tempo de distância
                        _buildTempoDistancia(),
                        const SizedBox(height: 30),
                        // Botão para sair da fila
                        _buildSairFilaButton(),
                        const SizedBox(height: 30),
                        // Ações complementares
                        _buildBottomActions(),
                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
              ),
            );
          }),
        ),
      ),
    );
  }

  // Função para confirmar a saída da tela de fila
  Future<bool> _confirmarSaida(BuildContext context) async {
    final filaStatus = controller.statusAtual.value;
    final String titulo = filaStatus == 'em_atendimento'
        ? 'Abandonar Atendimento?'
        : 'Sair da Fila?';
    final String mensagem = filaStatus == 'em_atendimento'
        ? 'Você está em atendimento no momento. Sair desta tela irá registrar seu atendimento como abandonado. Tem certeza que deseja continuar?'
        : 'Você está na posição ${controller.posicaoAtual} da fila. Sair desta tela significa perder sua posição na fila. Tem certeza que deseja continuar?';

    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            titulo,
            style: const TextStyle(
              fontFamily: 'Georgia',
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          content: Text(
            mensagem,
            style: const TextStyle(
              fontFamily: 'Georgia',
            ),
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text(
                'CANCELAR',
                style: TextStyle(
                  fontFamily: 'Georgia',
                  color: Colors.grey,
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                // Se o usuário confirmar a saída, chamar o método para sair da fila
                if (filaStatus != 'em_atendimento') {
                  controller.confirmarSaidaFila();
                }
                Navigator.of(context).pop(true);
              },
              child: const Text(
                'SIM, SAIR',
                style: TextStyle(
                  fontFamily: 'Georgia',
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        );
      },
    );

    // Se o usuário confirmou a saída e o resultado é true, então fazer Get.back()
    if (result == true) {
      Get.back();
    }

    // Retornar o resultado para o WillPopScope
    return result ?? false;
  }

  // Nova tela para quando o paciente está em atendimento
  Widget _buildEmAtendimentoScreen(BuildContext context) {
    return SafeArea(
      child: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            children: [
              const SizedBox(height: 20),

              // Cabeçalho com animação
              _buildEmAtendimentoHeader(),

              const SizedBox(height: 30),

              // Card principal com informações do atendimento
              _buildAtendimentoCard(),

              const SizedBox(height: 30),

              // Informações do médico com foto
              _buildMedicoAtendimentoInfo(),

              const SizedBox(height: 30),

              // Duração do atendimento
              _buildTempoAtendimento(),

              const SizedBox(height: 40),

              // Botões de segurança e emergência
              _buildSecurityButtons(),

              const SizedBox(height: 20),

              // Adicionar botão do WhatsApp
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () => controller.abrirWhatsApp(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF25D366),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  icon: const FaIcon(FontAwesomeIcons.whatsapp, size: 24),
                  label: const Text(
                    'Contatar Hospital pelo WhatsApp',
                    style: TextStyle(
                      fontFamily: 'Georgia',
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmAtendimentoHeader() {
    return Column(
      children: [
        // Ícone animado
        TweenAnimationBuilder<double>(
          tween: Tween<double>(begin: 0.0, end: 1.0),
          duration: const Duration(seconds: 1),
          builder: (context, value, child) {
            return Transform.scale(
              scale: value,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.green.shade100,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.green.withOpacity(0.3),
                      blurRadius: 12,
                      spreadRadius: 4 * value,
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.medical_services,
                  color: Colors.green,
                  size: 48,
                ),
              ),
            );
          },
        ),

        const SizedBox(height: 16),

        // Texto animado
        TweenAnimationBuilder<double>(
          tween: Tween<double>(begin: 0.0, end: 1.0),
          duration: const Duration(milliseconds: 800),
          curve: Curves.easeOutBack,
          builder: (context, value, child) {
            // Limitar o valor de opacidade para o intervalo permitido [0.0, 1.0]
            final safeOpacity = value.clamp(0.0, 1.0);

            return Opacity(
              opacity: safeOpacity,
              child: Transform.translate(
                offset: Offset(0, 20 * (1 - value)),
                child: const Text(
                  'Atendimento em Andamento',
                  style: TextStyle(
                    fontFamily: 'Georgia',
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            );
          },
        ),

        const SizedBox(height: 8),

        // Subtítulo
        Text(
          'Você está sendo atendido pelo profissional',
          style: TextStyle(
            fontFamily: 'Georgia',
            fontSize: 16,
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildAtendimentoCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.green.shade50, Colors.green.shade100],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.green.withOpacity(0.15),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
        border: Border.all(color: Colors.green.shade200, width: 1.5),
      ),
      child: Column(
        children: [
          // Status com ícone pulsante
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TweenAnimationBuilder<double>(
                tween: Tween<double>(begin: 0.8, end: 1.2),
                duration: const Duration(seconds: 1),
                curve: Curves.easeInOut,
                builder: (context, value, child) {
                  return Transform.scale(
                    scale: value,
                    child: const Icon(Icons.healing,
                        color: Colors.green, size: 28),
                  );
                },
                child: const SizedBox(),
              ),
              const SizedBox(width: 10),
              const Text(
                'Atendimento Iniciado',
                style: TextStyle(
                  fontFamily: 'Georgia',
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Informações do consultório
          Container(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    const Icon(Icons.location_on, color: Colors.teal, size: 20),
                    const SizedBox(width: 8),
                    const Text(
                      'Local do Atendimento:',
                      style: TextStyle(
                        fontFamily: 'Georgia',
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Obx(() => Text(
                      controller.hospitalNome.value,
                      style: const TextStyle(
                        fontFamily: 'Georgia',
                        fontSize: 16,
                      ),
                      textAlign: TextAlign.center,
                    )),
              ],
            ),
          ),

          const SizedBox(height: 12),

          // Horário de início
          Container(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    const Icon(Icons.access_time, color: Colors.teal, size: 20),
                    const SizedBox(width: 8),
                    const Text(
                      'Horário de Início:',
                      style: TextStyle(
                        fontFamily: 'Georgia',
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Obx(() => Text(
                      _formatarHorarioInicio(
                          controller.dataInicioAtendimento.value),
                      style: const TextStyle(
                        fontFamily: 'Georgia',
                        fontSize: 16,
                      ),
                      textAlign: TextAlign.center,
                    )),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatarHorarioInicio(DateTime? data) {
    if (data == null) return "Agora";
    return "${data.hour.toString().padLeft(2, '0')}:${data.minute.toString().padLeft(2, '0')}";
  }

  Widget _buildMedicoAtendimentoInfo() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          const Text(
            'Seu médico',
            style: TextStyle(
              fontFamily: 'Georgia',
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 16),

          // Avatar do médico
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.teal.shade50,
              border: Border.all(color: Colors.teal.shade200, width: 2),
            ),
            child: Center(
              child: Icon(
                Icons.person,
                size: 60,
                color: Colors.teal.shade300,
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Nome do médico
          Obx(() => Text(
                'Dr. ${controller.medicoNome.value}',
                style: const TextStyle(
                  fontFamily: 'Georgia',
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              )),

          const SizedBox(height: 4),

          // Especialidade
          Obx(() => Text(
                controller.especialidade.value,
                style: TextStyle(
                  fontFamily: 'Georgia',
                  fontSize: 16,
                  color: Colors.grey[700],
                  fontStyle: FontStyle.italic,
                ),
              )),

          const SizedBox(height: 16),

          // CRM
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              'CRM: ${controller.medicoCRM.value}',
              style: TextStyle(
                fontFamily: 'Georgia',
                fontSize: 14,
                color: Colors.grey[700],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTempoAtendimento() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          const Text(
            'Duração do Atendimento',
            style: TextStyle(
              fontFamily: 'Georgia',
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.timer, color: Colors.teal, size: 28),
              const SizedBox(width: 10),
              Obx(() => Text(
                    '${controller.duracaoAtendimento.value} minutos',
                    style: const TextStyle(
                      fontFamily: 'Georgia',
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.teal,
                    ),
                  )),
            ],
          ),
          const SizedBox(height: 10),
          Text(
            'O tempo médio de atendimento é de ${controller.tempoMedioAtendimento.value} minutos',
            style: TextStyle(
              fontFamily: 'Georgia',
              fontSize: 14,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSecurityButtons() {
    return Column(
      children: [
        // Texto explicativo
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            'Caso precise de assistência imediata ou esteja em situação de emergência durante o atendimento, utilize os botões abaixo:',
            style: TextStyle(
              fontFamily: 'Georgia',
              fontSize: 14,
              color: Colors.grey[700],
            ),
            textAlign: TextAlign.center,
          ),
        ),

        const SizedBox(height: 24),

        // Botões de emergência
        Builder(
          builder: (context) => Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Botão SOS
              _buildEmergencyButton(
                icon: Icons.emergency,
                color: Colors.red,
                label: 'SOS',
                onTap: () => _showEmergencyDialog(context, 'sos'),
              ),

              // Botão Denunciar
              _buildEmergencyButton(
                icon: Icons.report_problem,
                color: Colors.orange,
                label: 'Denunciar',
                onTap: () => _showEmergencyDialog(context, 'denuncia'),
              ),

              // Botão Ajuda
              _buildEmergencyButton(
                icon: Icons.help_outline,
                color: Colors.blue,
                label: 'Ajuda',
                onTap: () => _showEmergencyDialog(context, 'ajuda'),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildEmergencyButton({
    required IconData icon,
    required Color color,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 70,
            height: 70,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              shape: BoxShape.circle,
              border: Border.all(color: color, width: 2),
            ),
            child: Icon(
              icon,
              color: color,
              size: 36,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              fontFamily: 'Georgia',
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  void _showEmergencyDialog(BuildContext context, String tipo) {
    String titulo;
    String mensagem;
    IconData icone;
    Color cor;

    switch (tipo) {
      case 'sos':
        titulo = 'Emergência SOS';
        mensagem =
            'Deseja acionar o botão de emergência? Isso notificará a equipe de segurança do hospital imediatamente.';
        icone = Icons.emergency;
        cor = Colors.red;
        break;
      case 'denuncia':
        titulo = 'Fazer Denúncia';
        mensagem =
            'Você deseja registrar uma denúncia sobre este atendimento? Esta informação será tratada com confidencialidade.';
        icone = Icons.report_problem;
        cor = Colors.orange;
        break;
      case 'ajuda':
        titulo = 'Solicitar Ajuda';
        mensagem =
            'Precisa de assistência durante o atendimento? Um responsável será notificado para ajudá-lo.';
        icone = Icons.help_outline;
        cor = Colors.blue;
        break;
      default:
        titulo = 'Atenção';
        mensagem = 'Confirma esta ação?';
        icone = Icons.warning;
        cor = Colors.amber;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(icone, color: cor),
            const SizedBox(width: 10),
            Text(
              titulo,
              style: TextStyle(color: cor),
            ),
          ],
        ),
        content: Text(mensagem),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Implementar ação com base no tipo
              controller.registrarEmergencia(tipo);

              // Feedback ao usuário
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                      'Sua solicitação de ${tipo == 'sos' ? 'emergência' : tipo} foi enviada'),
                  backgroundColor: cor,
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: cor),
            child:
                const Text('Confirmar', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
  Widget _buildHeader() {
    return Column(
      children: [
        const SizedBox(height: 8),
        // Removido o título duplicado, já que agora está na AppBar
      ],
    );
  }
  Widget _buildInfoCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.teal.shade50, Colors.teal.shade100],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.teal.withOpacity(0.2),
            blurRadius: 12,
            offset: const Offset(0, 5),
          ),
        ],
        border: Border.all(color: Colors.teal.shade200, width: 1.5),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.teal.withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.people_alt, color: Colors.teal, size: 28),
              ),
              const SizedBox(width: 12),
              Text(
                'Sua posição atual:',
                style: TextStyle(
                  fontFamily: 'Georgia',
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.teal.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Obx(() {
            final int posicao = controller.posicaoAtual.value;
            final key = ValueKey<int>(posicao);
            
            return AnimatedSwitcher(
              duration: const Duration(milliseconds: 800),
              transitionBuilder: (Widget child, Animation<double> animation) {
                // Cria uma animação de slide + fade quando muda o número da posição
                return FadeTransition(
                  opacity: animation,
                  child: SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(0.0, 0.5),
                      end: Offset.zero,
                    ).animate(CurvedAnimation(
                      parent: animation,
                      curve: Curves.elasticOut,
                    )),
                    child: child,
                  ),
                );
              },
              child: TweenAnimationBuilder<double>(
                key: key,
                tween: Tween<double>(begin: 0.8, end: 1.2),
                duration: const Duration(milliseconds: 800),
                curve: Curves.elasticOut,
                builder: (context, scale, child) {
                  return Transform.scale(                    scale: scale,
                    child: Text(
                      '${posicao}°',
                      style: const TextStyle(
                        fontFamily: 'Georgia',
                        fontSize: 58,
                        fontWeight: FontWeight.bold,
                        color: Colors.teal,
                        shadows: [
                          Shadow(
                            offset: Offset(0, 2),
                            blurRadius: 3.0,
                            color: Color.fromARGB(80, 0, 0, 0),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            );
          }),
          const SizedBox(height: 8),
          Text(
            'Aguarde sua vez de ser chamado',
            style: TextStyle(
              fontFamily: 'Georgia',
              fontSize: 14,
              fontStyle: FontStyle.italic,
              color: Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }  Widget _buildFilaVisual() {
    // Não precisamos calcular totalPessoas aqui - vamos fazer isso dentro do Obx
    
    // Controlador para o scroll do carrossel
    final ScrollController scrollController = ScrollController();
    
    // Criar uma key baseada na posição atual para forçar reconstrução quando mudar
    final key = GlobalKey(debugLabel: 'fila_visual_${controller.posicaoAtual.value}');    // Configurar um listener para a posição atual
    ever(controller.posicaoAtual, (int posicaoAtual) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (scrollController.hasClients) {
          // Calcula a posição para centralizar o item atual
          // Cada item tem 70 de largura + 8 de padding (4 em cada lado)
          final double itemWidth = 78.0;
          final double targetPosition = (posicaoAtual - 1) * itemWidth;
          final double screenWidth = Get.width - 40; // Largura da tela menos padding
          final double offset = math.max(0, targetPosition - (screenWidth / 2) + (itemWidth / 2));
          
          // Animar até a posição com curva de animação suave
          scrollController.animateTo(
            offset,
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeOutQuart,
          );
        }
      });
    });
    
    // Adicionar também um listener para quando o widget for construído pela primeira vez
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final posicaoAtual = controller.posicaoAtual.value;
      if (scrollController.hasClients) {
        final double itemWidth = 78.0;
        final double targetPosition = (posicaoAtual - 1) * itemWidth;
        final double screenWidth = Get.width - 40;
        final double offset = math.max(0, targetPosition - (screenWidth / 2) + (itemWidth / 2));
        
        scrollController.animateTo(
          offset,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeOutQuart,
        );
      }
    });

    return Container(
      key: key,
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.7),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          const Text(
            'Visualização da fila',
            style: TextStyle(
              fontFamily: 'Georgia',
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          
          // Conteúdo da fila - pode ser uma mensagem ou o carrossel
          Obx(() {
            final totalPacientes = controller.totalPacientesFila.value;
            
            // Se houver apenas 1 paciente (o próprio usuário), exibir mensagem especial
            if (totalPacientes <= 1) {
              return Container(
                padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                decoration: BoxDecoration(
                  color: Colors.teal.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.teal.withOpacity(0.2)),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.info_outline, color: Colors.teal.shade700),
                    const SizedBox(width: 12),
                    Flexible(
                      child: Text(
                        'Você é o único paciente na fila!',
                        style: TextStyle(
                          fontFamily: 'Georgia',
                          fontSize: 16,
                          color: Colors.teal.shade700,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              );
            }
            
            // Se houver mais pacientes, mostrar o carrossel
            return SizedBox(
              height: 100,
              child: ListView.builder(
                controller: scrollController,
                scrollDirection: Axis.horizontal,
                itemCount: totalPacientes,
                itemBuilder: (context, index) {
                  final posicaoAtual = controller.posicaoAtual.value;
                  
                  // Determinar se este é a posição do paciente atual
                  final bool isCurrentPosition = index + 1 == posicaoAtual;
                  
                  // Determinar se já foi atendido (posições anteriores)
                  final bool isServed = index + 1 < posicaoAtual;
                  
                  // Determinar quem está na frente do paciente na fila
                  final bool isAhead = index + 1 > posicaoAtual;
                
                  return TweenAnimationBuilder<double>(
                    tween: Tween<double>(
                      begin: 0.8, 
                      end: isCurrentPosition ? 1.1 : 1.0
                    ),
                    duration: const Duration(milliseconds: 500),
                    curve: Curves.elasticOut,
                    builder: (context, scale, child) {
                      return Transform.scale(
                        scale: scale,
                        child: Container(
                          width: 70,
                          margin: const EdgeInsets.symmetric(horizontal: 4),
                          decoration: isCurrentPosition ? BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            color: Colors.teal.withOpacity(0.1),
                            border: Border.all(color: Colors.teal.withOpacity(0.3)),
                          ) : null,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                isCurrentPosition
                                    ? Icons.person_pin_circle
                                    : (isServed ? Icons.check_circle : Icons.person),
                                color: isCurrentPosition
                                    ? Colors.teal
                                    : (isServed ? Colors.grey : 
                                      isAhead ? Colors.black54 : Colors.grey),
                                size: isCurrentPosition ? 32 : 24,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                '${index + 1}',
                                style: TextStyle(
                                  fontFamily: 'Georgia',
                                  fontWeight: isCurrentPosition
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                                  color: isCurrentPosition 
                                      ? Colors.teal 
                                      : isServed ? Colors.grey : Colors.black87,
                                ),
                              ),
                              if (isCurrentPosition)
                                const Text(
                                  'Você',
                                  style: TextStyle(
                                    fontFamily: 'Georgia',
                                    fontSize: 12,
                                    color: Colors.teal,
                                  ),
                                ),
                              if (index + 1 == controller.posicaoAtual.value + 1)
                                const Text(
                                  'Próximo',
                                  style: TextStyle(
                                    fontFamily: 'Georgia',
                                    fontSize: 12,
                                    color: Colors.orange,
                                  ),
                                ),
                            ],
                          ),
                        ),
                      );
                    }
                  );
                },
              ),
            );
          }),
          
          // Instruções sobre como usar o carrossel
          const SizedBox(height: 8),
          Obx(() {
            final totalPacientes = controller.totalPacientesFila.value;
            
            // Só mostrar as instruções se houver mais de um paciente na fila
            if (totalPacientes <= 1) return const SizedBox.shrink();
            
            return const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.swipe, size: 16, color: Colors.grey),
                SizedBox(width: 4),
                Text(
                  'Deslize para ver mais posições na fila',
                  style: TextStyle(
                    fontFamily: 'Georgia',
                    fontSize: 12,
                    fontStyle: FontStyle.italic,
                    color: Colors.grey,
                  ),
                ),
              ],
            );
          }),
        ],
      ),
    );
  }

  Widget _buildMensagemSecretaria() {
    return Obx(() {
      if (controller.mensagens.isEmpty) {
        return Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.shade300),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  const Icon(Icons.notifications_none, color: Colors.grey),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      "Sem notificações",
                      style: TextStyle(
                        fontFamily: 'Georgia',
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              const Text(
                "Você receberá avisos da secretaria aqui",
                style: TextStyle(
                  fontFamily: 'Georgia',
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        );
      }

      final mensagem = controller.mensagens.first;
      return InkWell(
        onTap: () => _mostrarTodasMensagens(),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Colors.teal.shade50, Colors.teal.shade100],
            ),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.teal.shade200),
            boxShadow: [
              BoxShadow(
                color: Colors.teal.withOpacity(0.2),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.teal.withOpacity(0.2),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(Icons.notifications_active,
                        color: Colors.teal),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          mensagem.titulo,
                          style: const TextStyle(
                            fontFamily: 'Georgia',
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.teal,
                          ),
                        ),
                        Text(
                          _formatDateTime(mensagem.dataEnvio),
                          style: TextStyle(
                            fontFamily: 'Georgia',
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Icon(Icons.arrow_forward_ios,
                      size: 16, color: Colors.teal),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                mensagem.texto,
                style: const TextStyle(
                  fontFamily: 'Georgia',
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    controller.mensagens.length > 1
                        ? "Ver todas as ${controller.mensagens.length} mensagens"
                        : "Ver detalhes",
                    style: TextStyle(
                      fontFamily: 'Georgia',
                      fontSize: 12,
                      color: Colors.teal[700],
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    });
  }

  String _formatDateTime(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inHours < 1) {
      return '${difference.inMinutes} minutos atrás';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} horas atrás';
    } else {
      return '${date.day}/${date.month}/${date.year} às ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
    }
  }

  void _mostrarTodasMensagens() {
    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        elevation: 8,
        backgroundColor: Colors.white,
        child: Container(
          padding: const EdgeInsets.all(20),
          constraints: BoxConstraints(
            maxHeight: Get.height * 0.8,
            maxWidth: Get.width * 0.9,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.teal.withOpacity(0.2),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(Icons.notifications_active,
                        color: Colors.teal, size: 20),
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'Avisos da Secretaria',
                      style: TextStyle(
                        fontFamily: 'Georgia',
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.teal,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.grey),
                    onPressed: () => Get.back(),
                  ),
                ],
              ),
              const Divider(),
              const SizedBox(height: 16),
              Expanded(
                child: controller.mensagens.isEmpty
                    ? const Center(
                        child: Text(
                          'Nenhuma mensagem recebida',
                          style: TextStyle(
                            fontFamily: 'Georgia',
                            fontSize: 16,
                            color: Colors.grey,
                          ),
                        ),
                      )
                    : ListView.separated(
                        itemCount: controller.mensagens.length,
                        separatorBuilder: (context, index) => const Divider(),
                        itemBuilder: (context, index) {
                          final msg = controller.mensagens[index];
                          return Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: index == 0
                                  ? Colors.teal.shade50
                                  : Colors.white,
                              borderRadius: BorderRadius.circular(8),
                              border: index == 0
                                  ? Border.all(color: Colors.teal.shade200)
                                  : null,
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        msg.titulo,
                                        style: TextStyle(
                                          fontFamily: 'Georgia',
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          color: index == 0
                                              ? Colors.teal
                                              : Colors.black87,
                                        ),
                                      ),
                                    ),
                                    if (index == 0)
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 8, vertical: 2),
                                        decoration: BoxDecoration(
                                          color: Colors.teal,
                                          borderRadius:
                                              BorderRadius.circular(12),
                                        ),
                                        child: const Text(
                                          'NOVO',
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 10,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  _formatDateTime(msg.dataEnvio),
                                  style: TextStyle(
                                    fontFamily: 'Georgia',
                                    fontSize: 12,
                                    color: Colors.grey[600],
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  msg.texto,
                                  style: const TextStyle(
                                    fontFamily: 'Georgia',
                                    fontSize: 14,
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTempoEstimado() {
    return Obx(() => Container(
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.7),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '${controller.tempoEstimado.value}',
                style: const TextStyle(
                  fontFamily: 'Georgia',
                  fontSize: 48,
                  fontWeight: FontWeight.bold,
                  color: Colors.teal,
                ),
              ),
              const SizedBox(width: 10),
              const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'minutos para',
                    style: TextStyle(
                      fontFamily: 'Georgia',
                      fontSize: 20,
                    ),
                  ),
                  Text(
                    'sua vez',
                    style: TextStyle(
                      fontFamily: 'Georgia',
                      fontSize: 20,
                    ),
                  ),
                ],
              ),
              const SizedBox(width: 10),
              Image.asset(
                'assets/session-timeout.png',
                width: 48,
                height: 48,
              ),
            ],
          ),
        ));
  }

  Widget _buildMedicoInfo() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.7),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          const Icon(
            Icons.medical_services_outlined,
            size: 32,
            color: Colors.teal,
          ),
          const SizedBox(height: 8),
          Text(
            'Você será atendido(a) por',
            style: TextStyle(
              fontFamily: 'Georgia',
              fontSize: 16,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Dr. ${controller.medicoNome.value}',
            style: const TextStyle(
              fontFamily: 'Georgia',
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            controller.especialidade.value,
            style: TextStyle(
              fontFamily: 'Georgia',
              fontSize: 16,
              color: Colors.grey[700],
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTempoDistancia() {
    return Obx(() => Container(
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.7),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '${controller.tempoDistancia.value}',
                style: const TextStyle(
                  fontFamily: 'Georgia',
                  fontSize: 36,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
              const SizedBox(width: 10),
              const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'minutos de',
                    style: TextStyle(
                      fontFamily: 'Georgia',
                      fontSize: 16,
                    ),
                  ),
                  Text(
                    'distância',
                    style: TextStyle(
                      fontFamily: 'Georgia',
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
              const SizedBox(width: 10),
              Image.asset(
                'assets/exercise.png',
                width: 36,
                height: 36,
              ),
            ],
          ),
        ));
  }

  Widget _buildSairFilaButton() {
    return ElevatedButton.icon(
      onPressed: controller.confirmarSaidaFila,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.red.shade50,
        foregroundColor: Colors.red,
        padding: const EdgeInsets.symmetric(
          horizontal: 24,
          vertical: 16,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(color: Colors.red.shade200),
        ),
      ),
      icon: const Icon(Icons.exit_to_app),
      label: const Text(
        'Sair da fila',
        style: TextStyle(
          fontFamily: 'Georgia',
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildBottomActions() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildActionButton(
          icon: 'assets/map.png',
          label: 'Ir até o local',
          onTap: controller.abrirLocalizacao,
        ),
        _buildActionButton(
          icon: 'assets/whats-app.png',
          label: 'Atendente',
          onTap: controller.abrirWhatsApp,
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required String icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(
          vertical: 12,
          horizontal: 24,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Image.asset(
              icon,
              width: 32,
              height: 32,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: const TextStyle(
                fontFamily: 'Georgia',
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
