import 'package:flutter/material.dart';
import '../controllers/password_recovery_controller.dart';
import 'package:fila_app/views/gradient_background.dart';
import 'package:get/get.dart';
import 'package:flutter/services.dart';

class EsqueceuSenha extends StatefulWidget {
  const EsqueceuSenha({super.key});

  @override
  EsqueceuSenhaState createState() => EsqueceuSenhaState();
}

class EsqueceuSenhaState extends State<EsqueceuSenha>
    with SingleTickerProviderStateMixin {
  final TextEditingController emailController = TextEditingController();
  final _passwordController = PasswordRecoveryController();
  final _formKey = GlobalKey<FormState>();

  bool _isLoading = false;
  bool _isEmailEmpty = true;
  late AnimationController _buttonController;
  late Animation<double> _buttonSqueezeAnimation;

  @override
  void initState() {
    super.initState();
    _buttonController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _buttonSqueezeAnimation = Tween(
      begin: 200.0,
      end: 70.0,
    ).animate(CurvedAnimation(
      parent: _buttonController,
      curve: Curves.easeInOut,
    ));

    // Add listener to email field to update button state
    emailController.addListener(_updateEmailState);
  }

  void _updateEmailState() {
    setState(() {
      _isEmailEmpty = emailController.text.trim().isEmpty;
    });
  }

  @override
  void dispose() {
    emailController.removeListener(_updateEmailState);
    emailController.dispose();
    _buttonController.dispose();
    super.dispose();
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Por favor, insira um e-mail';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return 'Por favor, insira um e-mail válido';
    }
    return null;
  }

  Future<void> _handlePasswordReset() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);
    await _buttonController.forward();

    // Provide haptic feedback
    HapticFeedback.mediumImpact();

    try {
      final result = await _passwordController
          .sendPasswordResetEmail(emailController.text.trim());

      if (!mounted) return;

      if (result['success']) {
        await _showSuccessDialog();
      } else {
        _showErrorDialog(result['error']);
      }
    } catch (e) {
      _showErrorDialog('Ocorreu um erro ao processar sua solicitação');
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
        await _buttonController.reverse();
      }
    }
  }

  Future<void> _showSuccessDialog() {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: const Column(
            children: [
              Icon(
                Icons.check_circle_outline,
                color: Colors.green,
                size: 58,
              ),
              SizedBox(height: 16),
              Text(
                'E-mail Enviado',
                style: TextStyle(
                  fontFamily: 'Georgia',
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          content: const Text(
            'Um e-mail com um link para redefinir sua senha foi enviado. Por favor, verifique sua caixa de entrada e acesse o link para criar uma nova senha. O link é válido por 24 horas.',
            textAlign: TextAlign.center,
            style: TextStyle(fontFamily: 'Georgia'),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Get.back();
                Get.toNamed('/login');
              },
              child: const Text(
                'Voltar para Login',
                style: TextStyle(
                  color: Colors.teal,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: const Column(
            children: [
              Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 58,
              ),
              SizedBox(height: 16),
              Text(
                'Erro',
                style: TextStyle(
                  fontFamily: 'Georgia',
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          content: Text(
            message,
            textAlign: TextAlign.center,
            style: const TextStyle(fontFamily: 'Georgia'),
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text(
                'OK',
                style: TextStyle(
                  color: Colors.teal,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;

    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: GradientBackground(
        child: SafeArea(
          child: Stack(
            children: [
              // Conteúdo principal
              SingleChildScrollView(
                physics: const ClampingScrollPhysics(),
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    minHeight: size.height - MediaQuery.of(context).padding.top,
                  ),
                  child: IntrinsicHeight(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 30.0),
                      child: Form(
                        key: _formKey,
                        child: Column(
                          children: [
                            // Área superior - Logo e título
                            const SizedBox(height: 20),
                            const Text(
                              'Recuperação de Senha',
                              style: TextStyle(
                                fontFamily: 'Georgia',
                                fontWeight: FontWeight.bold,
                                fontSize: 28,
                                color: Colors.black,
                              ),
                              textAlign: TextAlign.center,
                            ),

                            // Área central - Conteúdo expansível
                            Expanded(
                              child: Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 20.0),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Card(
                                      elevation: 2,
                                      margin:
                                          EdgeInsets.symmetric(vertical: 10),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(15)),
                                      ),
                                      child: Padding(
                                        padding: EdgeInsets.all(16.0),
                                        child: Text(
                                          'Insira seu e-mail cadastrado para receber as instruções de recuperação de senha. Verifique também sua caixa de spam.',
                                          style: TextStyle(
                                            fontFamily: 'Georgia',
                                            fontSize: 16,
                                            color: Colors.black87,
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(height: 30),
                                    _buildEmailField(),
                                  ],
                                ),
                              ),
                            ),

                            // Área inferior - Botões
                            AnimatedContainer(
                              duration: const Duration(milliseconds: 300),
                              padding: EdgeInsets.only(
                                bottom: keyboardHeight > 0
                                    ? keyboardHeight + 20
                                    : 20,
                              ),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  _buildSubmitButton(),
                                  const SizedBox(height: 16),
                                  _buildCancelButton(),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmailField() {
    return TextFormField(
      controller: emailController,
      keyboardType: TextInputType.emailAddress,
      style: const TextStyle(
        fontFamily: 'Georgia',
        fontSize: 16,
      ),
      decoration: InputDecoration(
        labelText: 'E-mail',
        hintText: 'Digite seu e-mail cadastrado',
        prefixIcon: const Icon(Icons.email_outlined, color: Colors.teal),
        suffixIcon: emailController.text.isNotEmpty
            ? IconButton(
                icon: const Icon(Icons.clear, color: Colors.grey),
                onPressed: () {
                  emailController.clear();
                  setState(() {
                    _isEmailEmpty = true;
                  });
                },
              )
            : null,
        labelStyle: const TextStyle(
          color: Colors.black87,
          fontFamily: 'Georgia',
          fontSize: 18,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.teal),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.teal, width: 2),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.teal.withOpacity(0.5)),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.red),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        errorStyle: const TextStyle(
          fontFamily: 'Georgia',
        ),
        filled: true,
        fillColor: Colors.white.withOpacity(0.9),
      ),
      validator: _validateEmail,
      enabled: !_isLoading,
      textInputAction: TextInputAction.done,
      onFieldSubmitted: (_) {
        if (!_isEmailEmpty && !_isLoading) {
          _handlePasswordReset();
        }
      },
    );
  }

  Widget _buildSubmitButton() {
    return AnimatedBuilder(
      animation: _buttonSqueezeAnimation,
      builder: (context, child) {
        return SizedBox(
          width: _buttonSqueezeAnimation.value,
          height: 50,
          child: ElevatedButton(
            onPressed:
                (_isLoading || _isEmailEmpty) ? null : _handlePasswordReset,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal,
              disabledBackgroundColor: Colors.teal.withOpacity(0.4),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
              elevation: 3,
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 24,
                    width: 24,
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      strokeWidth: 2,
                    ),
                  )
                : const Text(
                    'Enviar',
                    style: TextStyle(
                      fontFamily: 'Georgia',
                      fontSize: 20,
                      color: Colors.white,
                    ),
                  ),
          ),
        );
      },
    );
  }

  Widget _buildCancelButton() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 40),
      child: TextButton.icon(
        onPressed: _isLoading ? null : () => Get.back(),
        icon: const Icon(Icons.arrow_back, size: 18, color: Colors.teal),
        label: const Text(
          'Voltar',
          style: TextStyle(
            fontFamily: 'Georgia',
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.teal,
          ),
        ),
        style: TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
            side: const BorderSide(color: Colors.teal, width: 1),
          ),
        ),
      ),
    );
  }
}
