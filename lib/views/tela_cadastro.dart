import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fila_app/views/gradient_background.dart';

class TelaDeCadastro extends StatefulWidget {
  const TelaDeCadastro({super.key});

  @override
  State<TelaDeCadastro> createState() => _TelaDeCadastroState();
}

class _TelaDeCadastroState extends State<TelaDeCadastro> {
  Map<String, String>? _dadosCadastro;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final args = ModalRoute.of(context)?.settings.arguments;
    if (args != null) {
      _dadosCadastro = (args as Map<String, dynamic>).map(
        (key, value) => MapEntry(key.toString(), value.toString()),
      );
    }

    if (_dadosCadastro == null || _dadosCadastro!.isEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Navigator.of(context).pushReplacementNamed('/primeiroAcesso');
      });
    }
  }

  void _handleTypeSelection(String tipoSelecionado) {
    if (_dadosCadastro == null) return;

    final dadosCompletos = {
      ..._dadosCadastro!,
      'tipo': tipoSelecionado,
    };

    if (tipoSelecionado == 'medico') {
      Get.toNamed(
        '/cadastroMedico',
        arguments: dadosCompletos,
      );
    } else {
      Get.toNamed(
        '/cadastroHospital',
        arguments: dadosCompletos,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // ignore: deprecated_member_use
    return WillPopScope(
      onWillPop: () async {
        if (_dadosCadastro != null) {
          final shouldPop = await showDialog<bool>(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Deseja voltar?'),
              content: const Text(
                  'Os dados preenchidos anteriormente serão mantidos.'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context, false),
                  child: const Text('Não'),
                ),
                TextButton(
                  onPressed: () => Navigator.pop(context, true),
                  child: const Text('Sim'),
                ),
              ],
            ),
          );
          return shouldPop ?? false;
        }
        return true;
      },
      child: Scaffold(
        body: GradientBackground(
          child: SafeArea(
            child: ListView(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              children: [
                const SizedBox(height: 50),
                Align(
                  alignment: Alignment.centerLeft,
                  child: IconButton(
                    icon: Image.asset('assets/left.png'),
                    iconSize: 40,
                    onPressed: () => Navigator.maybePop(context),
                  ),
                ),
                const SizedBox(height: 20),
                const Text(
                  'Selecione o tipo de cadastro',
                  style: TextStyle(
                    fontFamily: 'Georgia',
                    fontSize: 32,
                    fontWeight: FontWeight.normal,
                    color: Colors.black,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 50),
                _buildTypeOption(
                  text: 'MÉDICO',
                  imagePath:
                      'assets/logo-administrador-removebg-preview-1-5.png',
                  onTap: () => _handleTypeSelection('medico'),
                ),
                const SizedBox(height: 30),
                _buildTypeOption(
                  text: 'HOSPITAL',
                  imagePath:
                      'assets/logo-administrador-removebg-preview-1-5.png',
                  onTap: () => _handleTypeSelection('consultorio'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTypeOption({
    required String text,
    required String imagePath,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(10),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(
                imagePath,
                width: 76,
                height: 81,
                fit: BoxFit.contain,
              ),
              const SizedBox(height: 10),
              Text(
                text,
                style: const TextStyle(
                  fontFamily: 'Georgia',
                  fontSize: 36,
                  fontWeight: FontWeight.normal,
                  color: Colors.black,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
