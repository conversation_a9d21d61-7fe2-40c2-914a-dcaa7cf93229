import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fila_app/controllers/fila_medico_controller.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';

class TelaFilaMedico extends StatefulWidget {
  const TelaFilaMedico({super.key});

  @override
  State<TelaFilaMedico> createState() => _TelaFilaMedicoState();
}

class _TelaFilaMedicoState extends State<TelaFilaMedico> {
  final FilaMedicoController controller = Get.find<FilaMedicoController>();

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) async {
        if (didPop) return;
        controller.refreshTimer?.cancel();
        final shouldExit = await _confirmarSaida();
        if (shouldExit) {
          if (context.mounted) {
            Navigator.of(context).pop();
          }
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text(
            'Minha Fila de Pacientes',
            style: TextStyle(fontFamily: 'Georgia', color: Colors.white),
          ),
          backgroundColor: Colors.teal,
          actions: [
            IconButton(
              icon: const Icon(Icons.refresh, color: Colors.white),
              onPressed: () => controller.carregarFilaPacientes(),
            ),
            IconButton(
              icon: const Icon(Icons.logout, color: Colors.white),
              onPressed: () => _handleLogout(),
            ),
          ],
        ),
        body: Obx(() {
          if (controller.isLoading.value &&
              controller.pacientesNaFila.isEmpty) {
            return const Center(child: CircularProgressIndicator());
          }

          if (controller.error.value.isNotEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 48, color: Colors.red),
                  const SizedBox(height: 16),
                  Text(
                    controller.error.value,
                    style: const TextStyle(
                      fontFamily: 'Georgia',
                      fontSize: 16,
                      color: Colors.red,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () => controller.carregarFilaPacientes(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.teal,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Tentar novamente'),
                  ),
                ],
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: () => controller.carregarFilaPacientes(),
            child: Column(
              children: [
                // Metrics Card
                _buildMetricasCard(),

                // Lista de pacientes
                Expanded(
                  child: controller.pacientesNaFila.isEmpty
                      ? _buildFilaVazia()
                      : _buildListaPacientes(),
                ),
              ],
            ),
          );
        }),
        floatingActionButton: _buildEnviarMensagemButton(),
      ),
    );
  }

  Future<bool> _confirmarSaida() async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Confirmar saída'),
            content: const Text(
                'Deseja realmente sair? A atualização automática da fila será interrompida.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancelar'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('Sair'),
              ),
            ],
          ),
        ) ??
        false;
  }

  Future<void> _handleLogout() async {
    final confirm = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Confirmar logout'),
            content: const Text('Deseja realmente sair da sua conta?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancelar'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('Sair'),
              ),
            ],
          ),
        ) ??
        false;

    if (confirm) {
      controller.refreshTimer?.cancel();
      final currentUser = await ParseUser.currentUser();
      if (currentUser != null) {
        await currentUser.logout();
      }
      Get.offAllNamed('/login');
    }
  }

  Widget _buildMetricasCard() {
    return Obx(() {
      return Card(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Colors.blue.shade50, Colors.blue.shade100],
            ),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Métricas de Atendimento',
                    style: TextStyle(
                      fontFamily: 'Georgia',
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.refresh, color: Colors.blue),
                    onPressed: () => controller.obterMetricas(),
                  ),
                ],
              ),
              const Divider(color: Colors.blue),
              const SizedBox(height: 8),
              _buildMetricasGrid(),
              const SizedBox(height: 16),
              _buildMetricasTempo(),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildMetricasGrid() {
    return GridView.count(
      crossAxisCount: 2,
      childAspectRatio: 2.5,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      children: [
        _buildMetricaItem(
          'Pacientes Hoje',
          controller.metricas.value.totalAtendimentos.toString(),
          Icons.people,
          Colors.blue,
        ),
        _buildMetricaItem(
          'Na Fila',
          controller.pacientesNaFila.length.toString(),
          Icons.queue,
          Colors.orange,
        ),
        _buildMetricaItem(
          'Tempo Médio',
          '${controller.metricas.value.tempoMedioAtendimento.toStringAsFixed(1)} min',
          Icons.access_time,
          Colors.green,
        ),
        _buildMetricaItem(
          'Espera Atual',
          '${controller.metricas.value.tempoMedioEspera.toStringAsFixed(1)} min',
          Icons.hourglass_empty,
          Colors.red,
        ),
      ],
    );
  }

  Widget _buildMetricaItem(
      String title, String value, IconData icon, Color color) {
    return Container(
      margin: const EdgeInsets.all(4),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.2),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child: Icon(icon, color: color, size: 16),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontFamily: 'Georgia',
                    fontSize: 12,
                    color: Colors.grey[700],
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontFamily: 'Georgia',
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetricasTempo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Análise de Tempo de Espera',
          style: TextStyle(
            fontFamily: 'Georgia',
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.blue,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildTempoProgressCard(
                'Tempo médio de espera',
                controller.metricas.value.tempoMedioEspera.toDouble(),
                30, // valor de referência (30 min)
                Colors.orange,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildTempoProgressCard(
                'Tempo médio de atendimento',
                controller.metricas.value.tempoMedioAtendimento.toDouble(),
                15, // valor de referência (15 min)
                Colors.blue,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        const Text(
          'Eficiência de Atendimento',
          style: TextStyle(
            fontFamily: 'Georgia',
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.blue,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.blue.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Taxa de Eficiência',
                    style: TextStyle(
                      fontFamily: 'Georgia',
                      fontSize: 14,
                      color: Colors.black87,
                    ),
                  ),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getEficienciaColor(
                              controller.calcularEficienciaAtendimento())
                          .withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${(controller.calcularEficienciaAtendimento() * 100).toStringAsFixed(0)}%',
                      style: TextStyle(
                        fontFamily: 'Georgia',
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: _getEficienciaColor(
                            controller.calcularEficienciaAtendimento()),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              LinearProgressIndicator(
                value: controller.calcularEficienciaAtendimento(),
                backgroundColor: Colors.grey.shade200,
                valueColor: AlwaysStoppedAnimation<Color>(
                  _getEficienciaColor(
                      controller.calcularEficienciaAtendimento()),
                ),
                minHeight: 8,
                borderRadius: BorderRadius.circular(4),
              ),
              const SizedBox(height: 8),
              Text(
                _getEficienciaMessage(
                    controller.calcularEficienciaAtendimento()),
                style: const TextStyle(
                  fontFamily: 'Georgia',
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTempoProgressCard(
      String title, double value, double reference, Color color) {
    final percent = value <= reference ? value / reference : 1.0;
    final isOverReference = value > reference;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontFamily: 'Georgia',
              fontSize: 12,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${value.toStringAsFixed(1)} min',
                style: TextStyle(
                  fontFamily: 'Georgia',
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isOverReference ? Colors.red : color,
                ),
              ),
              Text(
                'Ref: $reference min',
                style: TextStyle(
                  fontFamily: 'Georgia',
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: percent,
            backgroundColor: Colors.grey.shade200,
            valueColor: AlwaysStoppedAnimation<Color>(
              isOverReference ? Colors.red : color,
            ),
            minHeight: 6,
            borderRadius: BorderRadius.circular(3),
          ),
          if (isOverReference)
            Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Row(
                children: [
                  const Icon(Icons.warning, color: Colors.amber, size: 12),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      'Acima do tempo ideal',
                      style: TextStyle(
                        fontFamily: 'Georgia',
                        fontSize: 10,
                        color: Colors.amber[800],
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  double calcularEficienciaAtendimento() {
    // Adaptar para usar os valores do controller quando disponíveis
    return controller.calcularEficienciaAtendimento();
  }

  Color _getEficienciaColor(double valor) {
    if (valor >= 0.75) return Colors.green;
    if (valor >= 0.5) return Colors.amber;
    return Colors.red;
  }

  String _getEficienciaMessage(double valor) {
    if (valor >= 0.75) return 'Excelente ritmo de atendimento';
    if (valor >= 0.5) return 'Bom ritmo, mas pode melhorar';
    return 'Considere otimizar o tempo de atendimento';
  }

  Widget _buildFilaVazia() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.check_circle_outline,
            size: 64,
            color: Colors.teal.withOpacity(0.6),
          ),
          const SizedBox(height: 16),
          const Text(
            'Sua fila está vazia',
            style: TextStyle(
              fontFamily: 'Georgia',
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.teal,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Não há pacientes aguardando atendimento',
            style: TextStyle(
              fontFamily: 'Georgia',
              fontSize: 16,
              color: Colors.grey[700],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildListaPacientes() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: controller.pacientesNaFila.length,
      itemBuilder: (context, index) {
        final paciente = controller.pacientesNaFila[index];
        return _buildCardPacienteFila(paciente);
      },
    );
  }

  Widget _buildCardPacienteFila(ParseObject paciente) {
    final nome = paciente.get<String>('nome_paciente') ?? 'Paciente';
    final posicao = paciente.get<int>('posicao') ?? 0;
    final status = paciente.get<String>('status') ?? 'aguardando';
    final isEmAtendimento = status == 'em_atendimento';
    final dataEntrada = paciente.get<DateTime>('data_entrada');
    final tempoEspera =
        dataEntrada != null ? _calcularTempoEspera(dataEntrada) : 0;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: isEmAtendimento ? Colors.blue.shade50 : Colors.white,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              decoration: BoxDecoration(
                color: isEmAtendimento ? Colors.blue : Colors.teal,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    isEmAtendimento ? Icons.medical_services : Icons.person,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    isEmAtendimento ? 'Em Atendimento' : 'Aguardando',
                    style: const TextStyle(
                      fontFamily: 'Georgia',
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'Posição: $posicao',
                      style: const TextStyle(
                        fontFamily: 'Georgia',
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      CircleAvatar(
                        backgroundColor: Colors.grey.shade200,
                        radius: 24,
                        child: const Icon(
                          Icons.person,
                          color: Colors.grey,
                          size: 32,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              nome,
                              style: const TextStyle(
                                fontFamily: 'Georgia',
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              'Esperando há: ${_formatarTempoEspera(tempoEspera)}',
                              style: TextStyle(
                                fontFamily: 'Georgia',
                                fontSize: 14,
                                color: Colors.grey[700],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildActionButton(
                        isEmAtendimento ? 'Finalizar' : 'Iniciar Atendimento',
                        isEmAtendimento
                            ? Icons.check_circle
                            : Icons.play_circle,
                        isEmAtendimento ? Colors.green : Colors.blue,
                        () => isEmAtendimento
                            ? _handleFinalizarAtendimento(paciente)
                            : _handleIniciarAtendimento(paciente),
                      ),
                      _buildActionButton(
                        'Enviar Mensagem',
                        Icons.message,
                        Colors.teal,
                        () => _mostrarFormularioMensagemIndividual(paciente),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(
      String text, IconData icon, Color color, VoidCallback onPressed) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, color: Colors.white, size: 16),
      label: Text(
        text,
        style: const TextStyle(
          fontFamily: 'Georgia',
          color: Colors.white,
        ),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  String _formatarTempoEspera(int minutos) {
    if (minutos < 60) {
      return '$minutos minutos';
    } else {
      final horas = minutos ~/ 60;
      final min = minutos % 60;
      return '${horas.toInt()} h ${min > 0 ? '${min.toInt()} min' : ''}';
    }
  }

  Widget _buildEnviarMensagemButton() {
    return FloatingActionButton.extended(
      onPressed: _mostrarOpcoesMensagem,
      backgroundColor: Colors.teal,
      label: const Text(
        'Enviar Mensagem',
        style: TextStyle(
          fontFamily: 'Georgia',
          color: Colors.white,
        ),
      ),
      icon: const Icon(Icons.message, color: Colors.white),
    );
  }

  void _mostrarOpcoesMensagem() {
    if (controller.pacientesNaFila.isEmpty) {
      Get.snackbar(
        'Atenção',
        'Não há pacientes na fila para enviar mensagem',
        backgroundColor: Colors.amber,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
        margin: const EdgeInsets.all(16),
      );
      return;
    }

    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Enviar Mensagem',
              style: TextStyle(
                fontFamily: 'Georgia',
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.people, color: Colors.teal),
              title: const Text('Para todos os pacientes'),
              onTap: () {
                Navigator.pop(context);
                _mostrarFormularioMensagemTodos();
              },
            ),
            ListTile(
              leading: const Icon(Icons.person, color: Colors.blue),
              title: const Text('Para um paciente específico'),
              onTap: () {
                Navigator.pop(context);
                _mostrarSelecaoPaciente();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _mostrarFormularioMensagemTodos() {
    final controller = TextEditingController();
    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Mensagem para todos',
                style: TextStyle(
                  fontFamily: 'Georgia',
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: controller,
                decoration: const InputDecoration(
                  hintText: 'Digite sua mensagem...',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Get.back(),
                    child: const Text('Cancelar'),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      if (controller.text.trim().isNotEmpty) {
                        this
                            .controller
                            .enviarMensagemTodos(controller.text.trim());
                        Get.back();
                        Get.snackbar(
                          'Sucesso',
                          'Mensagem enviada para todos os pacientes',
                          backgroundColor: Colors.green,
                          colorText: Colors.white,
                          snackPosition: SnackPosition.BOTTOM,
                        );
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.teal,
                    ),
                    child: const Text('Enviar'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _mostrarSelecaoPaciente() {
    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          padding: const EdgeInsets.all(16),
          constraints: BoxConstraints(
            maxHeight: Get.height * 0.7,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Selecione um paciente',
                style: TextStyle(
                  fontFamily: 'Georgia',
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: controller.pacientesNaFila.length,
                  itemBuilder: (context, index) {
                    final paciente = controller.pacientesNaFila[index];
                    final nome =
                        paciente.get<String>('nome_paciente') ?? 'Paciente';
                    return ListTile(
                      title: Text(nome),
                      subtitle: Text(
                        'Posição: ${paciente.get<int>('posicao') ?? 0}',
                      ),
                      onTap: () {
                        Get.back();
                        _mostrarFormularioMensagemIndividual(paciente);
                      },
                    );
                  },
                ),
              ),
              const SizedBox(height: 16),
              TextButton(
                onPressed: () => Get.back(),
                child: const Text('Cancelar'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _mostrarFormularioMensagemIndividual(ParseObject paciente) {
    final controller = TextEditingController();
    final nome = paciente.get<String>('nome_paciente') ?? 'Paciente';

    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Mensagem para $nome',
                style: const TextStyle(
                  fontFamily: 'Georgia',
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: controller,
                decoration: const InputDecoration(
                  hintText: 'Digite sua mensagem...',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Get.back(),
                    child: const Text('Cancelar'),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      if (controller.text.trim().isNotEmpty) {
                        this.controller.enviarMensagemIndividual(
                              paciente,
                              controller.text.trim(),
                            );
                        Get.back();
                        Get.snackbar(
                          'Sucesso',
                          'Mensagem enviada para $nome',
                          backgroundColor: Colors.green,
                          colorText: Colors.white,
                          snackPosition: SnackPosition.BOTTOM,
                        );
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.teal,
                    ),
                    child: const Text('Enviar'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  int _calcularTempoEspera(DateTime? dataEntrada) {
    if (dataEntrada == null) {
      return 0;
    }

    final agora = DateTime.now();
    final diferenca = agora.difference(dataEntrada);
    return diferenca.inMinutes;
  }

  void _handleIniciarAtendimento(ParseObject paciente) async {
    final confirm = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Confirmar início'),
            content: const Text('Deseja iniciar o atendimento deste paciente?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancelar'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('Iniciar'),
              ),
            ],
          ),
        ) ??
        false;

    if (confirm) {
      await controller.iniciarAtendimento(paciente);
      if (controller.successMessage.value.isNotEmpty) {
        _mostrarMensagem(controller.successMessage.value);
      }
    }
  }

  void _handleFinalizarAtendimento(ParseObject paciente) async {
    final confirm = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Confirmar finalização'),
            content:
                const Text('Deseja finalizar o atendimento deste paciente?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancelar'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('Finalizar'),
              ),
            ],
          ),
        ) ??
        false;

    if (confirm) {
      await controller.finalizarAtendimento(paciente);
      if (controller.successMessage.value.isNotEmpty) {
        _mostrarMensagem(controller.successMessage.value);
      }
    }
  }

  void _mostrarMensagem(String mensagem, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          mensagem,
          style: const TextStyle(fontFamily: 'Georgia'),
        ),
        backgroundColor: isError ? Colors.red : Colors.teal,
        duration: const Duration(seconds: 3),
      ),
    );
  }
}
