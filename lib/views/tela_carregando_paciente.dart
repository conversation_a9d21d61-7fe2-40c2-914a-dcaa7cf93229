import 'dart:async';
import 'package:flutter/material.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'package:fila_app/views/gradient_background.dart';
import 'package:fila_app/controllers/paciente_controller.dart';
import 'package:get/get.dart';

class TelaCarregandoPaciente extends StatefulWidget {
  final String solicitacaoId;
  final String medicoId;

  const TelaCarregandoPaciente({
    super.key,
    required this.solicitacaoId,
    required this.medicoId,
  });

  @override
  TelaCarregandoPacienteState createState() => TelaCarregandoPacienteState();
}

class TelaCarregandoPacienteState extends State<TelaCarregandoPaciente> {
  final PacienteController controller = Get.find<PacienteController>();
  bool _isCanceling = false;
  Timer? _refreshTimer;
  final String _status = 'aguardando';
  int _tempoEspera = 0;
  String? _idPaciente;
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    _iniciarMonitoramento();
    _carregarDadosSolicitacao();
    _iniciarContadorTempo();
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  Future<void> _carregarDadosSolicitacao() async {
    try {
      final query = QueryBuilder<ParseObject>(ParseObject('FilaSolicitacao'))
        ..whereEqualTo('objectId', widget.solicitacaoId);

      final response = await query.query();

      if (response.success &&
          response.results != null &&
          response.results!.isNotEmpty) {
        final solicitacao = response.results!.first;
        setState(() {
          _idPaciente = solicitacao.get<String>('idPaciente');
        });
      }
    } catch (e) {
      debugPrint('Erro ao carregar dados da solicitação: $e');
    }
  }

  void _iniciarMonitoramento() {
    _refreshTimer = Timer.periodic(const Duration(seconds: 3), (_) {
      _verificarStatusSolicitacao();
    });
  }

  void _iniciarContadorTempo() {
    Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted && !_isCanceling) {
        setState(() {
          _tempoEspera++;
        });
      }
    });
  }

  Future<void> _verificarStatusSolicitacao() async {
    setState(() => isLoading = true);

    try {
      // Obter dados do usuário
      final userData = await controller.userDataController.getUserData();
      if (userData == null) {
        throw Exception('Dados do usuário não encontrados.');
      }

      final idPaciente = userData['userId'];

      // Buscar a solicitação usando o ID
      final querySolicitacao =
          QueryBuilder<ParseObject>(ParseObject('FilaSolicitacao'))
            ..whereEqualTo('solicitacao_id', widget.solicitacaoId)
            ..whereEqualTo('idPaciente', idPaciente);

      final response = await querySolicitacao.query();

      if (!response.success ||
          response.results == null ||
          response.results!.isEmpty) {
        throw Exception('Solicitação não encontrada.');
      }

      final solicitacao = response.results!.first;
      final status = solicitacao.get<String>('status') ?? 'pendente';

      if (status == 'aprovado' || status == 'finalizada') {
        _redirecionarParaFila(solicitacao);
      } else if (status == 'rejeitado') {
        _redirecionarParaHomeComMensagem(
            'Sua solicitação foi recusada pelo consultório.');
      } else {
        // Verifica se já existe entrada na fila
        await _verificarFilaExistente(idPaciente);
      }
    } catch (e) {
      debugPrint('Erro ao verificar status: $e');
      _mostrarErro('Erro ao verificar status da solicitação.');
    } finally {
      if (mounted) {
        setState(() => isLoading = false);
      }
    }
  }

  Future<void> _verificarFilaExistente(String idPaciente) async {
    try {
      // Verificar se já existe na classe Fila
      final queryFila = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('idPaciente', idPaciente)
        ..whereContainedIn('status', ['aguardando', 'em_atendimento']);

      final response = await queryFila.query();

      if (response.success &&
          response.results != null &&
          response.results!.isNotEmpty) {
        final fila = response.results!.first;

        // Buscar dados do médico para a navegação
        final medico = fila.get<ParseObject>('medico');
        String? medicoNome;
        String? especialidade;

        if (medico != null) {
          await medico.fetch();
          medicoNome = medico.get<String>('nome');
          especialidade = medico.get<String>('especialidade');
        }

        // Navegar para a tela da fila
        if (!mounted) return;
        Navigator.pushReplacementNamed(
          context,
          '/fila_paciente',
          arguments: {
            'filaId': fila.objectId!,
            'posicao': fila.get<int>('posicao'),
            'medicoNome': medicoNome,
            'especialidade': especialidade,
          },
        );
      }
    } catch (e) {
      debugPrint('Erro ao verificar fila existente: $e');
    }
  }

  Future<void> _redirecionarParaFila(ParseObject solicitacao) async {
    try {
      final query = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('solicitacao', solicitacao)
        ..whereEqualTo('status', 'aguardando');

      final response = await query.query();

      if (!mounted) return;

      if (response.success &&
          response.results != null &&
          response.results!.isNotEmpty) {
        final fila = response.results!.first;
        final medico = solicitacao.get<ParseObject>('medicoId');

        Navigator.pushReplacementNamed(
          context,
          '/fila_paciente',
          arguments: {
            'filaId': fila.objectId!,
            'posicao': fila.get<int>('posicao'),
            'medicoNome': medico?.get<String>('nome'),
            'especialidade': medico?.get<String>('especialidade'),
          },
        );
      }
    } catch (e) {
      debugPrint('Erro ao redirecionar para fila: $e');
      _mostrarErro('Erro ao entrar na fila');
    }
  }

  Future<void> _cancelarSolicitacao([bool rejeitada = false]) async {
    if (_isCanceling) return;

    setState(() => _isCanceling = true);

    try {
      // Obter dados do usuário para verificação
      final userData = await controller.userDataController.getUserData();
      if (userData == null) {
        throw Exception('Dados do usuário não encontrados.');
      }

      final idPaciente = userData['userId'];

      if (rejeitada) {
        // Se foi rejeitada, apenas registramos o cancelamento local
        final cancelamento = ParseObject('SolicitacaoCancelamento')
          ..set('solicitacao_id', widget.solicitacaoId)
          ..set('motivo', 'rejeitado')
          ..set('tempo_espera', _tempoEspera);

        await cancelamento.save();
      } else {
        // Usar a Cloud Function para cancelar
        final params = <String, dynamic>{
          'solicitacaoId': widget.solicitacaoId,
          'deviceId': idPaciente
        };

        debugPrint(
            'Chamando Cloud Function para cancelar solicitação: $params');
        final response = await ParseCloudFunction('cancelarSolicitacaoPaciente')
            .execute(parameters: params);

        if (!response.success) {
          final errorMessage =
              response.error?.message ?? 'Erro ao cancelar solicitação';
          debugPrint('Erro retornado pela Cloud Function: $errorMessage');
          throw Exception(errorMessage);
        } else {
          debugPrint('Solicitação cancelada com sucesso: ${response.result}');
        }
      }

      if (!mounted) return;

      Navigator.of(context).pushNamedAndRemoveUntil('/', (route) => false);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            rejeitada
                ? 'Sua solicitação foi rejeitada'
                : 'Solicitação cancelada',
          ),
          backgroundColor: rejeitada ? Colors.red : Colors.orange,
        ),
      );
    } catch (e) {
      debugPrint('Erro ao cancelar solicitação: $e');
      if (mounted) {
        setState(() => _isCanceling = false);
        _mostrarErro('Erro ao cancelar solicitação: $e');
      }
    }
  }

  void _redirecionarParaHomeComMensagem(String mensagem) {
    Navigator.of(context).pushNamedAndRemoveUntil('/', (route) => false);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(mensagem),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _mostrarErro(String mensagem) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(mensagem),
        backgroundColor: Colors.red,
      ),
    );
  }

  String _formatarTempoEspera() {
    final minutos = (_tempoEspera / 60).floor();
    final segundos = _tempoEspera % 60;
    return '${minutos.toString().padLeft(2, '0')}:${segundos.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => false,
      child: Scaffold(
        body: GradientBackground(
          applyStatusBarStyles: true,
          child: Container(
            width: double.infinity,
            height: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (_idPaciente != null) _buildIdPaciente(),
                  const SizedBox(height: 30),
                  _buildLoadingIndicator(),
                  const SizedBox(height: 30),
                  Text(
                    _getMensagemStatus(),
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontFamily: 'Georgia',
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'Tempo de espera: ${_formatarTempoEspera()}',
                    style: const TextStyle(
                      fontFamily: 'Georgia',
                      fontSize: 18,
                    ),
                  ),
                  const SizedBox(height: 50),
                  _buildCancelButton(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildIdPaciente() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 30),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.85),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 5,
            spreadRadius: 1,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Seu ID',
            style: TextStyle(
              fontFamily: 'Georgia',
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 5),
          Text(
            _idPaciente!,
            style: const TextStyle(
              fontFamily: 'Georgia',
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    switch (_status) {
      case 'aprovado':
      case 'preenchendo_dados':
        return const Icon(
          Icons.check_circle_outline,
          size: 80,
          color: Colors.green,
        );
      case 'rejeitado':
        return const Icon(
          Icons.cancel_outlined,
          size: 80,
          color: Colors.red,
        );
      default:
        return const CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF5CC9C4)),
          strokeWidth: 8,
        );
    }
  }

  Widget _buildCancelButton() {
    return TextButton(
      onPressed: _isCanceling ? null : () => _cancelarSolicitacao(),
      child: _isCanceling
          ? const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                  ),
                ),
                SizedBox(width: 10),
                Text(
                  'Cancelando...',
                  style: TextStyle(
                    fontFamily: 'Georgia',
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.black54,
                  ),
                ),
              ],
            )
          : const Text(
              'Cancelar',
              style: TextStyle(
                fontFamily: 'Georgia',
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black,
                decoration: TextDecoration.underline,
              ),
            ),
    );
  }

  String _getMensagemStatus() {
    switch (_status) {
      case 'aprovado':
        return 'Solicitação Aprovada!\nAguardando dados serem preenchidos...';
      case 'preenchendo_dados':
        return 'Aguardando secretária\npreencher seus dados...';
      case 'rejeitado':
        return 'Solicitação Rejeitada';
      default:
        return 'Aguardando aprovação\nda secretária...';
    }
  }
}
