import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:fila_app/controllers/administrador_controller.dart';
import 'package:fila_app/views/gradient_background.dart';
import 'package:geolocator/geolocator.dart';
import 'package:fila_app/widgets/back_arrow_widget.dart';

class TelaDadosAdministrador extends StatefulWidget {
  const TelaDadosAdministrador({super.key});

  @override
  State<TelaDadosAdministrador> createState() => _TelaDadosAdministradorState();
}

class _TelaDadosAdministradorState extends State<TelaDadosAdministrador> {
  final _formKey = GlobalKey<FormState>();
  final _controller = AdministradorController();
  bool _isLoading = false;
  bool _isEditing = false;

  final _nomeController = TextEditingController();
  final _emailController = TextEditingController();
  final _cpfCnpjController = TextEditingController();
  final _telefoneController = TextEditingController();
  bool _ativo = true;

  LatLng? _selectedLocation;
  Set<Marker> _markers = {};
  String? _locationError;

  @override
  void initState() {
    super.initState();
    _carregarDados();
  }

  Future<void> _carregarDados() async {
    setState(() => _isLoading = true);
    try {
      final dados = await _controller.buscarDadosConsultorio();

      _nomeController.text = dados['nome'] ?? '';
      _emailController.text = dados['email'] ?? '';
      _cpfCnpjController.text = dados['cpfCnpj'] ?? '';
      _telefoneController.text = dados['telefone'] ?? '';
      _ativo = dados['ativo'] ?? true;

      if (dados['latitude'] != null && dados['longitude'] != null) {
        _selectedLocation = LatLng(
          dados['latitude']!.toDouble(),
          dados['longitude']!.toDouble(),
        );
        _updateMarker();
      } else {
        await _getCurrentLocation();
      }
    } catch (e) {
      _mostrarErro('Erro ao carregar dados: $e');
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  Future<void> _getCurrentLocation() async {
    try {
      final permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        final newPermission = await Geolocator.requestPermission();
        if (newPermission == LocationPermission.denied) {
          throw Exception('Permissão de localização negada');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw Exception('Permissão de localização permanentemente negada');
      }

      final position = await Geolocator.getCurrentPosition();
      setState(() {
        _selectedLocation = LatLng(position.latitude, position.longitude);
        _updateMarker();
        _locationError = null;
      });
    } catch (e) {
      setState(() {
        _locationError = e.toString();
      });
    }
  }

  void _updateMarker() {
    if (_selectedLocation == null) return;
    setState(() {
      _markers = {
        Marker(
          markerId: const MarkerId('hospital'),
          position: _selectedLocation!,
          draggable: _isEditing,
          onDragEnd: _isEditing
              ? (newPosition) {
                  setState(() {
                    _selectedLocation = newPosition;
                  });
                }
              : null,
        ),
      };
    });
  }

  Future<void> _salvarDados() async {
    if (!_formKey.currentState!.validate() || _selectedLocation == null) return;

    setState(() => _isLoading = true);
    try {
      await _controller.atualizarDadosConsultorio({
        'nome': _nomeController.text,
        'email': _emailController.text,
        'cpfCnpj': _cpfCnpjController.text,
        'telefone': _telefoneController.text,
        'latitude': _selectedLocation!.latitude,
        'longitude': _selectedLocation!.longitude,
        'ativo': _ativo,
      });

      if (mounted) {
        setState(() => _isEditing = false);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Dados atualizados com sucesso!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      _mostrarErro('Erro ao salvar: $e');
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  void _mostrarErro(String mensagem) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(mensagem), backgroundColor: Colors.red),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: const BackArrowWidget(
          iconColor: Colors.black,
        ),
        title: const Text('Dados do Consultório'),
        centerTitle: true,
        backgroundColor: Colors.teal,
        actions: [
          IconButton(
            icon: Icon(_isEditing ? Icons.lock_open : Icons.lock),
            onPressed: () => setState(() => _isEditing = !_isEditing),
          ),
        ],
      ),
      body: GradientBackground(
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : ListView(
                padding: const EdgeInsets.all(16),
                children: [
                  _buildMapContainer(),
                  if (_locationError != null)
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        _locationError!,
                        style: const TextStyle(color: Colors.red),
                      ),
                    ),
                  const SizedBox(height: 20),
                  _buildTextField(
                    label: "Nome",
                    controller: _nomeController,
                    enabled: _isEditing,
                    validator: (value) =>
                        value?.isEmpty ?? true ? 'Campo obrigatório' : null,
                  ),
                  _buildTextField(
                    label: "Email",
                    controller: _emailController,
                    enabled: _isEditing,
                    validator: (value) =>
                        value?.isEmpty ?? true ? 'Campo obrigatório' : null,
                  ),
                  _buildTextField(
                    label: "CNPJ",
                    controller: _cpfCnpjController,
                    enabled: _isEditing,
                    validator: (value) =>
                        value?.isEmpty ?? true ? 'Campo obrigatório' : null,
                  ),
                  _buildTextField(
                    label: "Telefone",
                    controller: _telefoneController,
                    enabled: _isEditing,
                    keyboardType: TextInputType.phone,
                  ),
                  const SizedBox(height: 20),
                  _buildAtivoSwitch(),
                  const SizedBox(height: 20),
                  if (_isEditing) _buildSalvarButton(),
                ],
              ),
      ),
    );
  }

  Widget _buildMapContainer() {
    return Container(
      height: 300,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.grey),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(15),
        child: _buildMapWidget(),
      ),
    );
  }

  Widget _buildMapWidget() {
    if (_selectedLocation == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return GoogleMap(
      initialCameraPosition: CameraPosition(
        target: _selectedLocation!,
        zoom: 15,
      ),
      markers: _markers,
      onTap: _isEditing
          ? (position) {
              setState(() {
                _selectedLocation = position;
                _updateMarker();
              });
            }
          : null,
    );
  }

  Widget _buildTextField({
    required String label,
    required TextEditingController controller,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    bool enabled = true,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 15),
      child: TextFormField(
        controller: controller,
        validator: validator,
        keyboardType: keyboardType,
        enabled: enabled,
        decoration: InputDecoration(
          labelText: label,
          border: const OutlineInputBorder(),
        ),
      ),
    );
  }

  Widget _buildAtivoSwitch() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Text('Ativo'),
        Switch(
          value: _ativo,
          activeColor: Colors.green,
          onChanged: _isEditing ? (value) => setState(() => _ativo = value) : null,
        ),
      ],
    );
  }

  Widget _buildSalvarButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _salvarDados,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.teal,
          padding: const EdgeInsets.all(15),
        ),
        child: const Text('Salvar'),
      ),
    );
  }
}
