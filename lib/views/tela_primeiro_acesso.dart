import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fila_app/views/gradient_background.dart';

class PrimeiroAcessoScreen extends StatefulWidget {
  const PrimeiroAcessoScreen({super.key});

  @override
  PrimeiroAcessoScreenState createState() => PrimeiroAcessoScreenState();
}

class PrimeiroAcessoScreenState extends State<PrimeiroAcessoScreen> {
  bool _obscureTextSenha = true;
  bool _obscureTextConfirme = true;

  final _formKey = GlobalKey<FormState>();
  final TextEditingController usernameController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController senhaController = TextEditingController();
  final TextEditingController confirmeController = TextEditingController();

  String? usernameError;
  String? emailError;
  String? senhaError;
  String? confirmaSenhaError;

  @override
  void dispose() {
    usernameController.dispose();
    emailController.dispose();
    senhaController.dispose();
    confirmeController.dispose();
    super.dispose();
  }

  bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  bool isStrongPassword(String password) {
    return password.length >= 8 &&
        RegExp(r'[A-Z]').hasMatch(password) &&
        RegExp(r'[0-9]').hasMatch(password) &&
        RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password);
  }

  void validateUsername(String value) {
    setState(() {
      if (value.isEmpty) {
        usernameError = 'Por favor, insira o nome';
      } else if (value.length < 3) {
        usernameError = 'Nome deve ter pelo menos 3 caracteres';
      } else {
        usernameError = null;
      }
    });
  }

  void validateEmail(String value) {
    setState(() {
      if (value.isEmpty) {
        emailError = 'Por favor, insira um e-mail';
      } else if (!isValidEmail(value)) {
        emailError = 'Por favor, insira um e-mail válido';
      } else {
        emailError = null;
      }
    });
  }

  void validateSenha(String value) {
    setState(() {
      senhaError = isStrongPassword(value)
          ? null
          : 'Senha deve ter 8+ caracteres, 1 maiúscula, 1 número e 1 símbolo';

      if (confirmeController.text.isNotEmpty) {
        validateConfirmaSenha(confirmeController.text);
      }
    });
  }

  void validateConfirmaSenha(String value) {
    setState(() {
      confirmaSenhaError =
          (value == senhaController.text) ? null : 'As senhas não são iguais';
    });
  }

  bool validateAll() {
    validateUsername(usernameController.text);
    validateEmail(emailController.text);
    validateSenha(senhaController.text);
    validateConfirmaSenha(confirmeController.text);

    return usernameError == null &&
        emailError == null &&
        senhaError == null &&
        confirmaSenhaError == null;
  }

  void _handleNext() {
    if (!validateAll() || !_formKey.currentState!.validate()) {
      return;
    }

    final dadosCadastro = {
      'username': usernameController.text.trim(),
      'email': emailController.text.trim(),
      'senha': senhaController.text,
    };

    Get.toNamed(
      '/tipo_de_cadastro',
      arguments: dadosCadastro,
    );
  }

  Future<bool> _onWillPop() async {
    if (usernameController.text.isNotEmpty ||
        emailController.text.isNotEmpty ||
        senhaController.text.isNotEmpty) {
      return await showDialog<bool>(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Deseja sair?'),
              content: const Text('Os dados preenchidos serão perdidos.'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context, false),
                  child: const Text('Não'),
                ),
                TextButton(
                  onPressed: () => Navigator.pop(context, true),
                  child: const Text('Sim'),
                ),
              ],
            ),
          ) ??
          false;
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        body: SafeArea(
          child: GradientBackground(
            child: Container(
              // Seu conteúdo vai aqui
              child: Form(
                key: _formKey,
                child: ListView(
                  padding: const EdgeInsets.all(16.0),
                  children: [
                    const SizedBox(height: 40), // Aumentado para 40
                    Stack(
                      alignment: Alignment.center,
                      children: [
                        Positioned(
                          left: 0,
                          child: GestureDetector(
                            onTap: () => Navigator.pop(context),
                            child: const Icon(
                              Icons.arrow_back,
                              size: 30,
                              color: Colors.black,
                            ),
                          ),
                        ),
                        const Center(
                          child: Text(
                            'Primeiro acesso',
                            style: TextStyle(
                              fontFamily: 'Georgia',
                              fontSize: 32,
                              fontWeight: FontWeight.w400,
                              color: Colors.black,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 60),
                    _buildTextInput(
                      'Nome do médico ou da instituição',
                      'Digite o nome aqui',
                      usernameController,
                      errorText: usernameError,
                      onChanged: validateUsername,
                    ),
                    const SizedBox(height: 20),
                    _buildTextInput(
                      'E-mail',
                      'Insira seu e-mail',
                      emailController,
                      errorText: emailError,
                      onChanged: validateEmail,
                    ),
                    const SizedBox(height: 20),
                    _buildPasswordInput(
                      'Senha',
                      'Insira sua senha',
                      senhaController,
                      _obscureTextSenha,
                      (value) {
                        setState(() => _obscureTextSenha = value);
                      },
                      errorText: senhaError,
                      onChanged: validateSenha,
                    ),
                    const SizedBox(height: 20),
                    _buildPasswordInput(
                      'Confirme sua senha',
                      'Repita sua senha',
                      confirmeController,
                      _obscureTextConfirme,
                      (value) {
                        setState(() => _obscureTextConfirme = value);
                      },
                      errorText: confirmaSenhaError,
                      onChanged: validateConfirmaSenha,
                    ),
                    const SizedBox(height: 50),
                    Center(
                      child: GestureDetector(
                        onTap: _handleNext,
                        child: const Text(
                          'Confirmar',
                          style: TextStyle(
                            fontFamily: 'Georgia',
                            fontSize: 23,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTextInput(
    String label,
    String hint,
    TextEditingController controller, {
    String? errorText,
    Function(String)? onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontFamily: 'Georgia',
            fontSize: 20,
            color: Colors.black,
          ),
        ),
        TextField(
          controller: controller,
          onChanged: onChanged,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(
              color: Colors.black.withOpacity(0.5),
            ),
            errorText: errorText,
            enabledBorder: const UnderlineInputBorder(
              borderSide: BorderSide(color: Colors.black),
            ),
            focusedBorder: const UnderlineInputBorder(
              borderSide: BorderSide(color: Colors.black, width: 2),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPasswordInput(
    String label,
    String hint,
    TextEditingController controller,
    bool obscureText,
    ValueChanged<bool> onToggle, {
    String? errorText,
    Function(String)? onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontFamily: 'Georgia',
            fontSize: 20,
            color: Colors.black,
          ),
        ),
        TextField(
          controller: controller,
          obscureText: obscureText,
          onChanged: onChanged,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(
              color: Colors.black.withOpacity(0.5),
            ),
            errorText: errorText,
            suffixIcon: IconButton(
              icon: Icon(
                obscureText ? Icons.visibility_off : Icons.visibility,
                color: Colors.black,
              ),
              onPressed: () => onToggle(!obscureText),
            ),
            enabledBorder: const UnderlineInputBorder(
              borderSide: BorderSide(color: Colors.black),
            ),
            focusedBorder: const UnderlineInputBorder(
              borderSide: BorderSide(color: Colors.black, width: 2),
            ),
          ),
        ),
      ],
    );
  }
}
