import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fila_app/views/gradient_background.dart';

class TelaInicial extends StatelessWidget {
  const TelaInicial({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientBackground(
        child: Safe<PERSON><PERSON>(
          child: LayoutBuilder(
            builder: (context, constraints) {
              final larguraTela = constraints.maxWidth;
              final alturaTela = constraints.maxHeight;

              return SingleChildScrollView(
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    minHeight: alturaTela,
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _buildLogo(larguraTela, alturaTela),
                      _buildOption(
                        larguraTela,
                        alturaTela,
                        'assets/logo-administrador-removebg-preview-1-5.png',
                        'ADMINISTRATIVO',
                        '/login',
                      ),
                      _buildOption(
                        larguraTela,
                        alturaTela,
                        'assets/paciente-logo-removebg-preview-2.png',
                        'PACIENTE',
                        '/paciente',
                      ),
                      _buildHelpIcon(larguraTela, alturaTela),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildLogo(double larguraTela, double alturaTela) {
    return Center(
      child: Image.asset(
        'assets/logo-removebg-preview-1-28.png',
        width: larguraTela * 0.8,
        height: alturaTela * 0.25,
      ),
    );
  }

  Widget _buildOption(double larguraTela, double alturaTela, String imagePath,
      String text, String route) {
    return Center(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            Get.toNamed(route);
          },
          borderRadius: BorderRadius.circular(8.0),
          splashColor: Colors.grey.withOpacity(0.5),
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: alturaTela * 0.02),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset(
                  imagePath,
                  width: larguraTela * 0.2,
                  height: alturaTela * 0.1,
                ),
                SizedBox(height: alturaTela * 0.02),
                Text(
                  text,
                  style: TextStyle(
                    fontFamily: 'Georgia',
                    fontSize: larguraTela * 0.06,
                    color: const Color.fromARGB(255, 0, 0, 0),
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHelpIcon(double larguraTela, double alturaTela) {
    return Align(
      alignment: Alignment.bottomRight,
      child: Padding(
        padding: EdgeInsets.only(
          right: larguraTela * 0.02,
          bottom: alturaTela * 0.01,
        ),
        child: IconButton(
          icon: Image.asset(
            'assets/help.png',
            width: larguraTela * 0.1,
            height: alturaTela * 0.1,
          ),
          onPressed: () {
            Get.toNamed('/ajuda');
          },
        ),
      ),
    );
  }
}
