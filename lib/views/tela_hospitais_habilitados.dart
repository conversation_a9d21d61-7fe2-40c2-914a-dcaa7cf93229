import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'package:fila_app/views/gradient_background.dart';
import '../controllers/hospitais_habilitados_controller.dart';
import '../utils/live_query_manager.dart';

class TelaHospitaisHabilitados extends GetView<HospitaisHabilitadosController> {
  const TelaHospitaisHabilitados({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: GradientBackground(
        child: SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 20),
              _buildHeader(),
              const SizedBox(height: 20),
              _buildSearchField(),
              const SizedBox(height: 20),
              Expanded(
                child: _buildHospitalsList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.black, size: 32),
            iconSize: 40, // Aumentando o espaço do botão
            onPressed: () => Get.back(),
          ),
          const Expanded(
            child: Center(
              child: Text(
                'Hospitais habilitados',
                style: TextStyle(
                  fontFamily: 'Georgia',
                  fontWeight: FontWeight.bold,
                  fontSize: 24,
                  color: Colors.black87,
                ),
              ),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.refresh, color: Color(0xFF34ECCB), size: 28),
            onPressed: () {
              // Carregar hospitais apenas quando o botão de refresh for clicado
              controller.loadHospitals();
              
              // Mostrar feedback ao usuário
              Get.snackbar(
                'Atualizando',
                'Atualizando lista de hospitais...',
                backgroundColor: Colors.blue,
                colorText: Colors.white,
                snackPosition: SnackPosition.BOTTOM,
                duration: const Duration(seconds: 1),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSearchField() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(30),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(25),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        onChanged: (value) => controller.searchQuery.value = value,
        decoration: const InputDecoration(
          hintText: 'Pesquisar hospitais...',
          prefixIcon: Icon(Icons.search, color: Color(0xFF34ECCB)),
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(horizontal: 20, vertical: 15),
        ),
      ),
    );
  }

  Widget _buildHospitalsList() {
    return Obx(() {
      if (controller.isLoading.value) {
        return const Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF34ECCB)),
          ),
        );
      }

      if (controller.error.isNotEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                controller.error.value,
                style: const TextStyle(
                  color: Colors.red,
                  fontFamily: 'Georgia',
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: controller.loadHospitals,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF34ECCB),
                ),
                child: const Text('Tentar Novamente'),
              ),
            ],
          ),
        );
      }

      final filteredHospitals = controller.getFilteredHospitals();

      if (filteredHospitals.isEmpty) {
        return const Center(
          child: Text(
            'Nenhum hospital encontrado',
            style: TextStyle(
              fontFamily: 'Georgia',
              fontSize: 16,
            ),
          ),
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: filteredHospitals.length,
        itemBuilder: (context, index) {
          final hospital = filteredHospitals[index];
          final originalIndex = controller.getOriginalIndex(hospital);
          return _buildHospitalCard(hospital, originalIndex);
        },
      );
    });
  }

  Widget _buildHospitalCard(ParseObject hospital, int index) {
    final nome = hospital.get<String>('nome') ?? 'Hospital sem nome';
    final cidade = hospital.get<String>('cidade') ?? '';
    final estado = hospital.get<String>('estado') ?? '';
    final location = [cidade, estado].where((e) => e.isNotEmpty).join(' - ');

    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(vertical: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15),
      ),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.white, const Color(0xFF34ECCB).withAlpha(25)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(15),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    nome,
                    style: const TextStyle(
                      fontFamily: 'Georgia',
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (location.isNotEmpty)
                    Text(
                      location,
                      style: const TextStyle(
                        fontFamily: 'Georgia',
                        fontSize: 14,
                        color: Colors.black54,
                      ),
                    ),
                ],
              ),
            ),
            Obx(() => Switch(
                  value: controller.selectedHospitals[index],
                  onChanged: (bool? value) {
                    if (value != null && !controller.isLoading.value) {
                      controller.updateHospitalSelection(index, value);
                    }
                  },
                  activeColor: const Color(0xFF34ECCB),
                )),
          ],
        ),
      ),
    );
  }
}
