import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_polyline_points/flutter_polyline_points.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:fila_app/views/gradient_background.dart';
import 'dart:math' show cos, sqrt, asin;
import 'dart:ui' show ImageByteFormat, PictureRecorder;
import 'package:http/http.dart' as http;
import 'dart:convert';

class LocalizacaoPacienteScreen extends StatefulWidget {
  const LocalizacaoPacienteScreen({super.key});

  @override
  LocalizacaoPacienteScreenState createState() =>
      LocalizacaoPacienteScreenState();
}

class LocalizacaoPacienteScreenState extends State<LocalizacaoPacienteScreen> {
  late GoogleMapController mapController;
  LatLng? _currentPosition;
  LatLng? _hospitalPosition;
  bool _isLoading = true;
  bool _isRetrying = false;
  Set<Marker> _markers = {};
  final Set<Polyline> _polylines = {};
  Map<String, dynamic> _routeInfo = {
    'distance': '0 km',
    'duration': '0 min',
  };
  late final String _googleApiKey;
  bool _showRouteDetails = false;
  String? _locationError;
  bool _locationPermissionDenied = false;
  bool _locationServiceDisabled = false;

  @override
  void initState() {
    super.initState();
    _googleApiKey = dotenv.env['MAPS_API_KEY'] ?? '';
    _initializeMap();
  }

  Future<void> _initializeMap() async {
    try {
      await _checkLocationServices();
      await _getCurrentLocation();
      _loadHospitalLocation();
    } catch (e) {
      // Errors already handled in specific methods
    }
  }

  Future<void> _checkLocationServices() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();

      if (!serviceEnabled) {
        setState(() {
          _isLoading = false;
          _locationServiceDisabled = true;
          _locationError =
              'Os serviços de localização estão desativados no seu dispositivo.';
        });
        return;
      }

      LocationPermission permission = await Geolocator.checkPermission();

      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          setState(() {
            _isLoading = false;
            _locationPermissionDenied = true;
            _locationError =
                'Permissão de localização negada. Precisamos da sua localização para mostrar a rota.';
          });
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        setState(() {
          _isLoading = false;
          _locationPermissionDenied = true;
          _locationError =
              'Permissão de localização permanentemente negada. Por favor, habilite nas configurações do seu dispositivo.';
        });
        return;
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _locationError = 'Erro ao verificar serviços de localização: $e';
      });
    }
  }

  Future<void> _getCurrentLocation() async {
    try {
      if (_locationPermissionDenied || _locationServiceDisabled) {
        return;
      }

      setState(() {
        _isRetrying = true;
      });

      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 15),
      );

      if (mounted) {
        setState(() {
          _currentPosition = LatLng(position.latitude, position.longitude);
          _updateMarkers();
          _locationError = null;
          _isRetrying = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _locationError =
              'Não foi possível obter sua localização: ${e.toString().contains('TimeoutException') ? 'Tempo esgotado' : e}';
          _isRetrying = false;
        });
      }
    }
  }

  // Tenta usar a última posição conhecida como alternativa
  Future<void> _tryLastKnownPosition() async {
    try {
      final position = await Geolocator.getLastKnownPosition();

      if (position != null && mounted) {
        setState(() {
          _currentPosition = LatLng(position.latitude, position.longitude);
          _updateMarkers();
          _locationError =
              'Usando última posição conhecida. Pode não ser precisa.';
          _isRetrying = false;
        });

        // Se temos a posição do hospital, podemos calcular a rota
        if (_hospitalPosition != null) {
          _getPolylinePoints();
          _calculateDistance();
        }
      } else {
        setState(() {
          _locationError =
              'Não foi possível obter nem mesmo a última posição conhecida.';
          _isRetrying = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _locationError = 'Erro ao obter última posição conhecida: $e';
          _isRetrying = false;
        });
      }
    }
  }

  void _loadHospitalLocation() {
    final args = Get.arguments as Map<String, dynamic>?;
    if (args != null) {
      final latitude = args['latitude'] as double;
      final longitude = args['longitude'] as double;

      setState(() {
        _hospitalPosition = LatLng(latitude, longitude);
        _updateMarkers();

        // Só tentamos calcular rotas se temos a posição do usuário
        if (_currentPosition != null) {
          _getPolylinePoints();
          _calculateDistance();
        }

        _isLoading = false;
      });
    } else {
      setState(() {
        _locationError = 'Dados do hospital não encontrados';
        _isLoading = false;
      });
    }
  }

  Future<BitmapDescriptor> _createCustomMarkerBitmap(
      IconData icon, Color backgroundColor, Color iconColor) async {
    final recorder = PictureRecorder();
    final canvas = Canvas(recorder);
    final size = const Size(80, 80);
    final paint = Paint()..color = backgroundColor;

    // Desenha o círculo de fundo
    canvas.drawCircle(Offset(size.width / 2, size.height / 2 - 10), 25, paint);

    // Desenha uma pequena sombra
    paint.color = Colors.black26;
    canvas.drawOval(
        Rect.fromLTWH(size.width / 2 - 10, size.height / 2 + 15, 20, 7), paint);

    // Desenha uma pequena ponta triangular abaixo do círculo
    final trianglePath = Path();
    trianglePath.moveTo(size.width / 2, size.height / 2 + 15);
    trianglePath.lineTo(size.width / 2 - 8, size.height / 2 - 5);
    trianglePath.lineTo(size.width / 2 + 8, size.height / 2 - 5);
    trianglePath.close();

    paint.color = backgroundColor;
    canvas.drawPath(trianglePath, paint);

    // Adiciona o ícone
    TextPainter textPainter = TextPainter(textDirection: TextDirection.ltr);
    textPainter.text = TextSpan(
      text: String.fromCharCode(icon.codePoint),
      style: TextStyle(
        color: iconColor,
        fontSize: 30,
        fontFamily: icon.fontFamily,
        fontWeight: FontWeight.bold,
      ),
    );

    textPainter.layout();
    textPainter.paint(
        canvas,
        Offset(size.width / 2 - textPainter.width / 2,
            size.height / 2 - 10 - textPainter.height / 2));

    final picture = recorder.endRecording();
    final img = await picture.toImage(size.width.toInt(), size.height.toInt());
    final data = await img.toByteData(format: ImageByteFormat.png);

    if (data != null) {
      return BitmapDescriptor.fromBytes(data.buffer.asUint8List());
    } else {
      // Fallback para ícone padrão
      return BitmapDescriptor.defaultMarkerWithHue(
          backgroundColor == Colors.blue
              ? BitmapDescriptor.hueBlue
              : BitmapDescriptor.hueRed);
    }
  }

  void _updateMarkers() async {
    try {
      final locationIcon = await _createCustomMarkerBitmap(
          Icons.my_location, Colors.blue, Colors.white);

      final hospitalIcon = await _createCustomMarkerBitmap(
          Icons.local_hospital, Colors.red, Colors.white);

      setState(() {
        _markers = {};

        if (_currentPosition != null) {
          _markers.add(
            Marker(
              markerId: const MarkerId('current_location'),
              position: _currentPosition!,
              infoWindow: const InfoWindow(
                title: 'Sua localização',
                snippet: 'Sua posição atual',
              ),
              icon: locationIcon,
            ),
          );
        }

        if (_hospitalPosition != null) {
          _markers.add(
            Marker(
              markerId: const MarkerId('hospital'),
              position: _hospitalPosition!,
              infoWindow: const InfoWindow(
                title: 'Hospital',
                snippet: 'Seu destino',
              ),
              icon: hospitalIcon,
            ),
          );
        }
      });
    } catch (e) {
      // Fallback para marcadores básicos se os personalizados falharem
      setState(() {
        _markers = {};

        if (_currentPosition != null) {
          _markers.add(
            Marker(
              markerId: const MarkerId('current_location'),
              position: _currentPosition!,
              infoWindow: const InfoWindow(title: 'Sua localização'),
              icon: BitmapDescriptor.defaultMarkerWithHue(
                  BitmapDescriptor.hueBlue),
            ),
          );
        }

        if (_hospitalPosition != null) {
          _markers.add(
            Marker(
              markerId: const MarkerId('hospital'),
              position: _hospitalPosition!,
              infoWindow: const InfoWindow(title: 'Hospital'),
              icon: BitmapDescriptor.defaultMarkerWithHue(
                  BitmapDescriptor.hueRed),
            ),
          );
        }
      });
    }
  }

  Future<void> _getPolylinePoints() async {
    if (_currentPosition == null || _hospitalPosition == null) return;

    try {
      // Primeiro vamos tentar com a API Directions diretamente via HTTP
      // Esta abordagem tende a ser mais confiável que o PolylinePoints
      final directionsResponse = await _getDirectionsFromApi();

      if (directionsResponse != null &&
          directionsResponse['routes'] != null &&
          directionsResponse['routes'].isNotEmpty) {
        _decodeAndAddPolyline(directionsResponse);
      } else {
        // Se a chamada direta falhar, tentamos a abordagem alternativa com PolylinePoints
        await _getPolylinePointsAlternative();
      }
    } catch (e) {
      debugPrint('Error in primary route calculation: $e');
      try {
        // Tenta abordagem alternativa se a primeira falhar
        await _getPolylinePointsAlternative();
      } catch (e2) {
        debugPrint('Error in alternate route calculation: $e2');
        _createStraightLinePolyline();
      }
    }
  }

  Future<Map<String, dynamic>?> _getDirectionsFromApi() async {
    try {
      // Formata a URL da API Directions
      final apiUrl = Uri.parse(
          'https://maps.googleapis.com/maps/api/directions/json?'
          'origin=${_currentPosition!.latitude},${_currentPosition!.longitude}'
          '&destination=${_hospitalPosition!.latitude},${_hospitalPosition!.longitude}'
          '&mode=driving'
          '&alternatives=true'
          '&key=$_googleApiKey');

      final response = await http.get(apiUrl);

      if (response.statusCode == 200) {
        final decoded = json.decode(response.body);
        return decoded;
      }
      return null;
    } catch (e) {
      debugPrint('Error in getDirectionsFromApi: $e');
      return null;
    }
  }

  void _decodeAndAddPolyline(Map<String, dynamic> directionData) {
    // Certifique-se de que temos rotas para processar
    if (directionData['routes'] == null || directionData['routes'].isEmpty) {
      _createStraightLinePolyline();
      return;
    }

    try {
      // Limpa qualquer polyline existente
      setState(() {
        _polylines.clear();
      });

      // Obtém os pontos encodados da primeira rota
      final route = directionData['routes'][0];
      final leg = route['legs'][0];
      final steps = leg['steps'];

      // Extrai o tempo e distância estimados
      final distance = leg['distance']['text'];
      final duration = leg['duration']['text'];
      setState(() {
        _routeInfo['distance'] = distance;
        _routeInfo['duration'] = duration;
      });

      // Decodifica e adiciona cada segmento da rota
      List<LatLng> polylineCoordinates = [];

      if (route.containsKey('overview_polyline') &&
          route['overview_polyline'].containsKey('points')) {
        // Usa a visão geral da polyline para rotas mais suaves
        polylineCoordinates =
            _decodePolyline(route['overview_polyline']['points']);
      } else {
        // Se não tiver overview_polyline, construímos a partir dos steps
        for (var step in steps) {
          if (step.containsKey('polyline') &&
              step['polyline'].containsKey('points')) {
            polylineCoordinates
                .addAll(_decodePolyline(step['polyline']['points']));
          }
        }
      }

      if (polylineCoordinates.isEmpty) {
        _createStraightLinePolyline();
        return;
      }

      // Adiciona as novas polylines com estilo aprimorado
      setState(() {
        // Adiciona a sombra primeiro (para ficar abaixo)
        _polylines.add(
          Polyline(
            polylineId: const PolylineId('route_shadow'),
            color: Colors.black54,
            points: polylineCoordinates,
            width: 9,
            zIndex: -1,
            endCap: Cap.roundCap,
            startCap: Cap.roundCap,
            jointType: JointType.round,
          ),
        );

        // Adiciona a linha principal da rota
        _polylines.add(
          Polyline(
            polylineId: const PolylineId('route'),
            color: Colors.blue,
            points: polylineCoordinates,
            width: 6,
            patterns: [
              PatternItem.dash(20),
              PatternItem.gap(5),
            ],
            endCap: Cap.roundCap,
            startCap: Cap.roundCap,
            jointType: JointType.round,
          ),
        );
      });
    } catch (e) {
      debugPrint('Error decoding polyline: $e');
      _createStraightLinePolyline();
    }
  }

  List<LatLng> _decodePolyline(String encoded) {
    List<LatLng> poly = [];
    int index = 0, len = encoded.length;
    int lat = 0, lng = 0;

    while (index < len) {
      int b, shift = 0, result = 0;
      do {
        b = encoded.codeUnitAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      int dlat = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
      lat += dlat;

      shift = 0;
      result = 0;
      do {
        b = encoded.codeUnitAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      int dlng = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
      lng += dlng;

      double latitude = lat / 1E5;
      double longitude = lng / 1E5;
      poly.add(LatLng(latitude, longitude));
    }
    return poly;
  }

  Future<void> _getPolylinePointsAlternative() async {
    PolylinePoints polylinePoints = PolylinePoints();
    try {
      PolylineResult result = await polylinePoints.getRouteBetweenCoordinates(
        googleApiKey: _googleApiKey,
        request: PolylineRequest(
          origin: PointLatLng(
              _currentPosition!.latitude, _currentPosition!.longitude),
          destination: PointLatLng(
              _hospitalPosition!.latitude, _hospitalPosition!.longitude),
          mode: TravelMode.driving,
        ),
      );

      if (result.points.isNotEmpty) {
        List<LatLng> polylineCoordinates = [];
        for (var point in result.points) {
          polylineCoordinates.add(LatLng(point.latitude, point.longitude));
        }

        setState(() {
          _polylines.clear();

          // Adiciona a linha de fundo primeiro
          _polylines.add(
            Polyline(
              polylineId: const PolylineId('route_shadow'),
              color: Colors.black54,
              points: polylineCoordinates,
              width: 9,
              zIndex: -1,
              endCap: Cap.roundCap,
              startCap: Cap.roundCap,
              jointType: JointType.round,
            ),
          );

          // Adiciona a linha principal por cima
          _polylines.add(
            Polyline(
              polylineId: const PolylineId('route'),
              color: Colors.blue,
              points: polylineCoordinates,
              width: 6,
              patterns: [
                PatternItem.dash(20),
                PatternItem.gap(5),
              ],
              endCap: Cap.roundCap,
              startCap: Cap.roundCap,
              jointType: JointType.round,
            ),
          );
        });
      } else {
        debugPrint('Failed to get polyline points: ${result.errorMessage}');
        _createStraightLinePolyline();
      }
    } catch (e) {
      debugPrint('Error in polyline alternative: $e');
      _createStraightLinePolyline();
    }
  }

  void _createStraightLinePolyline() {
    if (_currentPosition == null || _hospitalPosition == null) return;

    setState(() {
      _polylines.add(
        Polyline(
          polylineId: const PolylineId('straight_route'),
          color: Colors.red,
          points: [
            LatLng(_currentPosition!.latitude, _currentPosition!.longitude),
            LatLng(_hospitalPosition!.latitude, _hospitalPosition!.longitude),
          ],
          width: 5,
        ),
      );
    });
  }

  void _calculateDistance() {
    if (_currentPosition == null || _hospitalPosition == null) return;

    double calculateDistance(lat1, lon1, lat2, lon2) {
      var p = 0.017453292519943295;
      var c = cos;
      var a = 0.5 -
          c((lat2 - lat1) * p) / 2 +
          c(lat1 * p) * c(lat2 * p) * (1 - c((lon2 - lon1) * p)) / 2;
      return 12742 * asin(sqrt(a));
    }

    double distanceInKm = calculateDistance(
      _currentPosition!.latitude,
      _currentPosition!.longitude,
      _hospitalPosition!.latitude,
      _hospitalPosition!.longitude,
    );

    // Estimativa de tempo com base na distância - 50 km/h de média
    int estimatedMinutes = (distanceInKm / 50 * 60).round();

    setState(() {
      _routeInfo = {
        'distance': '${distanceInKm.toStringAsFixed(1)} km',
        'duration': '$estimatedMinutes min',
      };
    });
  }

  void _centerMap() {
    if (_currentPosition == null || _hospitalPosition == null || !mounted) {
      return;
    }

    final bounds = LatLngBounds(
      southwest: LatLng(
        _currentPosition!.latitude < _hospitalPosition!.latitude
            ? _currentPosition!.latitude
            : _hospitalPosition!.latitude,
        _currentPosition!.longitude < _hospitalPosition!.longitude
            ? _currentPosition!.longitude
            : _hospitalPosition!.longitude,
      ),
      northeast: LatLng(
        _currentPosition!.latitude > _hospitalPosition!.latitude
            ? _currentPosition!.latitude
            : _hospitalPosition!.latitude,
        _currentPosition!.longitude > _hospitalPosition!.longitude
            ? _currentPosition!.longitude
            : _hospitalPosition!.longitude,
      ),
    );

    mapController.animateCamera(CameraUpdate.newLatLngBounds(bounds, 100));
  }

  void _openGoogleMapsNavigation() async {
    if (_hospitalPosition == null) {
      _showError('Localização do hospital não disponível');
      return;
    }

    final url = Uri.parse(
        'https://www.google.com/maps/dir/?api=1&destination=${_hospitalPosition!.latitude},${_hospitalPosition!.longitude}&travelmode=driving');

    try {
      if (!await launchUrl(url, mode: LaunchMode.externalApplication)) {
        _showError('Não foi possível abrir o Google Maps');
      }
    } catch (e) {
      _showError('Erro ao abrir navegação: $e');
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }

  void _openAppSettings() async {
    await Geolocator.openAppSettings();
  }

  void _openLocationSettings() async {
    await Geolocator.openLocationSettings();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Fundo gradiente padrão
          GradientBackground(
            child: Container(),
          ),

          // Conteúdo principal
          if (_isLoading)
            const Center(child: CircularProgressIndicator())
          else if (_locationError != null &&
              (_locationPermissionDenied || _locationServiceDisabled))
            _buildLocationErrorView()
          else if (_currentPosition == null && _hospitalPosition != null)
            _buildLocationRetryView()
          else
            _buildMapView(),

          // Barra superior sempre visível
          _buildTopBar(),

          // Detalhes da rota quando expandidos
          if (_showRouteDetails &&
              !_locationPermissionDenied &&
              !_locationServiceDisabled &&
              _currentPosition != null)
            _buildRouteDetails(),

          // Botões de ação (apenas quando o mapa está visível)
          if (_currentPosition != null && _hospitalPosition != null)
            _buildBottomButtons(),
        ],
      ),
    );
  }

  Widget _buildLocationErrorView() {
    return Center(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.location_off,
              size: 80,
              color: Colors.red,
            ),
            const SizedBox(height: 24),
            Text(
              _locationError ?? 'Erro de localização',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            if (_locationServiceDisabled)
              ElevatedButton.icon(
                icon: const Icon(Icons.settings),
                label: const Text('Ativar Serviços de Localização'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 12,
                  ),
                ),
                onPressed: _openLocationSettings,
              ),
            if (_locationPermissionDenied)
              ElevatedButton.icon(
                icon: const Icon(Icons.settings),
                label: const Text('Conceder Permissão nas Configurações'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 12,
                  ),
                ),
                onPressed: _openAppSettings,
              ),
            const SizedBox(height: 16),
            TextButton.icon(
              icon: const Icon(Icons.arrow_back),
              label: const Text('Voltar'),
              onPressed: () => Navigator.pop(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationRetryView() {
    return Center(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.location_searching,
              size: 80,
              color: Colors.orange,
            ),
            const SizedBox(height: 24),
            Text(
              _locationError ?? 'Não foi possível obter sua localização',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              icon: const Icon(Icons.refresh),
              label: const Text('Tentar Novamente'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 12,
                ),
              ),
              onPressed: _isRetrying ? null : _getCurrentLocation,
            ),
            const SizedBox(height: 16),
            if (_isRetrying)
              const Padding(
                padding: EdgeInsets.all(8.0),
                child: CircularProgressIndicator(),
              )
            else
              TextButton.icon(
                icon: const Icon(Icons.history),
                label: const Text('Usar Última Localização Conhecida'),
                onPressed: _tryLastKnownPosition,
              ),
            const SizedBox(height: 16),
            TextButton.icon(
              icon: const Icon(Icons.arrow_back),
              label: const Text('Voltar'),
              onPressed: () => Navigator.pop(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMapView() {
    return GoogleMap(
      initialCameraPosition: CameraPosition(
        target: _hospitalPosition ?? const LatLng(-23.55052, -46.633309),
        zoom: 15,
      ),
      mapType: MapType.normal,
      markers: _markers,
      polylines: _polylines,
      myLocationEnabled: true,
      myLocationButtonEnabled: false,
      zoomControlsEnabled: false,
      trafficEnabled: true, // Exibe o tráfego nas vias
      buildingsEnabled: true, // Renderiza edificações em 3D
      compassEnabled: true, // Mostra a bússola no mapa
      mapToolbarEnabled: true, // Exibe opções adicionais ao segurar marcadores
      // Define o estilo de mapa personalizado
      onMapCreated: (GoogleMapController controller) {
        mapController = controller;
        _setMapStyle(controller);
        if (_currentPosition != null && _hospitalPosition != null) {
          _centerMap();
        }
      },
    );
  }

  Future<void> _setMapStyle(GoogleMapController controller) async {
    // Usando um estilo de mapa mais suave e profissional
    String mapStyle = '''
    [
      {
        "featureType": "administrative",
        "elementType": "geometry.fill",
        "stylers": [{"visibility": "on"}]
      },
      {
        "featureType": "poi",
        "elementType": "labels.icon",
        "stylers": [{"saturation": -5}, {"lightness": 10}]
      },
      {
        "featureType": "poi.medical",
        "elementType": "geometry.fill",
        "stylers": [{"color": "#f1e4e4"}]
      },
      {
        "featureType": "poi.medical",
        "elementType": "labels.icon",
        "stylers": [{"color": "#f86262"}]
      },
      {
        "featureType": "road",
        "elementType": "geometry.fill",
        "stylers": [{"color": "#ffffff"}]
      },
      {
        "featureType": "road",
        "elementType": "geometry.stroke",
        "stylers": [{"color": "#e9e9e9"}]
      },
      {
        "featureType": "transit.line",
        "elementType": "geometry.fill",
        "stylers": [{"saturation": -50}]
      },
      {
        "featureType": "water",
        "elementType": "geometry.fill",
        "stylers": [{"color": "#b3dfff"}]
      }
    ]
    ''';

    try {
      await controller.setMapStyle(mapStyle);
    } catch (e) {
      debugPrint('Erro ao aplicar estilo ao mapa: $e');
    }
  }

  Widget _buildTopBar() {
    return Positioned(
      top: 50,
      left: 20,
      right: 20,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(15),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              decoration: BoxDecoration(
                color: Colors.grey.shade200,
                shape: BoxShape.circle,
              ),
              child: IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () => Navigator.pop(context),
                color: Colors.black87,
                iconSize: 20,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text(
                    'Trajeto até o Hospital',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      fontFamily: 'Georgia',
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        Icons.directions_car,
                        size: 16,
                        color: Colors.blue.shade700,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        _currentPosition == null
                            ? 'Carregando rota...'
                            : '${_routeInfo['distance']} • ${_routeInfo['duration']}',
                        style: TextStyle(
                          color: Colors.grey[700],
                          fontSize: 14,
                          fontFamily: 'Georgia',
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      // Indicador de tráfego (simulado)
                      if (_currentPosition != null)
                        Container(
                          margin: const EdgeInsets.only(left: 8),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.green.shade100,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.trending_down,
                                size: 12,
                                color: Colors.green.shade700,
                              ),
                              const SizedBox(width: 2),
                              Text(
                                'Tráfego leve',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Colors.green.shade700,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
            if (_currentPosition != null && _hospitalPosition != null)
              Container(
                decoration: BoxDecoration(
                  color: _showRouteDetails
                      ? Colors.blue.shade50
                      : Colors.grey.shade200,
                  shape: BoxShape.circle,
                ),
                child: IconButton(
                  icon: Icon(
                    _showRouteDetails
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                    color: _showRouteDetails ? Colors.blue : Colors.black87,
                  ),
                  iconSize: 20,
                  onPressed: () =>
                      setState(() => _showRouteDetails = !_showRouteDetails),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildRouteDetails() {
    return Positioned(
      top: 130,
      left: 20,
      right: 20,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(15),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Seção de resumo da viagem
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Resumo da viagem',
                    style: TextStyle(
                      fontFamily: 'Georgia',
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _buildRouteDetailItem(
                        Icons.directions_car,
                        'Distância',
                        _routeInfo['distance'],
                        Colors.blue.shade700,
                      ),
                      _buildRouteDetailItem(
                        Icons.access_time,
                        'Tempo estimado',
                        _routeInfo['duration'],
                        Colors.blue.shade700,
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Destino
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.red.shade100,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    Icons.location_on,
                    color: Colors.red.shade700,
                  ),
                ),
                const SizedBox(width: 16),
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Destino',
                        style: TextStyle(
                          color: Colors.grey,
                          fontSize: 12,
                          fontFamily: 'Georgia',
                        ),
                      ),
                      Text(
                        'Hospital',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          fontFamily: 'Georgia',
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Dicas de viagem
            Container(
              padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
              decoration: BoxDecoration(
                color: Colors.amber.shade50,
                borderRadius: BorderRadius.circular(10),
                border: Border.all(color: Colors.amber.shade200),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Colors.amber.shade700,
                    size: 18,
                  ),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'Chegue pelo menos 15 minutos antes do horário previsto de atendimento.',
                      style: TextStyle(
                        fontSize: 12,
                        fontFamily: 'Georgia',
                        color: Colors.black87,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            if (_locationError != null && _currentPosition != null)
              Padding(
                padding: const EdgeInsets.only(top: 12.0),
                child: Row(
                  children: [
                    Icon(
                      Icons.warning_amber_rounded,
                      color: Colors.orange,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _locationError!,
                        style: const TextStyle(
                          color: Colors.orange,
                          fontSize: 12,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildRouteDetailItem(
      IconData icon, String title, String value, Color iconColor) {
    return Row(
      children: [
        Container(
          width: 36,
          height: 36,
          decoration: BoxDecoration(
            color: iconColor.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(icon, color: iconColor, size: 20),
        ),
        const SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
                fontFamily: 'Georgia',
              ),
            ),
            Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
                fontFamily: 'Georgia',
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildBottomButtons() {
    return Positioned(
      bottom: 30,
      left: 0,
      right: 0,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Botões de controle do mapa
          Padding(
            padding: const EdgeInsets.only(right: 20, bottom: 10),
            child: Align(
              alignment: Alignment.centerRight,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Botão para centralizar o mapa
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(30),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          blurRadius: 6,
                          offset: const Offset(0, 3),
                        ),
                      ],
                    ),
                    child: IconButton(
                      icon: const Icon(Icons.center_focus_strong),
                      color: Colors.black87,
                      onPressed: _centerMap,
                    ),
                  ),
                  const SizedBox(height: 8),
                  // Botão para atualizar a localização
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(30),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          blurRadius: 6,
                          offset: const Offset(0, 3),
                        ),
                      ],
                    ),
                    child: IconButton(
                      icon: _isRetrying
                          ? const SizedBox(
                              width: 18,
                              height: 18,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Icon(Icons.my_location),
                      color: Colors.blue,
                      onPressed: _isRetrying ? null : _getCurrentLocation,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Botão de navegação principal
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 20),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.blue.withOpacity(0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            width: double.infinity,
            height: 60,
            child: ElevatedButton.icon(
              onPressed: _openGoogleMapsNavigation,
              icon: const Icon(
                Icons.navigation,
                size: 24,
              ),
              label: const Text(
                'Iniciar Navegação no Google Maps',
                style: TextStyle(
                  fontFamily: 'Georgia',
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                elevation: 0,
              ),
            ),
          ),
          const SizedBox(height: 5),
          // Opção alternativa para abrir o Waze
          TextButton.icon(
            onPressed: _openWazeNavigation,
            icon: Image.asset(
              'assets/waze_icon.png',
              width: 20,
              height: 20,
              errorBuilder: (context, error, stackTrace) =>
                  const Icon(Icons.map, size: 20),
            ),
            label: const Text(
              'Abrir no Waze',
              style: TextStyle(
                color: Colors.blue,
                fontFamily: 'Georgia',
                decoration: TextDecoration.underline,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _openWazeNavigation() async {
    if (_hospitalPosition == null) {
      _showError('Localização do hospital não disponível');
      return;
    }

    final url = Uri.parse(
        'waze://?ll=${_hospitalPosition!.latitude},${_hospitalPosition!.longitude}&navigate=yes');

    try {
      if (!await launchUrl(url, mode: LaunchMode.externalApplication)) {
        // Tenta abrir o site do Waze como plano B
        final webUrl = Uri.parse(
            'https://waze.com/ul?ll=${_hospitalPosition!.latitude},${_hospitalPosition!.longitude}&navigate=yes');
        if (!await launchUrl(webUrl, mode: LaunchMode.externalApplication)) {
          _showError('Não foi possível abrir o Waze');
        }
      }
    } catch (e) {
      _showError('Erro ao abrir navegação com Waze');
    }
  }
}
