import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fila_app/views/gradient_background.dart';

class TelaDeAjudas extends StatefulWidget {
  const TelaDeAjudas({super.key});

  @override
  State<TelaDeAjudas> createState() => _TelaDeAjudasState();
}

class _TelaDeAjudasState extends State<TelaDeAjudas> {
  final TextEditingController _searchController = TextEditingController();
  int _selectedCategoryIndex = 0;
  String _searchQuery = '';
  
  // Adicionar variável para controlar qual ExpansionTile está expandido
  int? _expandedTileIndex;
  
  // Lista de controllers para os ExpansionTiles
  List<ExpansionTileController> _tileControllers = [];

  // Armazena o estado anterior para comparação
  String _previousSearchQuery = '';
  int _previousCategoryIndex = 0;
  
  // Adicionar um Key para forçar reconstrução completa ao mudar de categoria
  Key _expansionTileKey = UniqueKey();

  final List<Map<String, dynamic>> _categories = [
    {
      'icon': Icons.local_hospital,
      'title': 'Gerais',
      'topics': [
        {
          'title': 'Como funciona o aplicativo?',
          'content': '''O aplicativo permite que você:
• Entre na fila de atendimento virtualmente
• Tenha a rota mais próxima para o médico onde você será atendido
• Acompanhe sua posição na fila em tempo real
• Receba notificações sobre o andamento
• Comunique-se com a atendente pelo WhatsApp para esclarecer dúvidas e obter informações
• Visualize o tempo estimado de espera''',
        },
        {
          'title': 'Como entrar na fila de atendimento?',
          'content': '''Para entrar na fila:
1. Acesse a seção "Paciente"
2. Escaneie o QR Code do médico/hospital
3. Aguarde a confirmação
4. Acompanhe sua posição

Você receberá notificações sobre o andamento do atendimento.''',
        },
      ],
    },
    {
      'icon': Icons.security,
      'title': 'Segurança',
      'topics': [
        {
          'title': 'Como protegemos seus dados?',
          'content': '''Seus dados são protegidos por:
• Criptografia de ponta a ponta
• Servidores seguros
• Acesso restrito
• Conformidade com LGPD
• Backups regulares

Nunca compartilhamos suas informações com terceiros.''',
        },
        {
          'title': 'Política de privacidade',
          'content': '''Nossa política garante:
• Transparência no uso dos dados
• Direito de acesso às informações
• Opção de exclusão de conta
• Controle sobre notificações
• Segurança das informações médicas''',
        },
      ],
    },
    {
      'icon': Icons.person,
      'title': 'Conta',
      'topics': [
        {
          'title': 'Como atualizar meus dados?',
          'content': '''Para atualizar seus dados:
1. Acesse seu perfil
2. Clique em "Editar"
3. Atualize as informações
4. Salve as alterações

Mantenha seus dados sempre atualizados para melhor atendimento.''',
        },
        {
          'title': 'Problemas com login?',
          'content': '''Se tiver problemas:
1. Verifique seu e-mail e senha
2. Use a opção "Esqueci a senha"
3. Verifique sua conexão
4. Limpe o cache do app
5. Entre em contato com suporte''',
        },
      ],
    },
    {
      'icon': Icons.support_agent,
      'title': 'Suporte',
      'topics': [
        {
          'title': 'Como obter ajuda?',
          'content': '''Canais de atendimento:
• Chat no aplicativo
• E-mail: <EMAIL>
• Telefone: 0800-123-4567
• WhatsApp: (33) 9 9849-7355

Horário de atendimento: Segunda a Sexta, das 8h às 18h''',
        },
        {
          'title': 'Relatar um problema',
          'content': '''Para relatar problemas:
1. Descreva o ocorrido, via e-mail ou whatsapp
2. Anexe prints se possível
3. Informe seu dispositivo
4. Envie logs do app
5. Aguarde retorno da equipe''',
        },
      ],
    },
  ];

  List<Map<String, dynamic>> get _filteredTopics {
    if (_searchQuery.isEmpty) {
      return _categories[_selectedCategoryIndex]['topics'];
    }

    final query = _searchQuery.toLowerCase();
    final allTopics = <Map<String, dynamic>>[];

    for (var category in _categories) {
      allTopics.addAll(category['topics']);
    }

    return allTopics.where((topic) {
      return topic['title'].toLowerCase().contains(query) ||
          topic['content'].toLowerCase().contains(query);
    }).toList();
  }

  @override
  void initState() {
    super.initState();
    // Inicializar os controllers
    _resetExpansionTileControllers();
  }
  
  // Método para redefinir os controllers quando mudar de categoria ou termos de pesquisa
  void _resetExpansionTileControllers() {
    // Verifica se houve mudança na categoria ou na pesquisa
    bool needsReset = _selectedCategoryIndex != _previousCategoryIndex || 
                      _searchQuery != _previousSearchQuery;
    
    if (needsReset) {
      // Atualizar valores anteriores
      _previousCategoryIndex = _selectedCategoryIndex;
      _previousSearchQuery = _searchQuery;
      
      // Reset do estado
      _expandedTileIndex = null;
      
      // Gerar nova key para forçar reconstrução dos ExpansionTiles
      _expansionTileKey = UniqueKey();
      
      // Obter tópicos filtrados para definir o número correto de controllers
      final topics = _filteredTopics;
      _tileControllers = List.generate(
        topics.length,
        (_) => ExpansionTileController(),
      );
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Verificar se houve mudança na categoria ou pesquisa e resetar controllers se necessário
    if (_selectedCategoryIndex != _previousCategoryIndex || 
        _searchQuery != _previousSearchQuery) {
      _resetExpansionTileControllers();
    }
    
    return Scaffold(
      body: GradientBackground(
        child: SafeArea(
          child: Column(
            children: [
              const SizedBox(height: 20),

              // Header com botão voltar
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.arrow_back),
                      onPressed: () => Get.back(),
                    ),
                    const Expanded(
                      child: Text(
                        'Central de Ajuda',
                        style: TextStyle(
                          fontFamily: 'Georgia',
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.chat, color: Colors.teal),
                      onPressed: () => Get.toNamed('/chatbot'),
                      tooltip: 'Assistente Virtual',
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),

              // Cartão destacado do chatbot
              if (_searchQuery.isEmpty) ...[
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Card(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    elevation: 3,
                    child: InkWell(
                      onTap: () => Get.toNamed('/chatbot'),
                      borderRadius: BorderRadius.circular(16),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.teal.withOpacity(0.1),
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.support_agent,
                                color: Colors.teal,
                                size: 30,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: const [
                                  Text(
                                    'Assistente Virtual',
                                    style: TextStyle(
                                      fontFamily: 'Georgia',
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.teal,
                                    ),
                                  ),
                                  SizedBox(height: 4),
                                  Text(
                                    'Tire suas dúvidas diretamente com nosso assistente inteligente',
                                    style: TextStyle(
                                      fontFamily: 'Georgia',
                                      fontSize: 14,
                                      color: Colors.black87,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const Icon(
                              Icons.arrow_forward_ios,
                              color: Colors.teal,
                              size: 16,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
              ],

              // Barra de pesquisa
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Pesquisar ajuda...',
                    prefixIcon: const Icon(Icons.search, color: Colors.teal),
                    filled: true,
                    fillColor: Colors.white,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(30),
                      borderSide: BorderSide.none,
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(30),
                      borderSide: BorderSide.none,
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(30),
                      borderSide: const BorderSide(color: Colors.teal),
                    ),
                  ),
                  onChanged: (value) => setState(() => _searchQuery = value),
                ),
              ),
              const SizedBox(height: 20),

              // Categorias e lista de tópicos dentro de um Expanded
              Expanded(
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  child: Column(
                    children: [
                      // Categorias
                      if (_searchQuery.isEmpty) ...[
                        SizedBox(
                          height: 100,
                          child: ListView.builder(
                            scrollDirection: Axis.horizontal,
                            padding: const EdgeInsets.symmetric(horizontal: 10),
                            itemCount: _categories.length,
                            itemBuilder: (context, index) {
                              final category = _categories[index];
                              final isSelected =
                                  _selectedCategoryIndex == index;

                              return GestureDetector(
                                onTap: () => setState(() {
                                  // Fechar todas as expansionTiles abertas antes de mudar de categoria
                                  if (_expandedTileIndex != null && 
                                      _expandedTileIndex! < _tileControllers.length) {
                                    _tileControllers[_expandedTileIndex!].collapse();
                                  }
                                  _expandedTileIndex = null;
                                  
                                  // Mudar de categoria após fechar as tiles
                                  _selectedCategoryIndex = index;
                                  
                                  // Forçar reconstrução de todo o widget
                                  _expansionTileKey = UniqueKey();
                                }),
                                child: Container(
                                  width: 80,
                                  margin:
                                      const EdgeInsets.symmetric(horizontal: 8),
                                  decoration: BoxDecoration(
                                    color:
                                        isSelected ? Colors.teal : Colors.white,
                                    borderRadius: BorderRadius.circular(15),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.1),
                                        blurRadius: 8,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        category['icon'],
                                        color: isSelected
                                            ? Colors.white
                                            : Colors.teal,
                                        size: 32,
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        category['title'],
                                        style: TextStyle(
                                          color: isSelected
                                              ? Colors.white
                                              : Colors.black87,
                                          fontFamily: 'Georgia',
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                        const SizedBox(height: 20),
                      ],

                      // Lista de tópicos
                      _filteredTopics.isEmpty && _searchQuery.isNotEmpty
                          ? SizedBox(
                              height: MediaQuery.of(context).size.height * 0.5,
                              child: Center(
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 30),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      const SizedBox(height: 20),
                                      Icon(
                                        Icons.search_off,
                                        size: 70,
                                        color: Colors.teal.withOpacity(0.7),
                                      ),
                                      const SizedBox(height: 16),
                                      const Text(
                                        'Nenhum resultado encontrado.',
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                          fontFamily: 'Georgia',
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.black87,
                                        ),
                                      ),
                                      const SizedBox(height: 8),
                                      const Text(
                                        'Tente usar outros termos de pesquisa.',
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                          fontFamily: 'Georgia',
                                          fontSize: 16,
                                          color: Colors.black54,
                                        ),
                                      ),
                                      const SizedBox(height: 40),
                                    ],
                                  ),
                                ),
                              ),
                            )
                          : ListView.builder(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              padding: const EdgeInsets.all(20),
                              itemCount: _filteredTopics.length,
                              itemBuilder: (context, index) {
                                final topic = _filteredTopics[index];
                                // Garantir que temos controllers suficientes
                                if (index >= _tileControllers.length) {
                                  _tileControllers.add(ExpansionTileController());
                                }
                                
                                return Container(
                                  key: ValueKey('container_${_selectedCategoryIndex}_$index'),
                                  margin: const EdgeInsets.only(bottom: 16),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(15),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.05),
                                        offset: const Offset(0, 2),
                                        blurRadius: 8,
                                      ),
                                    ],
                                  ),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(15),
                                    child: Theme(
                                      data: Theme.of(context).copyWith(
                                        dividerColor: Colors.transparent,
                                        colorScheme: ColorScheme.light(
                                          primary: Colors.teal,
                                        ),
                                      ),
                                      child: ExpansionTile(
                                        key: ValueKey('tile_${_selectedCategoryIndex}_$index'),
                                        controller: _tileControllers[index],
                                        initiallyExpanded: false,
                                        onExpansionChanged: (expanded) {
                                          setState(() {
                                            // Se este está expandindo
                                            if (expanded) {
                                              // Se tem um item expandido anteriormente
                                              if (_expandedTileIndex != null && _expandedTileIndex != index) {
                                                // Verifica se o índice anterior está dentro dos limites
                                                if (_expandedTileIndex! < _tileControllers.length) {
                                                  // Fecha o item anterior
                                                  _tileControllers[_expandedTileIndex!].collapse();
                                                }
                                              }
                                              _expandedTileIndex = index;
                                            } else if (_expandedTileIndex == index) {
                                              // Se é o mesmo item fechando
                                              _expandedTileIndex = null;
                                            }
                                          });
                                        },
                                        tilePadding: const EdgeInsets.symmetric(
                                          horizontal: 20,
                                          vertical: 16,
                                        ),
                                        expandedAlignment: Alignment.centerLeft,
                                        childrenPadding: EdgeInsets.zero,
                                        iconColor: Colors.teal,
                                        collapsedIconColor: Colors.teal,
                                        title: Text(
                                          topic['title'],
                                          style: const TextStyle(
                                            fontFamily: 'Georgia',
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.black87,
                                          ),
                                        ),
                                        children: [
                                          Container(
                                            width: double.infinity,
                                            decoration: BoxDecoration(
                                              color:
                                                  Colors.teal.withOpacity(0.05),
                                              border: Border(
                                                top: BorderSide(
                                                  color: Colors.teal
                                                      .withOpacity(0.1),
                                                ),
                                              ),
                                            ),
                                            padding: const EdgeInsets.all(20),
                                            child: Text(
                                              topic['content'],
                                              style: const TextStyle(
                                                fontFamily: 'Georgia',
                                                fontSize: 14,
                                                height: 1.6,
                                                color: Colors.black87,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
