import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../controllers/doctor_profile_controller.dart';
import 'package:fila_app/views/gradient_background.dart';
import 'dart:io';
import 'package:image_picker/image_picker.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';

class TelaPerfilMedico extends StatefulWidget {
  const TelaPerfilMedico({super.key});

  @override
  State<TelaPerfilMedico> createState() => _TelaPerfilMedicoState();
}

class _TelaPerfilMedicoState extends State<TelaPerfilMedico> {
  final _formKey = GlobalKey<FormState>();
  final _doctorController = DoctorProfileController();
  bool _isSaving = false;
  bool _isEditing = false;
  bool _controllersInitialized = false; // Nova variável para controlar a inicialização
  File? _profileImage;
  final ImagePicker _picker = ImagePicker();

  final _nomeController = TextEditingController();
  final _crmController = TextEditingController();
  final _cpfController = TextEditingController();
  final _especializacaoController = TextEditingController();
  final _emailController = TextEditingController();
  final _telefoneController = TextEditingController(); // Adicionando controlador para telefone
  bool _isActive = true;
  
  // Variáveis para pesquisa de satisfação
  bool _pesquisaSatisfacaoAtiva = false;
  List<String> _perguntasPersonalizadas = [];
  final _novaPerguntaController = TextEditingController();

  Future<void> _pickImage() async {
    final XFile? pickedImage = await _picker.pickImage(source: ImageSource.gallery);
    if (pickedImage != null) {
      setState(() {
        _profileImage = File(pickedImage.path);
      });
    }
  }

  @override
  void dispose() {
    _nomeController.dispose();
    _crmController.dispose();
    _cpfController.dispose();
    _especializacaoController.dispose();
    _emailController.dispose();
    _telefoneController.dispose(); // Liberar recursos do novo controlador
    _novaPerguntaController.dispose(); // Liberar recursos do controlador de perguntas
    super.dispose();
  }

  Future<void> _handleSave() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isSaving = true);

    try {
      print("Enviando status para o servidor: ${_isActive ? 'Ativo' : 'Inativo'}");
      
      // Guardar o valor antigo para comparar depois
      final bool statusAnterior = await _doctorController.isDoctorActive();
      
      // Converter o CPF para número (int) antes de enviar
      int cpfNumerico;
      try {
        cpfNumerico = int.parse(_cpfController.text.replaceAll(RegExp(r'[^0-9]'), ''));
      } catch (e) {
        _showErrorDialog("CPF inválido. Utilize apenas números.");
        setState(() => _isSaving = false);
        return;
      }
      
      final result = await _doctorController.updateDoctorProfile(
        nome: _nomeController.text,
        email: _emailController.text,
        crm: _crmController.text,
        cpf: cpfNumerico, // Enviando como inteiro diretamente
        especializacao: _especializacaoController.text,
        ativo: _isActive,
        telefone: _telefoneController.text,
        pesquisaSatisfacaoAtiva: _pesquisaSatisfacaoAtiva,
        perguntasPersonalizadas: _perguntasPersonalizadas,
        profileImage: _profileImage,
      );

      if (!mounted) return;

      if (result['success']) {
        setState(() => _isEditing = false);
        // Garantir que o status foi salvo corretamente
        print("Status salvo com sucesso: ${_isActive ? 'Ativo' : 'Inativo'}");
        
        // Se houve mudança no status, notificar os hospitais vinculados
        if (statusAnterior != _isActive) {
          print("Status do médico alterado de ${statusAnterior ? 'Ativo' : 'Inativo'} para ${_isActive ? 'Ativo' : 'Inativo'}");
          
          // Notificar todos os hospitais aos quais o médico está vinculado
          _notificarHospitaisSobreMudancaStatus();
        }
        
        _showSuccessDialog();
      } else {
        _showErrorDialog(result['error']);
      }
    } catch (e) {
      _showErrorDialog('Erro ao atualizar perfil: $e');
    } finally {
      if (mounted) setState(() => _isSaving = false);
    }
  }

  // Método para notificar hospitais sobre mudança de status do médico
  Future<void> _notificarHospitaisSobreMudancaStatus() async {
    try {
      final medicoId = _doctorController.currentDoctor?.objectId;
      if (medicoId == null) {
        debugPrint("[STATUS_MÉDICO] ❌ ID do médico não disponível para notificação");
        return;
      }
      
      final medicoNome = _nomeController.text;
      final acao = _isActive ? 'vincular' : 'desvincular';
      
      debugPrint("[STATUS_MÉDICO] 📡 Iniciando notificação de hospitais sobre alteração de status");
      debugPrint("[STATUS_MÉDICO] 👨‍⚕️ Médico: $medicoNome (ID: $medicoId)");
      debugPrint("[STATUS_MÉDICO] 🔄 Mudança: ${_isActive ? 'ATIVO ✅' : 'INATIVO ❌'}");
      
      // Obter lista de hospitais vinculados
      final hospitaisVinculados = await _doctorController.getLinkedHospitals();
      
      if (hospitaisVinculados.isEmpty) {
        debugPrint("[STATUS_MÉDICO] ⚠️ Nenhum hospital vinculado para notificar");
        return;
      }
      
      debugPrint("[STATUS_MÉDICO] 🏥 Enviando notificação para ${hospitaisVinculados.length} hospitais");
      
      // Para cada hospital vinculado, criar um objeto HospitalMedicoUpdate
      for (var hospital in hospitaisVinculados) {
        final hospitalId = hospital['id'] ?? hospital.objectId;
        final hospitalNome = hospital['nome'] ?? 'Hospital sem nome';
        
        if (hospitalId != null) {
          debugPrint("[STATUS_MÉDICO] 🏥 Notificando hospital: $hospitalNome (ID: $hospitalId)");
          
          // Criar objeto de notificação para o LiveQuery
          final params = {
            'hospitalId': hospitalId,
            'medicoId': medicoId,
            'medicoNome': medicoNome,
            'action': acao,
            'reason': 'status_change',
            'isActive': _isActive,
          };
          
          debugPrint("[STATUS_MÉDICO] 📤 Enviando parâmetros: $params");
          
          // Chamar função Cloud para criar notificação
          final ParseCloudFunction function = ParseCloudFunction('createHospitalMedicoUpdate');
          final result = await function.execute(parameters: params);
          
          if (result.success) {
            debugPrint("[STATUS_MÉDICO] ✅ Notificação enviada com sucesso para o hospital $hospitalNome");
          } else {
            debugPrint("[STATUS_MÉDICO] ❌ Erro ao enviar notificação: ${result.error?.message}");
          }
        }
      }
      
      debugPrint("[STATUS_MÉDICO] ✅ Processo de notificação de mudança de status concluído");
    } catch (e) {
      debugPrint("[STATUS_MÉDICO] ❌ Erro durante notificação de hospitais: $e");
    }
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: Column(
          children: const [
            Icon(
              Icons.check_circle_outline,
              color: Colors.green,
              size: 48,
            ),
            SizedBox(height: 16),
            Text(
              'Perfil Atualizado',
              style: TextStyle(fontFamily: 'Georgia'),
            ),
          ],
        ),
        content: const Text(
          'Suas informações foram atualizadas com sucesso!',
          textAlign: TextAlign.center,
          style: TextStyle(fontFamily: 'Georgia'),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text(
              'OK',
              style: TextStyle(
                color: Colors.teal,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: Column(
          children: const [
            Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 48,
            ),
            SizedBox(height: 16),
            Text(
              'Erro',
              style: TextStyle(fontFamily: 'Georgia'),
            ),
          ],
        ),
        content: Text(
          message,
          textAlign: TextAlign.center,
          style: const TextStyle(fontFamily: 'Georgia'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'OK',
              style: TextStyle(
                color: Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: GradientBackground(
        child: SafeArea(
          child: FutureBuilder<Map<String, dynamic>>(
            future: _doctorController.fetchDoctorProfile(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              } else if (snapshot.hasError) {
                return Center(child: Text('Erro: ${snapshot.error}'));
              } else if (!snapshot.hasData || !snapshot.data!['success']) {
                return const Center(child: Text('Erro ao carregar perfil'));
              } else {
                final data = snapshot.data!['data'];
                
                // Inicializa os controladores apenas uma vez ao carregar a tela
                if (!_controllersInitialized) {
                  _nomeController.text = data['nome'] ?? '';
                  _emailController.text = data['email'] ?? '';
                  _crmController.text = data['crm'] ?? '';
                  
                  // Formatar CPF se for numérico
                  if (data['cpf'] != null) {
                    String cpfStr = data['cpf'].toString();
                    if (cpfStr.length == 11) {
                      _cpfController.text = '${cpfStr.substring(0, 3)}.${cpfStr.substring(3, 6)}.${cpfStr.substring(6, 9)}-${cpfStr.substring(9, 11)}';
                    } else {
                      _cpfController.text = cpfStr;
                    }
                  }
                  
                  _especializacaoController.text = data['especialidade'] ?? ''; // Corrigido para 'especialidade'
                  _telefoneController.text = data['telefone'] ?? '';
                  _isActive = data['ativo'] ?? true;
                  _pesquisaSatisfacaoAtiva = data['pesquisaSatisfacaoAtiva'] ?? false;
                  
                  // Garantir que a lista de perguntas seja inicializada corretamente
                  if (data['perguntasPersonalizadas'] != null) {
                    _perguntasPersonalizadas = List<String>.from(data['perguntasPersonalizadas']);
                  }
                  
                  debugPrint('Dados carregados:');
                  debugPrint('Nome: ${_nomeController.text}');
                  debugPrint('CRM: ${_crmController.text}');
                  debugPrint('CPF: ${_cpfController.text}');
                  debugPrint('Especialidade: ${_especializacaoController.text}');
                  debugPrint('Telefone: ${_telefoneController.text}');
                  
                  _controllersInitialized = true;
                }

                return ListView(
                  padding: const EdgeInsets.all(20),
                  children: [
                    _buildHeader(),
                    _buildForm(),
                  ],
                );
              }
            },
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    // Destaque para o status (ativo/inativo) no header
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            if (!_isEditing)
              IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.black, size: 32),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            if (_isEditing)
              const SizedBox(width: 48), // Espaço para manter alinhamento
            Expanded(
              child: Center(
                child: Stack(
                  alignment: Alignment.bottomRight,
                  children: [
                    GestureDetector(
                      onTap: _isEditing ? _pickImage : null,
                      child: CircleAvatar(
                        radius: 50,
                        backgroundImage: _profileImage != null 
                            ? FileImage(_profileImage!) 
                            : null,
                        backgroundColor: Colors.teal.shade100,
                        child: _profileImage == null
                            ? Text(
                                _nomeController.text.isNotEmpty
                                    ? _nomeController.text[0].toUpperCase()
                                    : 'M',
                                style: TextStyle(
                                  fontSize: 40,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.teal.shade700,
                                ),
                              )
                            : null,
                      ),
                    ),
                    if (_isEditing)
                      Positioned(
                        right: 0,
                        bottom: 0,
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: const BoxDecoration(
                            color: Colors.teal,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.edit,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                      ),
                    if (!_isEditing)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: _isActive ? Colors.green : Colors.red,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          _isActive ? 'Ativo' : 'Inativo',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
            const SizedBox(width: 48), // Espaço para manter alinhamento
          ],
        ),
        
        // Status mais visível abaixo do nome para enfatizar
        if (!_isEditing) ...[
          const SizedBox(height: 10),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: _isActive ? Colors.green.withOpacity(0.1) : Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: _isActive ? Colors.green.withOpacity(0.3) : Colors.red.withOpacity(0.3),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  _isActive ? Icons.check_circle_outline : Icons.cancel_outlined,
                  color: _isActive ? Colors.green : Colors.red,
                  size: 18,
                ),
                const SizedBox(width: 6),
                Text(
                  'Status: ${_isActive ? 'Ativo' : 'Inativo'}',
                  style: TextStyle(
                    fontFamily: 'Georgia',
                    fontSize: 14,
                    color: _isActive ? Colors.green : Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
        
        const SizedBox(height: 20),
        Text(
          _nomeController.text,
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            fontFamily: 'Georgia',
          ),
        ),
        Text(
          _especializacaoController.text,
          style: TextStyle(
            fontSize: 18,
            color: Colors.grey[600],
            fontFamily: 'Georgia',
          ),
        ),
      ],
    );
  }

  Widget _buildForm() {
    return Container(
      margin: const EdgeInsets.only(top: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Form(
        key: _formKey,
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Informações Pessoais',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Georgia',
                    ),
                  ),
                  IconButton(
                    icon: Icon(
                      _isEditing ? Icons.lock_open : Icons.lock,
                      color: Colors.teal,
                    ),
                    onPressed: () {
                      setState(() => _isEditing = !_isEditing);
                    },
                  ),
                ],
              ),
              const SizedBox(height: 20),
              _buildField(
                label: "Nome",
                controller: _nomeController,
                enabled: _isEditing,
                icon: Icons.person_outline,
              ),
              _buildField(
                label: "E-mail",
                controller: _emailController,
                enabled: _isEditing,
                icon: Icons.email_outlined,
                keyboardType: TextInputType.emailAddress,
              ),
              _buildField(
                label: "CRM",
                controller: _crmController,
                enabled: _isEditing,
                icon: Icons.badge_outlined,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                ],
              ),
              _buildField(
                label: "CPF",
                controller: _cpfController,
                enabled: _isEditing,
                icon: Icons.credit_card,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                ],
              ),
              _buildField(
                label: "Especialização",
                controller: _especializacaoController,
                enabled: _isEditing,
                icon: Icons.medical_services_outlined,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Por favor, informe a especialização';
                  }
                  return null;
                },
              ),
              _buildField(
                label: "Telefone",
                controller: _telefoneController,
                enabled: _isEditing,
                icon: Icons.phone,
                keyboardType: TextInputType.phone,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                ],
              ),
              const SizedBox(height: 20),
              _buildStatusSection(),
              const SizedBox(height: 20),
              _buildPesquisaSatisfacaoSection(),
              if (_isEditing) ...[
                const SizedBox(height: 30),
                ElevatedButton(
                  onPressed: _isSaving ? null : _handleSave,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.teal,
                    padding: const EdgeInsets.symmetric(vertical: 15),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: _isSaving
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        )
                      : const Text(
                          'Salvar Alterações',
                          style: TextStyle(
                            fontSize: 16,
                            fontFamily: 'Georgia',
                            color: Colors.white,
                          ),
                        ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  // Seção para status do médico (ativo/inativo)
  Widget _buildStatusSection() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _isActive ? Colors.green.withOpacity(0.1) : Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _isActive ? Colors.green.withOpacity(0.3) : Colors.red.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const Text(
            'Status do Perfil',
            style: TextStyle(
              fontFamily: 'Georgia',
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(
                    _isActive 
                        ? Icons.check_circle_outline 
                        : Icons.cancel_outlined,
                    color: _isActive ? Colors.green : Colors.red,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _isActive ? 'Perfil Ativo' : 'Perfil Inativo',
                    style: TextStyle(
                      fontFamily: 'Georgia',
                      fontSize: 14,
                      color: _isActive ? Colors.green : Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              if (_isEditing)
                Transform.scale(
                  scale: 1.2,
                  child: Switch(
                    value: _isActive,
                    onChanged: (value) {
                      // Garantir que o estado seja atualizado imediatamente
                      setState(() {
                        _isActive = value;
                        print("Status alterado para: ${value ? 'Ativo' : 'Inativo'}");
                      });
                    },
                    activeColor: Colors.green,
                    activeTrackColor: Colors.green.shade200,
                    inactiveThumbColor: Colors.red,
                    inactiveTrackColor: Colors.red.shade200,
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  // Nova seção para pesquisa de satisfação
  Widget _buildPesquisaSatisfacaoSection() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _pesquisaSatisfacaoAtiva 
            ? Colors.green.withOpacity(0.1) 
            : Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _pesquisaSatisfacaoAtiva 
              ? Colors.green.withOpacity(0.3) 
              : Colors.red.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const Text(
            'Pesquisa de Satisfação',
            style: TextStyle(
              fontFamily: 'Georgia',
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(
                    _pesquisaSatisfacaoAtiva 
                        ? Icons.check_circle_outline 
                        : Icons.cancel_outlined,
                    color: _pesquisaSatisfacaoAtiva ? Colors.green : Colors.red,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _pesquisaSatisfacaoAtiva ? 'Pesquisa Ativa' : 'Pesquisa Desativada',
                    style: TextStyle(
                      fontFamily: 'Georgia',
                      fontSize: 14,
                      color: _pesquisaSatisfacaoAtiva ? Colors.green : Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              if (_isEditing)
                Transform.scale(
                  scale: 1.2,
                  child: Switch(
                    value: _pesquisaSatisfacaoAtiva,
                    onChanged: (value) {
                      setState(() {
                        _pesquisaSatisfacaoAtiva = value;
                        if (value && _perguntasPersonalizadas.isEmpty) {
                          _mostrarDialogoPerguntas();
                        }
                      });
                    },
                    activeColor: Colors.green,
                    activeTrackColor: Colors.green.shade200,
                    inactiveThumbColor: Colors.red,
                    inactiveTrackColor: Colors.red.shade200,
                  ),
                ),
            ],
          ),
          if (_pesquisaSatisfacaoAtiva) ...[
            const SizedBox(height: 10),
            // Exibir as perguntas personalizadas
            if (_perguntasPersonalizadas.isNotEmpty) ...[
              const Text(
                'Perguntas Personalizadas:',
                style: TextStyle(
                  fontFamily: 'Georgia',
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 5),
              ...List.generate(
                _perguntasPersonalizadas.length,
                (index) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4.0),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          '${index + 1}. ${_perguntasPersonalizadas[index]}',
                          style: const TextStyle(
                            fontFamily: 'Georgia',
                            fontSize: 14,
                          ),
                        ),
                      ),
                      if (_isEditing)
                        IconButton(
                          icon: const Icon(Icons.delete, color: Colors.red, size: 20),
                          onPressed: () {
                            setState(() {
                              _perguntasPersonalizadas.removeAt(index);
                            });
                          },
                        ),
                    ],
                  ),
                ),
              ),
            ],
            if (_isEditing) ...[
              const SizedBox(height: 10),
              ElevatedButton.icon(
                onPressed: _mostrarDialogoPerguntas,
                icon: const Icon(Icons.edit, color: Colors.white),
                label: const Text(
                  'Editar Perguntas',
                  style: TextStyle(
                    fontFamily: 'Georgia',
                    fontSize: 14,
                    color: Colors.white,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.teal,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
          ],
        ],
      ),
    );
  }

  // Método para exibir o diálogo de edição de perguntas
  void _mostrarDialogoPerguntas() {
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text(
            'Perguntas da Pesquisa',
            style: TextStyle(
              fontFamily: 'Georgia',
              fontWeight: FontWeight.bold,
            ),
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'Adicione perguntas personalizadas para a pesquisa de satisfação:',
                  style: TextStyle(fontFamily: 'Georgia'),
                ),
                const SizedBox(height: 15),
                // Lista de perguntas atuais
                for (int i = 0; i < _perguntasPersonalizadas.length; i++)
                  ListTile(
                    title: Text(
                      _perguntasPersonalizadas[i],
                      style: const TextStyle(fontFamily: 'Georgia', fontSize: 14),
                    ),
                    trailing: IconButton(
                      icon: const Icon(Icons.delete, color: Colors.red),
                      onPressed: () {
                        setState(() {
                          _perguntasPersonalizadas.removeAt(i);
                        });
                      },
                    ),
                    contentPadding: EdgeInsets.zero,
                  ),
                const SizedBox(height: 10),
                // Campo para adicionar nova pergunta
                TextField(
                  controller: _novaPerguntaController,
                  decoration: const InputDecoration(
                    labelText: 'Nova Pergunta',
                    border: OutlineInputBorder(),
                    hintText: 'Digite a pergunta aqui',
                  ),
                  maxLines: 2,
                  style: const TextStyle(fontFamily: 'Georgia'),
                ),
                const SizedBox(height: 10),
                ElevatedButton.icon(
                  onPressed: () {
                    if (_novaPerguntaController.text.isNotEmpty) {
                      setState(() {
                        _perguntasPersonalizadas.add(_novaPerguntaController.text);
                        _novaPerguntaController.clear();
                      });
                    }
                  },
                  icon: const Icon(Icons.add, color: Colors.white),
                  label: const Text(
                    'Adicionar Pergunta',
                    style: TextStyle(color: Colors.white),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.teal,
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancelar'),
            ),
            ElevatedButton(
              onPressed: () {
                // Atualiza o estado na tela principal e fecha o diálogo
                this.setState(() {});
                Navigator.of(context).pop();
              },
              child: const Text('Confirmar'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.teal,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildField({
    required String label,
    required TextEditingController controller,
    bool enabled = true,
    IconData? icon,
    List<TextInputFormatter>? inputFormatters,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: TextFormField(
        controller: controller,
        enabled: enabled,
        keyboardType: keyboardType,
        inputFormatters: inputFormatters,
        style: TextStyle(
          fontFamily: 'Georgia',
          color: enabled ? Colors.black87 : Colors.grey,
          fontSize: 16,
        ),
        decoration: InputDecoration(
          labelText: label,
          labelStyle: TextStyle(
            fontFamily: 'Georgia',
            color: Colors.grey[700],
            fontSize: 16,
          ),
          prefixIcon: icon != null ? Icon(icon, color: Colors.teal) : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Colors.teal, width: 2),
          ),
          disabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.shade200),
          ),
          filled: true,
          fillColor: enabled ? Colors.white : Colors.grey.shade50,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
        validator: validator ?? (value) {
          if (value == null || value.isEmpty) {
            return 'Por favor, preencha este campo';
          }
          return null;
        },
      ),
    );
  }
}
