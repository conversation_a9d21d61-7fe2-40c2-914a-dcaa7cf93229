import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fila_app/views/gradient_background.dart';
import '../controllers/confirmacao_saida_controller.dart';

class ConfirmacaoSairFilaPacienteScreen
    extends GetView<ConfirmacaoSaidaController> {
  const ConfirmacaoSairFilaPacienteScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        // Usar Get.back() em vez de Get.offAllNamed() para preservar a sessão
        Get.back();
        return false;
      },
      child: Scaffold(
        body: GradientBackground(
          child: SafeArea(
            child: Obx(() {
              // Verificar se a pesquisa está ativa
              if (!controller.isPesquisaAtiva.value) {
                // Se a pesquisa não estiver ativa, mostrar apenas o botão para sair da fila
                return _buildConfirmacaoSimples();
              }
              
              // Se a pesquisa estiver ativa, mostrar o formulário completo
              return ListView(
                padding: EdgeInsets.symmetric(
                  horizontal: Get.width * 0.05,
                  vertical: Get.height * 0.02,
                ),
                children: [
                  _buildFeedbackCard(),
                  SizedBox(height: Get.height * 0.03),
                  _buildNavigationButtons(),
                ],
              );
            }),
          ),
        ),
      ),
    );
  }

  // Widget simplificado para quando a pesquisa não estiver ativa
  Widget _buildConfirmacaoSimples() {
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: Get.width * 0.1),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              'assets/confirmar.png',
              width: 100,
              height: 100,
            ),
            const SizedBox(height: 30),
            const Text(
              'Confirmar saída da fila?',
              style: TextStyle(
                fontFamily: 'Georgia',
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            const Text(
              'Você está saindo da fila de atendimento. Deseja confirmar?',
              style: TextStyle(
                fontFamily: 'Georgia',
                fontSize: 18,
                color: Colors.black,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 50),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildNavigationButton(
                  'Voltar',
                  'assets/leftG.png',
                  () {
                    Get.back();
                  },
                  isBackButton: true,
                ),
                _buildNavigationButton(
                  'Confirmar',
                  null,
                  controller.salvarFeedbackESairDaFila,
                  isConfirm: true,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeedbackCard() {
    return Card(
      color: Colors.white,
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Padding(
        padding: EdgeInsets.all(Get.width * 0.05),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Questionário de\nSatisfação',
              style: TextStyle(
                fontFamily: 'Georgia',
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
            SizedBox(height: Get.height * 0.02),
            const Text(
              'Por qual motivo você está saindo da fila?',
              style: TextStyle(
                fontFamily: 'Georgia',
                fontSize: 18,
                color: Colors.black,
              ),
            ),
            SizedBox(height: Get.height * 0.03),
            _buildOptionsColumn(),
            const SizedBox(height: 25),
            _buildTextField(),
            // Perguntas personalizadas, se houver
            _buildPerguntasPersonalizadas(),
          ],
        ),
      ),
    );
  }

  Widget _buildOptionsColumn() {
    final options = [
      {
        'label': 'Demora no atendimento',
        'rxBool': controller.demoraNoAtendimento,
        'option': 'demoraNoAtendimento'
      },
      {
        'label': 'Imprevistos',
        'rxBool': controller.imprevistos,
        'option': 'imprevistos'
      },
      {
        'label': 'Atendimento em outro local',
        'rxBool': controller.atendimentoOutroLocal,
        'option': 'atendimentoOutroLocal'
      },
      {
        'label': 'Fila grande',
        'rxBool': controller.filaGrande,
        'option': 'filaGrande'
      },
      {
        'label': 'Sem previsão de atendimento',
        'rxBool': controller.semPrevisaoAtendimento,
        'option': 'semPrevisaoAtendimento'
      },
      {
        'label': 'Prefiro não responder',
        'rxBool': controller.prefiroNaoResponder,
        'option': 'prefiroNaoResponder'
      },
    ];

    return Column(
      children: options
          .map(
            (option) => Padding(
              padding: EdgeInsets.only(bottom: Get.height * 0.02),
              child: _buildOption(
                option['label'] as String,
                option['rxBool'] as RxBool,
                option['option'] as String,
              ),
            ),
          )
          .toList(),
    );
  }

  Widget _buildOption(String label, RxBool rxBool, String option) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              label,
              style: const TextStyle(
                fontFamily: 'Georgia',
                fontSize: 16,
                color: Colors.black,
              ),
            ),
          ),
          Obx(() => Transform.scale(
                scale: 1.2,
                child: Checkbox(
                  value: rxBool.value,
                  onChanged: controller.isSaving.value
                      ? null
                      : (_) => controller.toggleOption(option),
                  activeColor: Colors.black,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                  ),
                  side: const BorderSide(width: 2),
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildTextField() {
    return TextField(
      onChanged: (value) => controller.outroMotivo.value = value,
      decoration: const InputDecoration(
        border: OutlineInputBorder(),
        labelText: 'Outro Motivo',
        labelStyle: TextStyle(
          fontFamily: 'Georgia',
          color: Colors.black,
        ),
      ),
    );
  }

  // Novo método para construir perguntas personalizadas
  Widget _buildPerguntasPersonalizadas() {
    return Obx(() {
      if (controller.perguntasPersonalizadas.isEmpty) {
        return const SizedBox.shrink();
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 25),
          const Text(
            'Perguntas Adicionais',
            style: TextStyle(
              fontFamily: 'Georgia',
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 15),
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: controller.perguntasPersonalizadas.length,
            itemBuilder: (context, index) {
              return Container(
                margin: const EdgeInsets.only(bottom: 15),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      controller.perguntasPersonalizadas[index],
                      style: const TextStyle(
                        fontFamily: 'Georgia',
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 10),
                    TextField(
                      decoration: const InputDecoration(
                        hintText: 'Digite sua resposta aqui',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 2,
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      );
    });
  }

  Widget _buildNavigationButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildNavigationButton(
          'Voltar',
          'assets/leftG.png',
          () {
            // Usar Get.back() em vez de Get.offAllNamed() para preservar a sessão
            Get.back();
          },
          isBackButton: true,
        ),
        _buildNavigationButton(
          'Confirmar',
          null,
          controller.salvarFeedbackESairDaFila,
          isConfirm: true,
        ),
      ],
    );
  }

  Widget _buildNavigationButton(
    String label,
    String? iconPath,
    VoidCallback onTap, {
    bool isBackButton = false,
    bool isConfirm = false,
  }) {
    return Obx(() => GestureDetector(
          onTap: controller.isSaving.value ? null : onTap,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (controller.isSaving.value && isConfirm)
                SizedBox(
                  width: Get.width * 0.12,
                  height: Get.width * 0.12,
                  child: const CircularProgressIndicator(
                    strokeWidth: 3,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                  ),
                )
              else if (iconPath != null)
                Image.asset(
                  iconPath,
                  width: Get.width * 0.12,
                  height: Get.width * 0.12,
                )
              else
                Icon(
                  Icons.check_circle_outline,
                  size: Get.width * 0.12,
                  color: Colors.black,
                ),
              SizedBox(height: Get.height * 0.01),
              Text(
                label,
                style: const TextStyle(
                  fontFamily: 'Georgia',
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
            ],
          ),
        ));
  }
}
