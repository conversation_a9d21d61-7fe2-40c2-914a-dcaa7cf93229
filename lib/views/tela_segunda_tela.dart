import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fila_app/views/gradient_background.dart';

class TipodeAcessoScreen extends StatelessWidget {
  const TipodeAcessoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientBackground(
        child: Column(
          children: [
            const SizedBox(height: 40),
            <PERSON><PERSON>(
              alignment: Alignment.centerLeft,
              child: IconButton(
                icon: Image.asset('assets/left.png'),
                iconSize: 40,
                onPressed: () {
                  Navigator.pop(context);
                },
              ),
            ),
            const SizedBox(height: 60),
            GestureDetector(
              onTap: () {
                Get.toNamed('/medico'); // Rota para a tela do médico
              },
              child: buildOptionButton(context, 'MÉDICO',
                  'assets/logo-administrador-removebg-preview-1-5.png'),
            ),
            const SizedBox(height: 40),
            GestureDetector(
              onTap: () {
                Get.toNamed('/hospital'); // Rota para a tela do hospital
              },
              child: buildOptionButton(context, 'HOSPITAL',
                  'assets/logo-administrador-removebg-preview-1-5.png'),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildOptionButton(
      BuildContext context, String label, String iconPath) {
    return Column(
      children: [
        Image.asset(
          iconPath,
          width: 76,
          height: 81,
        ),
        const SizedBox(height: 10),
        Text(
          label,
          style: const TextStyle(
            fontFamily: 'Georgia',
            fontSize: 36,
            fontWeight: FontWeight.w400,
          ),
        ),
      ],
    );
  }
}
