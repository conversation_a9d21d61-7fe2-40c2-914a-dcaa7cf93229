// lib/main.dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart' as dotenv;
import 'package:fila_app/controllers/login_controller.dart';
import 'package:fila_app/controllers/notification_controller.dart';
import 'package:fila_app/controllers/user_data_controller.dart'; // Importar UserDataController
import 'package:fila_app/services/auth_service.dart';
import 'package:fila_app/services/connectivity_service.dart';
import 'package:fila_app/services/sync_manager.dart';
import 'package:fila_app/services/push_notification_service.dart';
import 'package:fila_app/services/throttling_service.dart'; // Importar serviço de throttling
import 'package:fila_app/conexao.dart';
import 'package:fila_app/routes/app_routes.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:fila_app/utils/ui_utils.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:fila_app/utils/api_cache_manager.dart';
import 'package:fila_app/modules/admin/admin_module.dart';

// Handler para mensagens em background
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // Garantir que Firebase está inicializado mesmo em background
  await Firebase.initializeApp();
  print('Mensagem recebida em background: ${message.notification?.title}');
  print('Dados da mensagem: ${message.data}');
}

void main() async {
  try {
    // 1. Inicialização do Flutter
    WidgetsFlutterBinding.ensureInitialized();

    print('===== INICIANDO SAUDE SEM ESPERA APP =====');

    // 2. Carregar variáveis de ambiente
    await dotenv.dotenv.load();
    print('Variáveis de ambiente carregadas');

    // 3. Configurar orientação do dispositivo
    await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

    // 4. Configurar UI do sistema
    UiUtils.setTranslucentStatusBar();
    UiUtils.setEdgeToEdgeDisplay();

    // 5. Inicializar Firebase ANTES de tudo
    print('Inicializando Firebase...');
    await Firebase.initializeApp();

    // 5.1 Configurar handler para mensagens em background
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

    // 5.2 Obter e mostrar token FCM no console
    final token = await FirebaseMessaging.instance.getToken();
    print('\n========== FCM TOKEN ==========');
    print(token);
    print('===============================\n');

    // 6. Solicitar permissões para notificações
    final settings = await FirebaseMessaging.instance.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );
    print(
        'Status de permissão de notificação: ${settings.authorizationStatus}');

    // 7. Inicializar conexão com o backend
    print('Conectando ao Parse Server...');
    await Conexao.initialize();

    // 8. Inicializar controllers globais
    print('Inicializando controllers...');
    Get.put(LoginController());
    Get.put(NotificationController());

    // Inicializar UserDataController antes dos serviços
    print('Inicializando UserDataController...');
    final userDataController = UserDataController();
    Get.put(userDataController, permanent: true);

    // Garantir que os dados do usuário sejam carregados
    await userDataController.checkUserData();
    print(
        'UserDataController inicializado: ${userDataController.userDataExists.value}');

    // 9. Inicializar serviços
    print('Inicializando serviços...');
    await initServices();

    // 10. Executar o aplicativo
    print('Inicializando interface...');
    runApp(const MyApp());
  } catch (e, stackTrace) {
    // Em caso de erro durante a inicialização, registrar detalhes e iniciar o app
    print('ERRO NA INICIALIZAÇÃO: $e');
    print('Stack trace: $stackTrace');
    debugPrint('Erro na inicialização: $e');
    runApp(const MyApp());
  }
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  String initialRoute = '/';
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _checkCurrentUser();
  }

  Future<void> _checkCurrentUser() async {
    try {
      final result = await AuthService.checkCurrentUser();
      if (result['success'] && result.containsKey('route')) {
        setState(() {
          initialRoute = result['route'];
          isLoading = false;
        });
      } else {
        setState(() {
          initialRoute = '/';
          isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Erro ao verificar usuário: $e');
      setState(() {
        initialRoute = '/';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return MaterialApp(
        debugShowCheckedModeBanner: false,
        home: Scaffold(
          body: Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
    }

    return GetMaterialApp(
      title: 'Fila App',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      localizationsDelegates: GlobalMaterialLocalizations.delegates,
      supportedLocales: const [Locale('pt', 'BR')],
      initialRoute: initialRoute,
      getPages: AppRoutes.getPages(initialRoute: initialRoute),
    );
  }
}

Future<void> initServices() async {
  // Registrar serviços com GetX
  Get.put(ConnectivityService(), permanent: true);
  Get.put(SyncManager(), permanent: true);

  // Registrar serviço de throttling
  Get.put(ThrottlingService(), permanent: true);
  print('Serviço de Throttling inicializado - limite de requisições: 10/s');

  // Inicializar e registrar o serviço de notificações push
  final pushNotificationService = PushNotificationService();
  await pushNotificationService.init();
  Get.put(pushNotificationService, permanent: true);

  // Inicializar gerenciador de cache
  await ApiCacheManager().initialize();

  // Inicializar serviço de notificações em segundo plano
  // para não bloquear a inicialização do app
  PushNotificationService().initialize();

  // Initialize modules
  AdminModule.init();
}
