import 'package:intl/intl.dart';

String formatarTempoRelativo(DateTime? dataHora) {
  if (dataHora == null) {
    return 'Data não disponível';
  }

  final agora = DateTime.now();
  final diferenca = agora.difference(dataHora);

  if (diferenca.inSeconds < 60) {
    return 'Agora';
  } else if (diferenca.inMinutes < 60) {
    return '${diferenca.inMinutes} ${diferenca.inMinutes == 1 ? 'minuto' : 'minutos'} atrás';
  } else if (diferenca.inHours < 24) {
    return '${diferenca.inHours} ${diferenca.inHours == 1 ? 'hora' : 'horas'} atrás';
  } else if (diferenca.inDays < 30) {
    return '${diferenca.inDays} ${diferenca.inDays == 1 ? 'dia' : 'dias'} atrás';
  } else {
    return DateFormat('dd/MM/yyyy HH:mm').format(dataHora);
  }
}
