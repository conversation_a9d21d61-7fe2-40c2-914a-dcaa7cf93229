class ValidadoresRegistro {
  // Validação básica de CRM: XXXXX-UF
  static bool validarCRM(String crm) {
    final RegExp regExp = RegExp(r'^\d{5,6}(-[A-Z]{2})?$');
    return regExp.hasMatch(crm);
  }
  
  // Validação básica de CRO: XXXXX-UF
  static bool validarCRO(String cro) {
    final RegExp regExp = RegExp(r'^\d{5,6}(-[A-Z]{2})?$');
    return regExp.hasMatch(cro);
  }
  
  // Validação básica de CREFITO: XXXXX-X/UF
  static bool validarCREFITO(String crefito) {
    final RegExp regExp = RegExp(r'^\d{5,6}-\d{1}(/[A-Z]{2})?$');
    return regExp.hasMatch(crefito);
  }
  
  // Validação básica de CRP: XXXXX-UF
  static bool validarCRP(String crp) {
    final RegExp regExp = RegExp(r'^\d{5,6}(-[A-Z]{2})?$');
    return regExp.hasMatch(crp);
  }
  
  // Validação básica de CRN: XXXXX-UF
  static bool validarCRN(String crn) {
    final RegExp regExp = RegExp(r'^\d{5,6}(-[A-Z]{2})?$');
    return regExp.hasMatch(crn);
  }
  
  // Função genérica que escolhe o validador correto com base no tipo
  static bool validar(String registro, String tipo) {
    registro = registro.toUpperCase();
    
    switch (tipo) {
      case 'CRM':
        return validarCRM(registro);
      case 'CRO':
        return validarCRO(registro);
      case 'CREFITO':
        return validarCREFITO(registro);
      case 'CRP':
        return validarCRP(registro);
      case 'CRN':
        return validarCRN(registro);
      // Adicione mais casos conforme necessário
      default:
        // Caso não exista validador específico, aceita qualquer formato
        return registro.isNotEmpty;
    }
  }
}
