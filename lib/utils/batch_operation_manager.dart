import 'package:fila_app/utils/api_cache_manager.dart';
import 'package:flutter/foundation.dart';

/// Gerenciador para operações em lote no Back4App
/// Permite agrupar múltiplas requisições em uma única chamada
class BatchOperationManager {
  static final BatchOperationManager _instance =
      BatchOperationManager._internal();

  final ApiCacheManager _cacheManager = ApiCacheManager();
  final List<BatchOperation> _pendingOperations = [];
  bool _isBatchInProgress = false;

  factory BatchOperationManager() {
    return _instance;
  }

  BatchOperationManager._internal();

  /// Adiciona uma operação ao lote atual
  void addOperation(BatchOperation operation) {
    _pendingOperations.add(operation);
  }

  /// Adiciona uma operação de consulta de fila ao lote
  void addFilaOperation({
    required String medicoId,
    required String consultorioId,
    required String operationId,
  }) {
    addOperation(
      BatchOperation(
        operationId: operationId,
        type: 'fila',
        params: {
          'medicoId': medicoId,
          'consultorioId': consultorioId,
        },
      ),
    );
  }

  /// Adiciona uma operação de consulta de mensagens ao lote
  void addMensagensOperation({
    required String consultorioId,
    required String operationId,
  }) {
    addOperation(
      BatchOperation(
        operationId: operationId,
        type: 'mensagensFila',
        params: {
          'consultorioId': consultorioId,
        },
      ),
    );
  }

  /// Limpa todas as operações pendentes
  void clearOperations() {
    _pendingOperations.clear();
  }

  /// Executa todas as operações pendentes em um único batch
  Future<Map<String, dynamic>> executeBatch({
    Duration? maxAge,
    bool forceRefresh = false,
  }) async {
    if (_pendingOperations.isEmpty) {
      return {};
    }

    if (_isBatchInProgress) {
      debugPrint('Batch já em execução, aguardando...');
      await Future.delayed(const Duration(milliseconds: 500));
      if (_isBatchInProgress) {
        throw Exception('Batch já em execução. Tente novamente mais tarde.');
      }
    }

    try {
      _isBatchInProgress = true;

      final operations = List<Map<String, dynamic>>.from(
          _pendingOperations.map((op) => op.toJson()));

      final results =
          await _cacheManager.executeCloudFunction<Map<String, dynamic>>(
        functionName: 'batchGetData',
        params: {
          'operations': operations,
        },
        maxAge: maxAge,
        forceRefresh: forceRefresh,
      );

      // Limpar operações após execução bem-sucedida
      clearOperations();

      return results;
    } catch (e) {
      debugPrint('Erro ao executar batch: $e');
      rethrow;
    } finally {
      _isBatchInProgress = false;
    }
  }

  /// Executa um batch específico para dados do dashboard
  Future<Map<String, dynamic>> getDashboardData({
    required String medicoId,
    required String consultorioId,
    Duration? maxAge,
    bool forceRefresh = false,
  }) async {
    try {
      return await _cacheManager.executeCloudFunction<Map<String, dynamic>>(
        functionName: 'getDashboardData',
        params: {
          'medicoId': medicoId,
          'consultorioId': consultorioId,
        },
        maxAge: maxAge ?? const Duration(seconds: 30),
        forceRefresh: forceRefresh,
      );
    } catch (e) {
      debugPrint('Erro ao obter dados do dashboard: $e');
      rethrow;
    }
  }
}

/// Classe que representa uma operação em lote
class BatchOperation {
  final String operationId;
  final String type;
  final Map<String, dynamic> params;

  BatchOperation({
    required this.operationId,
    required this.type,
    required this.params,
  });

  Map<String, dynamic> toJson() {
    return {
      'operationId': operationId,
      'type': type,
      'params': params,
    };
  }
}
