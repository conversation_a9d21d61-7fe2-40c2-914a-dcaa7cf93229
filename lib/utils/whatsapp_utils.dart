import 'package:url_launcher/url_launcher.dart';

class WhatsAppUtils {
  static Future<bool> abrirWhatsApp(String telefone, {String? mensagem}) async {
    final numeroLimpo = telefone.replaceAll(RegExp(r'[^0-9]'), '');
    final numeroCompleto =
        numeroLimpo.startsWith('55') ? numeroLimpo : '55$numeroLimpo';
    final mensagemEncoded = Uri.encodeComponent(
        mensagem ?? 'Olá! Entrando em contato sobre sua consulta.');

    final url =
        Uri.parse('https://wa.me/$numeroCompleto?text=$mensagemEncoded');
    return await launchUrl(url, mode: LaunchMode.externalApplication);
  }
}
