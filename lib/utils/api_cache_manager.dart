import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'package:flutter/foundation.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'dart:io';

/// Um gerenciador de cache para otimizar requisições ao Back4App
class ApiCacheManager {
  static final ApiCacheManager _instance = ApiCacheManager._internal();

  // Cache em memória para acesso mais rápido
  final Map<String, dynamic> _memoryCache = {};

  // Registro de timestamps para gerenciar expiração
  final Map<String, DateTime> _cacheTimestamps = {};

  // ID de dispositivo para rate limiting
  String? _deviceId;

  // Registro de timestamps para rate limiting
  final Map<String, DateTime> _lastRequestTimes = {};

  factory ApiCacheManager() {
    return _instance;
  }

  ApiCacheManager._internal();

  /// Inicializa o cache manager, obtendo o ID de dispositivo
  Future<void> initialize() async {
    await _getDeviceId();
  }

  /// Obtém o ID do dispositivo para identificação nas requisições
  Future<String> _getDeviceId() async {
    if (_deviceId != null) return _deviceId!;

    final deviceInfo = DeviceInfoPlugin();
    if (Platform.isAndroid) {
      final androidInfo = await deviceInfo.androidInfo;
      _deviceId = androidInfo.id;
    } else if (Platform.isIOS) {
      final iosInfo = await deviceInfo.iosInfo;
      _deviceId = iosInfo.identifierForVendor;
    } else {
      _deviceId = DateTime.now().millisecondsSinceEpoch.toString();
    }

    return _deviceId!;
  }

  /// Verifica se pode fazer uma requisição baseado em rate limiting
  Future<bool> canMakeRequest(String endpoint,
      {int minIntervalMs = 2000}) async {
    final deviceId = await _getDeviceId();
    final key = '$deviceId:$endpoint';

    final now = DateTime.now();
    if (_lastRequestTimes.containsKey(key)) {
      final lastTime = _lastRequestTimes[key]!;
      final difference = now.difference(lastTime).inMilliseconds;

      if (difference < minIntervalMs) {
        return false;
      }
    }

    _lastRequestTimes[key] = now;
    return true;
  }

  /// Gera uma chave de cache baseada no endpoint e parâmetros
  String _generateCacheKey(String endpoint, Map<String, dynamic>? params) {
    String key = endpoint;
    if (params != null && params.isNotEmpty) {
      // Ordenar as chaves para garantir consistência
      final sortedParams = Map.fromEntries(
          params.entries.toList()..sort((a, b) => a.key.compareTo(b.key)));
      key += '_${json.encode(sortedParams)}';
    }
    return key;
  }

  /// Obtém dados do cache (memória ou persistente)
  Future<T?> getCachedData<T>(
    String endpoint,
    Map<String, dynamic>? params, {
    Duration? maxAge,
  }) async {
    final cacheKey = _generateCacheKey(endpoint, params);

    // Verificar cache em memória primeiro (mais rápido)
    if (_memoryCache.containsKey(cacheKey) &&
        _cacheTimestamps.containsKey(cacheKey)) {
      final timestamp = _cacheTimestamps[cacheKey]!;
      final age = DateTime.now().difference(timestamp);

      // Se o cache não expirou, retornar os dados em memória
      if (maxAge == null || age < maxAge) {
        return _memoryCache[cacheKey] as T?;
      }
    }

    // Verificar cache persistente se não estiver em memória ou expirou
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheData = prefs.getString(cacheKey);

      if (cacheData != null) {
        final cacheInfo = json.decode(cacheData);
        final timestamp = DateTime.parse(cacheInfo['timestamp']);
        final age = DateTime.now().difference(timestamp);

        // Se o cache persistente não expirou, atualizar memória e retornar
        if (maxAge == null || age < maxAge) {
          final data = cacheInfo['data'];
          _memoryCache[cacheKey] = data;
          _cacheTimestamps[cacheKey] = timestamp;
          return data as T?;
        }
      }
    } catch (e) {
      debugPrint('Erro ao acessar cache persistente: $e');
    }

    return null;
  }

  /// Salva dados no cache (memória e persistente)
  Future<void> setCacheData(
      String endpoint, Map<String, dynamic>? params, dynamic data) async {
    final cacheKey = _generateCacheKey(endpoint, params);
    final timestamp = DateTime.now();

    // Salvar em memória
    _memoryCache[cacheKey] = data;
    _cacheTimestamps[cacheKey] = timestamp;

    // Salvar em cache persistente
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheInfo = {
        'data': data,
        'timestamp': timestamp.toIso8601String(),
      };
      await prefs.setString(cacheKey, json.encode(cacheInfo));
    } catch (e) {
      debugPrint('Erro ao salvar cache persistente: $e');
    }
  }

  /// Limpa uma entrada específica do cache
  Future<void> invalidateCache(
      String endpoint, Map<String, dynamic>? params) async {
    final cacheKey = _generateCacheKey(endpoint, params);

    // Remover da memória
    _memoryCache.remove(cacheKey);
    _cacheTimestamps.remove(cacheKey);

    // Remover do cache persistente
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(cacheKey);
    } catch (e) {
      debugPrint('Erro ao remover cache persistente: $e');
    }
  }

  /// Limpa todo o cache
  Future<void> clearAllCache() async {
    // Limpar cache em memória
    _memoryCache.clear();
    _cacheTimestamps.clear();

    // Limpar cache persistente
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs
          .getKeys()
          .where((key) => key.contains('_')); // Filtrar apenas chaves de cache
      for (final key in keys) {
        await prefs.remove(key);
      }
    } catch (e) {
      debugPrint('Erro ao limpar cache persistente: $e');
    }
  }

  /// Executa uma função com dados em cache, atualizando o cache se necessário
  Future<T> executeWithCache<T>({
    required String endpoint,
    Map<String, dynamic>? params,
    required Future<T> Function() fetchFunction,
    Duration maxAge = const Duration(minutes: 5),
    bool forceRefresh = false,
  }) async {
    // Se não for forçar atualização, tentar obter do cache
    if (!forceRefresh) {
      final cachedData =
          await getCachedData<T>(endpoint, params, maxAge: maxAge);
      if (cachedData != null) {
        debugPrint('Usando dados em cache para $endpoint');
        return cachedData;
      }
    }

    // Se chegou aqui, precisa buscar dados frescos
    debugPrint('Buscando dados frescos para $endpoint');
    final result = await fetchFunction();

    // Salvar no cache
    await setCacheData(endpoint, params, result);

    return result;
  }

  /// Executa um Cloud Function no Back4App com suporte a cache e rate limiting
  Future<T> executeCloudFunction<T>({
    required String functionName,
    Map<String, dynamic>? params,
    Duration? maxAge,
    bool forceRefresh = false,
    int minIntervalMs = 2000,
  }) async {
    // Verificar rate limit
    if (!await canMakeRequest(functionName, minIntervalMs: minIntervalMs)) {
      throw Exception(
          'Muitas requisições em um curto período. Tente novamente em alguns segundos.');
    }

    // Adicionar deviceId aos parâmetros
    final deviceId = await _getDeviceId();
    final fullParams = {
      'clientId': deviceId,
      ...(params ?? {}),
    };

    // Executar com cache
    return executeWithCache<T>(
      endpoint: functionName,
      params: fullParams,
      fetchFunction: () async {
        final ParseCloudFunction function = ParseCloudFunction(functionName);
        final ParseResponse response =
            await function.execute(parameters: fullParams);

        if (response.success && response.result != null) {
          return response.result as T;
        } else {
          throw Exception(response.error?.message ?? 'Erro desconhecido');
        }
      },
      maxAge: maxAge ?? const Duration(minutes: 5),
      forceRefresh: forceRefresh,
    );
  }
}
