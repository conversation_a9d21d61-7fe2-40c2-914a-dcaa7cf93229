// lib/utils/live_query_manager.dart
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';

/// Estado da conexão LiveQuery
enum LiveQueryConnectionState {
  disconnected,
  connecting,
  connected,
  reconnecting,
  failed
}

/// Gerenciador central para conexões LiveQuery
/// Ajuda a evitar tentativas repetidas de conexão e monitorar o estado
class LiveQueryManager {
  static final LiveQueryManager _instance = LiveQueryManager._internal();
  factory LiveQueryManager() => _instance;
  LiveQueryManager._internal();

  // Estado atual da conexão
  final ValueNotifier<LiveQueryConnectionState> connectionState =
      ValueNotifier(LiveQueryConnectionState.disconnected);

  // Número de tentativas de reconexão
  int _reconnectAttempts = 0;
  static const int _maxReconnectAttempts = 5;

  // Timer para tentar reconexão com backoff exponencial
  Timer? _reconnectTimer;

  // LiveQuery client compartilhado
  LiveQuery? _liveQuery;
  LiveQuery? get liveQuery => _liveQuery;

  // Mensagem de erro atual
  String? _lastErrorMessage;
  String? get lastErrorMessage => _lastErrorMessage;

  // Método para inicializar o LiveQuery
  Future<LiveQuery?> initializeLiveQuery() async {
    if (_liveQuery != null) {
      return _liveQuery;
    }

    try {
      connectionState.value = LiveQueryConnectionState.connecting;
      debugPrint('LiveQueryManager: Inicializando LiveQuery');

      // Verificar se o usuário está autenticado
      final currentUser = await ParseUser.currentUser() as ParseUser?;
      if (currentUser == null) {
        _lastErrorMessage = 'Usuário não autenticado';
        connectionState.value = LiveQueryConnectionState.failed;
        return null;
      }

      // Verificar se o sessionToken está presente
      if (currentUser.sessionToken == null || currentUser.sessionToken!.isEmpty) {
        // Usar o método correto para obter o token da sessão
        await currentUser.getUpdatedUser();
      }

      _liveQuery = LiveQuery();
      connectionState.value = LiveQueryConnectionState.connected;
      
      // Não tentamos fazer uma subscrição de teste aqui para evitar erros na inicialização
      debugPrint('LiveQueryManager: Conexão LiveQuery inicializada');
      
      return _liveQuery;
    } catch (e) {
      _lastErrorMessage = e.toString();
      debugPrint('LiveQueryManager: Erro ao inicializar - $_lastErrorMessage');
      connectionState.value = LiveQueryConnectionState.failed;
      _scheduleReconnect();
      return null;
    }
  }

  // Programar reconexão com backoff exponencial
  void _scheduleReconnect() {
    // Cancelar timer anterior se existir
    _reconnectTimer?.cancel();
    
    // Se atingiu o número máximo de tentativas, parar
    if (_reconnectAttempts >= _maxReconnectAttempts) {
      debugPrint('LiveQueryManager: Máximo de tentativas atingido. Desistindo.');
      return;
    }
    
    // Calcular tempo de espera com backoff exponencial
    final waitTime = Duration(seconds: (1 << _reconnectAttempts) * 2);
    _reconnectAttempts++;
    
    debugPrint('LiveQueryManager: Tentativa $_reconnectAttempts em ${waitTime.inSeconds}s');
    connectionState.value = LiveQueryConnectionState.reconnecting;
    
    _reconnectTimer = Timer(waitTime, () async {
      debugPrint('LiveQueryManager: Tentando reconexão...');
      // Resetar liveQuery para forçar nova conexão
      _liveQuery = null;
      await initializeLiveQuery();
    });
  }

  // Inscrever-se em uma query
  Future<Subscription?> subscribe(QueryBuilder query) async {
    try {
      final lq = await initializeLiveQuery();
      if (lq == null) return null;

      debugPrint('LiveQueryManager: Inscrevendo-se na classe ${query.object.parseClassName}');
      
      // Usar a forma correta de inscrever-se: lq.client.subscribe retorna um Subscription
      final subscription = await lq.client.getClientEventStream.first.then((_) {
        return lq.client.subscribe(query);
      });
      
      return subscription;
    } catch (e) {
      _lastErrorMessage = e.toString();
      debugPrint('LiveQueryManager: Erro ao inscrever-se - _lastErrorMessage');
      return null;
    }
  }

  // Cancelar inscrição
  void unsubscribe(dynamic subscription) {
    if (subscription != null && _liveQuery != null) {
      try {
        // Na versão atual do parse_server_sdk_flutter, o subscription é um objeto Subscription
        // mas não possui um método direto para cancelar a inscrição
        // A melhor forma é fechar/limpar a referência
        debugPrint('LiveQueryManager: Tentando cancelar inscrição');
        
        // Definir como nulo para permitir que o garbage collector limpe
        subscription = null;
        debugPrint('LiveQueryManager: Inscrição cancelada');
      } catch (e) {
        debugPrint('LiveQueryManager: Erro ao cancelar inscrição - $e');
      }
    }
  }

  // Limpar recursos
  void dispose() {
    _reconnectTimer?.cancel();
    _liveQuery = null;
    connectionState.value = LiveQueryConnectionState.disconnected;
  }
}