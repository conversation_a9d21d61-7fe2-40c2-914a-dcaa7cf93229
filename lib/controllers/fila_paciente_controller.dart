import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fila_app/controllers/fila_state_controller.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:math' as math;
import 'package:flutter_dotenv/flutter_dotenv.dart';
import '../utils/whatsapp_utils.dart'; // Importar a classe utilitária
import '../models/mensagem_fila.dart'; // Importar o modelo de mensagem
import '../models/fila.dart'; // Importe o modelo Fila

// Sistema de exceções recomendado
class AppException implements Exception {
  final String message;
  final dynamic cause;

  AppException(this.message, [this.cause]);

  @override
  String toString() =>
      'AppException: $message${cause != null ? '\nCause: $cause' : ''}';
}

class NetworkException extends AppException {
  NetworkException(super.message, [super.cause]);
}

class LocationException extends AppException {
  LocationException(super.message, [super.cause]);
}

class FilaPacienteController extends GetxController {  final RxString filaId = ''.obs;
  final RxString medicoNome = ''.obs;
  final RxString especialidade = ''.obs;  
  final RxInt posicaoAtual = 0.obs;
  final RxInt posicaoAnterior = 0.obs; // Para rastrear a posição antes da atualização
  final RxBool posicaoMudou = false.obs; // Para sinalizar mudança e acionar animações
  final RxInt tempoEstimado = 0.obs;
  final RxInt tempoDistancia = 15.obs; // Valor padrão de 15 minutos
  final RxInt totalPacientesFila = 0.obs; // Número total de pacientes na fila
  final RxBool isLoading = true.obs;
  final RxBool isSaindo = false.obs;
  final Rx<LatLng?> hospitalLocation = Rx<LatLng?>(null);
  final RxString hospitalTelefone = ''.obs;
  final RxList<MensagemFila> mensagens = <MensagemFila>[].obs;

  // Novas propriedades para a tela de atendimento em andamento
  final RxString statusAtual = 'aguardando'.obs;  final RxString hospitalNome = ''.obs;
  final Rx<DateTime?> dataInicioAtendimento = Rx<DateTime?>(null);
  final RxString medicoCRM = 'CRM/SP 000000'.obs;
  final RxInt duracaoAtendimento = 0.obs;
  final RxInt tempoMedioAtendimento = 15.obs;

  Timer? refreshTimer;
  Timer? locationTimer;
  Timer? mensagensTimer;
  Timer? duracaoTimer; // Novo timer para controlar a duração do atendimento
  String _googleApiKey = '';
  Position? _currentPosition;

  FilaPacienteController({Map<String, dynamic>? initialFilaState}) {
    if (initialFilaState != null) {
      filaId.value = initialFilaState['filaId'] ?? '';
      medicoNome.value = initialFilaState['medicoNome'] ?? '';
      especialidade.value = initialFilaState['especialidade'] ?? '';
      posicaoAtual.value = initialFilaState['posicao'] ?? 0;
      statusAtual.value = initialFilaState['status'] ?? 'aguardando';
    }
    _googleApiKey = dotenv.env['GOOGLE_MAPS_API_KEY'] ?? '';
  }

  get idPaciente => null;

  @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null && Get.arguments is Map<String, dynamic>) {
      _inicializarComArgumentos(Get.arguments);
    }
    inicializarDados();
  }

  @override
  void onClose() {
    try {
      if (refreshTimer != null && refreshTimer!.isActive) {
        refreshTimer!.cancel();
      }
      if (locationTimer != null && locationTimer!.isActive) {
        locationTimer!.cancel();
      }
      if (mensagensTimer != null && mensagensTimer!.isActive) {
        mensagensTimer!.cancel();
      }
      if (duracaoTimer != null && duracaoTimer!.isActive) {
        duracaoTimer!.cancel();
      }
    } catch (e) {
      debugPrint('Erro ao cancelar timers: $e');
    } finally {
      super.onClose();
    }
  }

  void _inicializarComArgumentos(Map<String, dynamic> args) {
    filaId.value = args['filaId'] ?? '';
    medicoNome.value = args['medicoNome'] ?? '';
    especialidade.value = args['especialidade'] ?? '';
    posicaoAtual.value = args['posicao'] ?? 0;
    statusAtual.value = args['status'] ?? 'aguardando';
  }
  Future<void> inicializarDados() async {
    isLoading.value = true;
    try {
      await _buscarDadosHospital();
      await FilaStateController.salvarEstadoFila(
        filaId: filaId.value,
        medicoNome: medicoNome.value,
        especialidade: especialidade.value,
        posicao: posicaoAtual.value,
      );
      _iniciarAtualizacaoAutomatica();

      // Obter o total de pacientes na fila
      await _obterTotalPacientesFila();

      // Adicionar esta linha
      await _setupLocationUpdates();

      // Calcular tempo de distância logo após obter localização do hospital
      if (hospitalLocation.value != null) {
        await _calcularTempoDistancia();
      }

      // Buscar mensagens
      await _buscarMensagens();
      _iniciarAtualizacaoMensagens();

      // Se já estiver em atendimento, iniciar contagem de duração
      if (statusAtual.value == 'em_atendimento') {
        _iniciarContadorDuracao();
        await _obterTempoMedioAtendimento();
      }
    } on AppException catch (e) {
      debugPrint('Erro ao inicializar dados: ${e.message}');
    } catch (e) {
      debugPrint('Erro desconhecido ao inicializar dados: $e');
    } finally {      isLoading.value = false;
    }
  }
  void _iniciarAtualizacaoAutomatica() {
    refreshTimer?.cancel();
    
    // Aumentar o intervalo para 60 segundos para reduzir requisições desnecessárias
    // mas ainda mantendo atualizações em tempo hábil quando houver mudanças na fila
    refreshTimer = Timer.periodic(const Duration(seconds: 60), (_) {
      if (!isSaindo.value) {
        _verificarStatusFila();
      }
    });
    
    // Fazer uma verificação inicial
    _verificarStatusFila();
  }

  Future<void> _setupLocationUpdates() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        throw LocationException('Serviços de localização desativados');
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw LocationException('Permissão de localização negada');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw LocationException(
            'Permissão de localização negada permanentemente');
      }

      _currentPosition = await Geolocator.getCurrentPosition();
      debugPrint(
          'Posição inicial obtida: ${_currentPosition!.latitude}, ${_currentPosition!.longitude}');

      locationTimer = Timer.periodic(const Duration(minutes: 2), (_) async {
        Position newPosition = await Geolocator.getCurrentPosition();
        debugPrint(
            'Nova posição obtida: ${newPosition.latitude}, ${newPosition.longitude}');

        if (_currentPosition == null ||
            (newPosition.latitude != _currentPosition!.latitude ||
                newPosition.longitude != _currentPosition!.longitude)) {
          _currentPosition = newPosition;
          if (hospitalLocation.value != null) {
            await _calcularTempoDistancia();
            // Adicionar cálculo do tempo médio também
            await _obterTempoMedioAtendimento();
          }
        }
      });
    } on LocationException catch (e) {
      debugPrint(
          'Erro ao configurar atualizações de localização: ${e.message}');
    } catch (e) {
      debugPrint(
          'Erro desconhecido ao configurar atualizações de localização: $e');
    }
  }

  Future<void> _buscarDadosHospital() async {
    try {
      final queryFila = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('objectId', filaId.value)
        ..includeObject(['consultorio', 'medico']);

      final response = await queryFila.query();

      if (response.success &&
          response.results != null &&
          response.results!.isNotEmpty) {
        final consultorio =
            response.results!.first.get<ParseObject>('consultorio');
        if (consultorio != null) {
          final latitude = consultorio.get<num>('latitude')?.toDouble();
          final longitude = consultorio.get<num>('longitude')?.toDouble();

          if (latitude != null && longitude != null) {
            hospitalLocation.value = LatLng(latitude, longitude);
            // Calcula o tempo de distância assim que obtiver a localização
            await _calcularTempoDistancia();
          }

          hospitalTelefone.value = consultorio.get<String>('telefone') ?? '';
          hospitalNome.value = consultorio.get<String>('nome') ?? 'Hospital';
        }

        // Buscar dados do médico
        final medico = response.results!.first.get<ParseObject>('medico');
        if (medico != null) {
          medicoCRM.value = medico.get<String>('crm') ?? 'CRM não informado';
          // Se o nome do médico não foi definido ainda
          if (medicoNome.value.isEmpty) {
            medicoNome.value = medico.get<String>('nome') ?? 'Médico';
          }
          // Se a especialidade não foi definida ainda
          if (especialidade.value.isEmpty) {
            especialidade.value =
                medico.get<String>('especialidade') ?? 'Especialista';
          }
        }

        // Verificar se já está em atendimento e salvar a data de início
        final status =
            response.results!.first.get<String>('status') ?? 'aguardando';
        statusAtual.value = status;

        if (status == 'em_atendimento') {
          dataInicioAtendimento.value =
              response.results!.first.get<DateTime>('data_inicio_atendimento');
        }
      }
    } on AppException catch (e) {
      debugPrint('Erro ao buscar dados do hospital: ${e.message}');
    } catch (e) {
      debugPrint('Erro desconhecido ao buscar dados do hospital: $e');
    }
  }

  Future<void> _calcularTempoDistancia() async {
    if (hospitalLocation.value == null) {
      tempoDistancia.value = 15; // Valor padrão se não houver localização
      return;
    }

    try {
      // Verificar permissão de localização
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          tempoDistancia.value = 15;
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        tempoDistancia.value = 15;
        return;
      }

      // Obter posição atual
      final position = await Geolocator.getCurrentPosition();
      final origem = LatLng(position.latitude, position.longitude);
      final destino = hospitalLocation.value!;

      if (_googleApiKey.isNotEmpty) {
        try {
          final url =
              Uri.parse('https://maps.googleapis.com/maps/api/directions/json?'
                  'origin=${origem.latitude},${origem.longitude}'
                  '&destination=${destino.latitude},${destino.longitude}'
                  '&mode=driving'
                  '&key=$_googleApiKey');

          final response = await http.get(url);
          if (response.statusCode == 200) {
            final data = json.decode(response.body);
            if (data['status'] == 'OK' && data['routes'].isNotEmpty) {
              final route = data['routes'][0]['legs'][0];
              final durationInMinutes =
                  (route['duration']['value'] / 60).round();
              tempoDistancia.value = durationInMinutes;
              return;
            }
          }
        } on NetworkException catch (e) {
          debugPrint('Erro na API do Google Maps: ${e.message}');
        } catch (e) {
          debugPrint('Erro desconhecido na API do Google Maps: $e');
        }
      }

      // Cálculo alternativo se a API falhar ou não estiver disponível
      final distanceInMeters = Geolocator.distanceBetween(
        origem.latitude,
        origem.longitude,
        destino.latitude,
        destino.longitude,
      );

      // Estimativa usando velocidade média de 30 km/h
      final tempoEstimado = ((distanceInMeters / 1000) / 30 * 60).round();
      // Garantir um tempo mínimo razoável
      tempoDistancia.value = tempoEstimado < 5
          ? 5
          : tempoEstimado > 120
              ? 120
              : tempoEstimado.toInt(); // Ensure it's an int
    } catch (e) {
      debugPrint('Erro ao calcular tempo de distância: $e');
      tempoDistancia.value = 15; // Valor padrão em caso de erro
    }
  }

  Future<void> _obterTempoMedioAtendimento() async {
    try {
      final queryMetricas =
          QueryBuilder<ParseObject>(ParseObject('MetricasAtendimento'))
            ..whereEqualTo(
                'medico',
                ParseObject('Medico')
                  ..objectId = filaId.value.split('_').first);

      final response = await queryMetricas.query();

      if (response.success &&
          response.results != null &&
          response.results!.isNotEmpty) {
        final metricas = response.results!.first;
        final tempoMedio =
            metricas.get<num>('tempo_medio_atendimento')?.toInt() ?? 15;
        tempoMedioAtendimento.value = tempoMedio;
      }
    } catch (e) {
      debugPrint('Erro ao obter tempo médio de atendimento: $e');
    }
  }
  Future<void> _verificarStatusFila() async {
    try {
      if (filaId.isEmpty) return;

      // Consultar status atual da fila
      final queryFila = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('objectId', filaId.value)
        ..includeObject(['medico']);

      final response = await queryFila.query();

      if (response.success &&
          response.results != null &&
          response.results!.isNotEmpty) {
        final fila = response.results!.first;
        final status = fila.get<String>('status') ?? 'aguardando';

        // Atualizar o status
        final statusAnterior = statusAtual.value;
        statusAtual.value = status;

        // Se não está mais na fila, redirecionar
        if (status != 'aguardando' && status != 'em_atendimento') {
          if (isSaindo.isTrue) return; // Evitar duplas navegações

          isSaindo.value = true;
          // Usar Get.until em vez de Get.offAllNamed para preservar a sessão
          Get.until((route) => route.settings.name == '/home' || route.settings.name == '/');
          return;
        }

        // Atualizar posição na fila
        final novaPosicao = fila.get<int>('posicao') ?? 0;
        
        // Guardar a posição anterior antes de atualizar
        posicaoAnterior.value = posicaoAtual.value;
        
        if (novaPosicao != posicaoAtual.value) {
          // Ativar o sinalizador que indica mudança de posição para disparar animação
          posicaoMudou.value = true;
          
          // Atualizar a posição atual
          posicaoAtual.value = novaPosicao;
          debugPrint('Posição atualizada: $novaPosicao (anterior: ${posicaoAnterior.value})');

          // Recalcular tempo estimado com a nova posição
          await _calcularTempoEstimado(fila);
          
          // Programar a desativação da animação após 3 segundos
          Future.delayed(const Duration(seconds: 3), () {
            posicaoMudou.value = false;
          });
        }

        // Obter o total de pacientes na fila do mesmo médico
        await _obterTotalPacientesFila();

        // Verificar notificações/mensagens se houver mudança de status
        if (status == 'em_atendimento' && statusAnterior != 'em_atendimento') {
          dataInicioAtendimento.value =
              fila.get<DateTime>('data_inicio_atendimento') ?? DateTime.now();
          _iniciarContadorDuracao();
          await _obterTempoMedioAtendimento();

          // Adicionar uma mensagem local de notificação
          final mensagemInicio = MensagemFila(
            titulo: 'Atendimento Iniciado',
            texto: 'Seu atendimento com Dr. ${medicoNome.value} foi iniciado!',
            dataEnvio: DateTime.now(),
            prioridade: 'alta',
            icone: 'notification',
          );

          if (mensagens.isEmpty ||
              mensagens.first.titulo != mensagemInicio.titulo) {
            mensagens.insert(0, mensagemInicio);
          }
        }
      }
    } catch (e) {
      debugPrint('Erro ao verificar status da fila: $e');
    }
  }

  Future<void> abrirWhatsApp() async {
    if (hospitalTelefone.isEmpty) {
      Get.snackbar(
        'Erro',
        'Telefone do hospital não disponível',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    try {
      final sucesso = await WhatsAppUtils.abrirWhatsApp(hospitalTelefone.value);
      if (!sucesso) {
        _mostrarErroWhatsApp();
      }
    } on AppException catch (e) {
      debugPrint('Erro ao abrir WhatsApp: ${e.message}');
      _mostrarErroWhatsApp();
    } catch (e) {
      debugPrint('Erro desconhecido ao abrir WhatsApp: $e');
      _mostrarErroWhatsApp();
    }
  }

  void _mostrarErroWhatsApp() {
    Get.snackbar(
      'Erro',
      'Não foi possível abrir o WhatsApp',
      backgroundColor: Colors.red,
      colorText: Colors.white,
    );
  }

  void abrirLocalizacao() {
    if (hospitalLocation.value == null) {
      Get.snackbar(
        'Erro',
        'Localização do hospital não disponível',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    Get.toNamed(
      '/localizacao',
      arguments: {
        'latitude': hospitalLocation.value!.latitude,
        'longitude': hospitalLocation.value!.longitude,
      },
    );
  }

  Future<void> confirmarSaidaFila() async {
    if (isSaindo.value) return;

    isSaindo.value = true;
    refreshTimer?.cancel();
    locationTimer?.cancel();

    try {
      await FilaStateController.limparEstadoFila();

      // Usar Get.toNamed em vez de Get.offAllNamed para preservar a sessão
      Get.toNamed(
        '/confirmacao_sair_fila',
        arguments: {
          'filaId': filaId.value,
          'nomeMedico': medicoNome.value,
          'especialidade': especialidade.value,
        },
      );
    } on AppException catch (e) {
      debugPrint('Erro ao sair da fila: ${e.message}');
      isSaindo.value = false;
      _iniciarAtualizacaoAutomatica();

      Get.snackbar(
        'Erro',
        'Erro ao sair da fila. Tente novamente.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } catch (e) {
      debugPrint('Erro desconhecido ao sair da fila: $e');
      isSaindo.value = false;
      _iniciarAtualizacaoAutomatica();

      Get.snackbar(
        'Erro',
        'Erro ao sair da fila. Tente novamente.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  void atualizarLocalizacao() async {
    if (_currentPosition == null) {
      try {
        _currentPosition = await Geolocator.getCurrentPosition();
        if (hospitalLocation.value != null) {
          await _calcularTempoDistancia();
        }
      } on LocationException catch (e) {
        debugPrint('Erro ao atualizar localização: ${e.message}');
      } catch (e) {
        debugPrint('Erro desconhecido ao atualizar localização: $e');
      }
    }
  }

  Future<void> atualizarTempos() async {
    try {
      await _calcularTempoDistancia();
      await _obterTempoMedioAtendimento();
    } on AppException catch (e) {
      debugPrint('Erro ao atualizar tempos: ${e.message}');
    } catch (e) {
      debugPrint('Erro desconhecido ao atualizar tempos: $e');
    }
  }

  Future<bool> verificarPermissoesLocalizacao() async {
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      Get.snackbar(
        'Erro',
        'Serviços de localização estão desativados',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        Get.snackbar(
          'Erro',
          'Permissões de localização negadas',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return false;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      Get.snackbar(
        'Erro',
        'Permissões de localização permanentemente negadas. Por favor, habilite nas configurações.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }

    return true;
  }

  Future<void> atualizarTemposDistancia() async {
    try {
      await _calcularTempoDistancia(); // Calculando o tempo de distância em tempo real
      await _obterTempoMedioAtendimento();
    } on AppException catch (e) {
      debugPrint('Erro ao atualizar tempos: ${e.message}');
    } catch (e) {
      debugPrint('Erro desconhecido ao atualizar tempos: $e');
    }
  }

  Future<void> registrarEntrada() async {
    try {
      final fila = ParseObject('Fila')
        ..objectId = filaId.value
        ..set('data_entrada', DateTime.now());
      await fila.save();
    } on AppException catch (e) {
      debugPrint('Erro ao registrar entrada: ${e.message}');
    } catch (e) {
      debugPrint('Erro desconhecido ao registrar entrada: $e');
    }
  }

  Future<void> registrarInicioAtendimento() async {
    try {
      final fila = ParseObject('Fila')
        ..objectId = filaId.value
        ..set('data_inicio_atendimento', DateTime.now());
      await fila.save();

      debugPrint('Início do atendimento registrado com sucesso.');
    } on AppException catch (e) {
      debugPrint('Erro ao registrar início do atendimento: ${e.message}');
    } catch (e) {
      debugPrint('Erro desconhecido ao registrar início do atendimento: $e');
    }
  }

  Future<void> registrarFimAtendimento() async {
    try {
      final dataFim = DateTime.now();
      final queryFila = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('objectId', filaId.value);

      final response = await queryFila.query();

      if (response.success &&
          response.results != null &&
          response.results!.isNotEmpty) {
        final fila = response.results!.first;
        final dataInicio = fila.get<DateTime>('data_inicio_atendimento');

        if (dataInicio != null) {
          final duracao = dataFim.difference(dataInicio).inMinutes;

          // Atualizando os dados na instância de Fila
          fila
            ..set('data_fim_atendimento', dataFim)
            ..set('tempo_atendimento', duracao);

          await fila.save();

          debugPrint(
              'Fim do atendimento registrado com sucesso. Tempo de atendimento: $duracao minutos');
        } else {
          debugPrint('Erro: data de início do atendimento não encontrada.');
        }
      } else {
        debugPrint('Erro: Fila não encontrada.');
      }
    } on AppException catch (e) {
      debugPrint('Erro ao registrar fim do atendimento: ${e.message}');
    } catch (e) {
      debugPrint('Erro desconhecido ao registrar fim do atendimento: $e');
    }
  }

  Future<void> _buscarMensagens() async {
    try {
      final usuarioAtual = await ParseUser.currentUser() as ParseUser?;
      if (usuarioAtual == null) {
        return;
      }

      final query = QueryBuilder<ParseObject>(ParseObject('Mensagem'))
        ..whereEqualTo('paciente', usuarioAtual)
        ..includeObject(['medico'])
        ..orderByDescending('createdAt');

      final response = await query.query();

      if (response.results != null && response.results!.isNotEmpty) {
        final List<MensagemFila> novasMensagens = response.results!.map((m) {
          // Processar a mensagem diretamente sem verificação null desnecessária
          return MensagemFila.fromParse(m as ParseObject);
        }).toList();

        if (novasMensagens.isNotEmpty) {
          // Adicionar as novas mensagens ao início da lista existente
          final List<MensagemFila> listaAtualizada = [
            ...novasMensagens,
            ...mensagens
          ];

          // Limitar a 30 mensagens para economizar memória
          if (listaAtualizada.length > 30) {
            mensagens.value = listaAtualizada.sublist(0, 30);
          } else {
            mensagens.value = listaAtualizada;
          }
        }
      }
    } catch (e) {
      debugPrint('Erro ao buscar mensagens: $e');
    }
  }

  void _iniciarAtualizacaoMensagens() {
    mensagensTimer?.cancel();
    // Aumentar o intervalo para 45 segundos para reduzir chamadas ao servidor
    mensagensTimer = Timer.periodic(const Duration(seconds: 45), (_) {
      if (!isSaindo.value) {
        _buscarMensagens();
      }
    });
  }

  Future<void> _calcularTempoEstimado(ParseObject fila) async {
    try {
      final medico = fila.get<ParseObject>('medico');
      if (medico == null) return;

      // Obter métricas de atendimento para este médico
      final queryMetricas =
          QueryBuilder<ParseObject>(ParseObject('MetricasAtendimento'))
            ..whereEqualTo('medico', medico)
            ..orderByDescending('createdAt')
            ..setLimit(1);

      final metricasResponse = await queryMetricas.query();

      int tempoMedioPorPaciente = 15; // valor padrão em minutos

      if (metricasResponse.success &&
          metricasResponse.results != null &&
          metricasResponse.results!.isNotEmpty) {
        final metricas = metricasResponse.results!.first;
        final tempoMedioAtendimento =
            metricas.get<num>('tempo_medio_atendimento')?.toInt() ?? 15;
        final tempoMedioEspera =
            metricas.get<num>('tempo_medio_espera')?.toInt() ?? 5;

        // Tempo médio total por paciente
        tempoMedioPorPaciente = tempoMedioAtendimento + tempoMedioEspera;
      }

      // Calcular tempo estimado com base na posição atual e no tempo médio por paciente
      // Subtrai 1 da posição porque o paciente na posição 1 será o próximo a ser atendido
      final tempoEstimadoMinutos =
          (posicaoAtual.value - 1) * tempoMedioPorPaciente;

      // Garantir que não seja negativo
      tempoEstimado.value = tempoEstimadoMinutos > 0 ? tempoEstimadoMinutos : 0;
    } catch (e) {
      debugPrint('Erro ao calcular tempo estimado: $e');
      // Em caso de erro, usar um valor padrão baseado na posição
      tempoEstimado.value = (posicaoAtual.value - 1) * 15;
      if (tempoEstimado.value < 0) tempoEstimado.value = 0;
    }
  }  // Método para obter o número total de pacientes na fila do mesmo médico
  Future<void> _obterTotalPacientesFila() async {
    try {
      if (filaId.isEmpty) return;
      
      // Extrair o ID do médico da filaId (formato padrão: medico_id_consultorio_id)
      List<String> partes = filaId.value.split('_');
      if (partes.length < 2) {
        debugPrint('Formato de filaId inválido para consulta de total de pacientes');
        return;
      }
      
      String medicoId = partes[0];
      
      // Consultar todas as filas ativas deste médico
      final queryFilas = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('medico', ParseObject('Medico')..objectId = medicoId)
        ..whereContainedIn('status', ['aguardando', 'em_atendimento']);
      
      final response = await queryFilas.query();
      
      if (response.success && response.results != null) {
        totalPacientesFila.value = response.results!.length;
        debugPrint('Total de pacientes na fila: ${totalPacientesFila.value}');
      } else {
        // Se houver erro, definir pelo menos 1 (o próprio paciente)
        totalPacientesFila.value = 1;
      }
    } catch (e) {
      debugPrint('Erro ao obter total de pacientes na fila: $e');
      // Em caso de erro, garantir que pelo menos o próprio paciente seja contado
      totalPacientesFila.value = math.max(1, posicaoAtual.value);
    }
  }
  Future<void> atualizarDados() async {
    try {
      await _verificarStatusFila();
      await _buscarMensagens();
      await _calcularTempoDistancia();
      await _obterTotalPacientesFila(); // Atualizar o total de pacientes
      
      // Adicionar feedback visual sobre a atualização
      Get.snackbar(
        'Atualizado',
        'Dados da fila atualizados com sucesso',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.teal.withOpacity(0.7),
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
        margin: const EdgeInsets.only(bottom: 20, left: 20, right: 20),
        borderRadius: 10,
        icon: const Icon(Icons.refresh, color: Colors.white),
      );
    } catch (e) {
      debugPrint('Erro ao atualizar dados: $e');
      Get.snackbar(
        'Erro',
        'Não foi possível atualizar os dados da fila',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.7),
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );
    }
  }

  Future<void> _buscarMetricas() async {
    try {
      // Obtém a fila atual para acessar as referências do médico e consultório
      final queryFila = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('objectId', filaId.value)
        ..includeObject(['medico', 'consultorio']);

      final filaResponse = await queryFila.query();

      if (!filaResponse.success ||
          filaResponse.results == null ||
          filaResponse.results!.isEmpty) {
        return;
      }

      final filaParse = filaResponse.results!.first;
      final medico = filaParse.get<ParseObject>('medico');
      final consultorio = filaParse.get<ParseObject>('consultorio');

      if (medico == null || consultorio == null) {
        return;
      }

      // Consulta as métricas de atendimento para este médico e consultório
      final queryMetricas =
          QueryBuilder<ParseObject>(ParseObject('MetricasAtendimento'))
            ..whereEqualTo('medico_id', medico)
            ..whereEqualTo('consultorio_id', consultorio)
            ..orderByDescending('data')
            ..setLimit(1);

      final metricasResponse = await queryMetricas.query();

      if (metricasResponse.success &&
          metricasResponse.results != null &&
          metricasResponse.results!.isNotEmpty) {
        final metricas = metricasResponse.results!.first;
        final tempoMedioEspera =
            metricas.get<num>('tempo_medio_espera')?.toInt() ?? 0;
        final tempoMedioAtendimento =
            metricas.get<num>('tempo_medio_atendimento')?.toInt() ?? 0;

        // Atualiza o tempo estimado considerando a posição atual e os tempos médios
        final calculatedTime = (posicaoAtual.value - 1) *
            (tempoMedioAtendimento > 0 ? tempoMedioAtendimento : 15);

        tempoEstimado.value = calculatedTime.toInt();

        debugPrint(
            'Métricas atualizadas: Tempo médio de espera: $tempoMedioEspera min, '
            'Tempo médio de atendimento: $tempoMedioAtendimento min');
      }
    } catch (e) {
      debugPrint('Erro ao buscar métricas: $e');
    }
  }

  Future<bool> atualizarStatusFila(String status) async {
    if (filaId.isEmpty || isSaindo.value) return false;

    try {
      isSaindo.value = true;

      // Buscar a fila para obter o ID do paciente
      final queryFila = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('objectId', filaId.value)
        ..whereEqualTo('status', 'aguardando');

      final response = await queryFila.query();

      if (!response.success ||
          response.results == null ||
          response.results!.isEmpty) {
        throw Exception('Fila não encontrada ou já finalizada');
      }

      final fila = response.results!.first;
      final idPaciente = fila.get<String>('idPaciente');

      if (idPaciente == null) {
        throw Exception('ID do paciente não encontrado na fila');
      }

      // Usar o método Fila.updateStatus para atualizar o status
      final success = await Fila.updateStatus(
        filaId: filaId.value,
        status: status,
        idPaciente: idPaciente,
      );

      if (!success) {
        throw Exception('Não foi possível atualizar a fila');
      }

      return true;
    } catch (e) {
      debugPrint('Erro ao atualizar status: $e');
      return false;
    } finally {
      isSaindo.value = false;
    }
  }

  // Método para iniciar o contador de duração do atendimento
  void _iniciarContadorDuracao() {
    duracaoTimer?.cancel();

    // Definir duração inicial com base na data de início
    if (dataInicioAtendimento.value != null) {
      final inicioAtendimento = dataInicioAtendimento.value!;
      final agora = DateTime.now();
      final diferencaMinutos = agora.difference(inicioAtendimento).inMinutes;
      duracaoAtendimento.value = diferencaMinutos;
    } else {
      duracaoAtendimento.value = 0;
      dataInicioAtendimento.value = DateTime.now();
    }

    // Atualizar a cada minuto
    duracaoTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      if (statusAtual.value != 'em_atendimento') {
        timer.cancel();
        return;
      }

      if (dataInicioAtendimento.value != null) {
        final inicioAtendimento = dataInicioAtendimento.value!;
        final agora = DateTime.now();
        final diferencaMinutos = agora.difference(inicioAtendimento).inMinutes;
        duracaoAtendimento.value = diferencaMinutos;
      } else {
        duracaoAtendimento.value++;
      }
    });
  }

  // Método para registrar emergências
  Future<bool> registrarEmergencia(String tipo) async {
    try {
      if (filaId.isEmpty) return false;

      isLoading.value = true;

      // Criar registro de emergência
      final emergencia = ParseObject('Emergencia')
        ..set('fila', ParseObject('Fila')..objectId = filaId.value)
        ..set('tipo', tipo)
        ..set('detalhes', 'Solicitação feita pelo paciente durante atendimento')
        ..set('data_registro', DateTime.now())
        ..set('status', 'pendente');

      final result = await emergencia.save();

      if (result.success) {
        // Notificar via Cloud Function (opcional)
        try {
          final params = {
            'filaId': filaId.value,
            'tipo': tipo,
            'hospital': hospitalNome.value,
            'medico': medicoNome.value,
          };

          await ParseCloudFunction('notificarEmergencia')
              .execute(parameters: params);
        } catch (cloudError) {
          debugPrint(
              'Aviso: Erro ao enviar notificação de emergência: $cloudError');
        }

        return true;
      } else {
        return false;
      }
    } catch (e) {
      debugPrint('Erro ao registrar emergência: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }
}
