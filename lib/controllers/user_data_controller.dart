// lib/controllers/user_data_controller.dart
import 'dart:convert';
import 'dart:math';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'package:uuid/uuid.dart';
import 'package:device_info_plus/device_info_plus.dart';

class UserDataController extends GetxController {
  final nomeController = TextEditingController();
  final telefoneController = TextEditingController();
  final RxBool isLoading = false.obs;
  final RxBool userDataExists = false.obs;
  final RxString formattedUserId = ''.obs;

  static const String _userDataKey = 'userData';
  static const String _userSessionKey = 'userSession';

  @override
  void onInit() {
    super.onInit();
    checkUserData();
  }

  @override
  void onClose() {
    nomeController.dispose();
    telefoneController.dispose();
    super.onClose();
  }

  Future<void> checkUserData() async {
    try {
      isLoading.value = true;
      final userData = await getUserData();
      if (userData != null) {
        nomeController.text = userData['nome'] ?? '';
        telefoneController.text = userData['telefone'] ?? '';
        formattedUserId.value = userData['userId'] ?? '';
        userDataExists.value = true;
        
        // Atualizar o horário do último acesso
        _updateLastAccess();
      } else {
        userDataExists.value = false;
      }
    } catch (e) {
      debugPrint('Erro ao verificar dados do usuário: $e');
      userDataExists.value = false;
    } finally {
      isLoading.value = false;
    }
  }

  // Método para atualizar o horário do último acesso
  Future<void> _updateLastAccess() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userData = prefs.getString(_userDataKey);
      
      if (userData != null) {
        final Map<String, dynamic> userDataMap = json.decode(userData);
        // Atualizar apenas o último acesso
        userDataMap['ultimoAcesso'] = DateTime.now().toIso8601String();
        
        // Salvar novamente
        await prefs.setString(_userDataKey, json.encode(userDataMap));
        
        // Atualizar no servidor também
        await _updateLastAccessInCloud(userDataMap);
      }
    } catch (e) {
      debugPrint('Erro ao atualizar último acesso: $e');
    }
  }

  // Método para atualizar o último acesso no servidor
  Future<void> _updateLastAccessInCloud(Map<String, dynamic> userData) async {
    try {
      final userId = userData['userId'];
      if (userId == null) return;

      // Verificar se existe um paciente com esse ID
      final queryPaciente = QueryBuilder<ParseObject>(ParseObject('Paciente'))
        ..whereEqualTo('userId', userId);

      final pacienteResponse = await queryPaciente.query();

      if (pacienteResponse.success &&
          pacienteResponse.results != null &&
          pacienteResponse.results!.isNotEmpty) {
        // Atualizar paciente existente
        final paciente = pacienteResponse.results!.first;
        paciente.set('ultimoAcesso', DateTime.parse(userData['ultimoAcesso']));
        await paciente.save();
      }
    } catch (e) {
      // Apenas registrar o erro, mas não falhar
      debugPrint('Erro ao atualizar último acesso no servidor: $e');
    }
  }

  Future<bool> saveUserData(String nome, String telefone,
      {bool isEditing = false}) async {
    try {
      isLoading.value = true;

      if (nome.isEmpty || telefone.isEmpty) {
        return false;
      }

      final prefs = await SharedPreferences.getInstance();

      String? userId;

      // Se estiver editando ou se for a primeira vez, verificar se já existe um ID
      final existingData = await getUserData();
      if (existingData != null && existingData.containsKey('userId')) {
        userId = existingData['userId'];
      } else {
        // Gerar um novo userId
        userId = await _generateUserId();
      }

      // Verificar se já existe um usuário com esse telefone
      if (!isEditing) {
        final queryTelefone = QueryBuilder<ParseObject>(ParseObject('Paciente'))
          ..whereEqualTo(
              'telefone', telefone.replaceAll(RegExp(r'[^0-9]'), ''));

        final telefoneResponse = await queryTelefone.query();

        if (telefoneResponse.success &&
            telefoneResponse.results != null &&
            telefoneResponse.results!.isNotEmpty) {
          // Se encontrar, usar o userId existente
          final pacienteExistente = telefoneResponse.results!.first;
          userId = pacienteExistente.get<String>('userId') ?? userId;
        }
      }

      // Dados completos do usuário
      final userData = {
        'nome': nome,
        'telefone': telefone,
        'userId': userId,
        'dataCadastro': isEditing
            ? (existingData != null && existingData.containsKey('dataCadastro')
                ? existingData['dataCadastro']
                : DateTime.now().toIso8601String())
            : DateTime.now().toIso8601String(),
        'ultimoAcesso': DateTime.now().toIso8601String(),
      };

      // Salvar no dispositivo
      await prefs.setString(_userDataKey, json.encode(userData));
      
      // Salvar também um flag de sessão ativa para maior persistência
      await prefs.setString(_userSessionKey, "active");

      // Atualizar na nuvem também (se já existir um userId)
      await _syncUserDataToCloud(userData, isEditing);

      // Atualizar estado local
      nomeController.text = nome;
      telefoneController.text = telefone;
      formattedUserId.value = userId ?? '';
      userDataExists.value = true;

      return true;
    } catch (e) {
      debugPrint('Erro ao salvar dados: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Sincronizar dados do usuário com o Parse Server
  Future<void> _syncUserDataToCloud(
      Map<String, dynamic> userData, bool isEditing) async {
    try {
      final userId = userData['userId'];

      // Verificar se existe um paciente com esse ID
      final queryPaciente = QueryBuilder<ParseObject>(ParseObject('Paciente'))
        ..whereEqualTo('userId', userId);

      final pacienteResponse = await queryPaciente.query();

      late ParseObject paciente;

      final bool hasExistingPatient = pacienteResponse.success &&
          pacienteResponse.results != null &&
          pacienteResponse.results!.isNotEmpty;

      if (hasExistingPatient) {
        // Atualizar paciente existente
        paciente = pacienteResponse.results!.first;
      } else {
        // Criar novo paciente
        paciente = ParseObject('Paciente');
        paciente.set('userId', userId);
        paciente.set('dataCadastro', DateTime.parse(userData['dataCadastro']));
      }

      // Atualizar dados
      paciente.set('nome', userData['nome']);
      paciente.set(
          'telefone', userData['telefone'].replaceAll(RegExp(r'[^0-9]'), ''));
      paciente.set('ultimoAcesso', DateTime.parse(userData['ultimoAcesso']));
      
      // Definir ACL pública para facilitar acesso
      final acl = ParseACL();
      acl.setPublicReadAccess(allowed: true);
      acl.setPublicWriteAccess(allowed: true);
      paciente.setACL(acl);

      // Tentar obter informações do dispositivo
      try {
        final deviceInfo = await _getDeviceInfo();
        paciente.set('deviceInfo', deviceInfo);
      } catch (e) {
        // Ignorar erros de informações do dispositivo
        debugPrint('Erro ao obter informações do dispositivo: $e');
      }

      await paciente.save();
    } catch (e) {
      // Apenas registrar o erro, mas não falhar
      debugPrint('Erro ao sincronizar dados com o servidor: $e');
    }
  }

  // Obter informações sobre o dispositivo
  Future<Map<String, dynamic>> _getDeviceInfo() async {
    final deviceInfo = DeviceInfoPlugin();

    try {
      if (GetPlatform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        return {
          'platform': 'Android',
          'model': androidInfo.model,
          'manufacturer': androidInfo.manufacturer,
          'version': androidInfo.version.release,
        };
      } else if (GetPlatform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        return {
          'platform': 'iOS',
          'model': iosInfo.model,
          'name': iosInfo.name,
          'systemVersion': iosInfo.systemVersion,
        };
      } else {
        return {
          'platform': 'Web/Desktop',
        };
      }
    } catch (e) {
      return {
        'platform': 'Unknown',
        'error': 'Não foi possível obter informações detalhadas',
      };
    }
  }

  Future<String> _generateUserId() async {
    // Gerando um ID universal único para o usuário
    const uuid = Uuid();
    final prefix = _generatePrefix();
    final uniqueId = uuid.v4().substring(0, 8);

    return 'P-$prefix-$uniqueId';
  }

  String _generatePrefix() {
    final random = Random();
    final letters = [
      'A',
      'B',
      'C',
      'D',
      'E',
      'F',
      'G',
      'H',
      'J',
      'K',
      'L',
      'M',
      'N',
      'P',
      'Q',
      'R',
      'S',
      'T',
      'U',
      'V',
      'W',
      'X',
      'Y',
      'Z'
    ];

    // Pegando 2 letras aleatórias, evitando confusão com números
    final letter1 = letters[random.nextInt(letters.length)];
    final letter2 = letters[random.nextInt(letters.length)];

    // Gerando 3 números aleatórios
    final numbers = random.nextInt(900) + 100; // Ensures 3 digits (100-999)

    return '$letter1$letter2$numbers';
  }

  Future<Map<String, dynamic>?> getUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userData = prefs.getString(_userDataKey);
      
      // Verificar também o flag de sessão
      final sessionActive = prefs.getString(_userSessionKey);

      if (userData != null && sessionActive != null) {
        return json.decode(userData);
      } else if (userData != null && sessionActive == null) {
        // Restaurar a sessão se os dados existem mas o flag não
        await prefs.setString(_userSessionKey, "active");
        return json.decode(userData);
      }
      return null;
    } catch (e) {
      debugPrint('Erro ao obter dados do usuário: $e');
      return null;
    }
  }
  
  // Limpar os dados do usuário ao fazer logout
  Future<void> clearUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_userSessionKey);
      // Não removemos o _userDataKey para manter o cache, apenas desativamos a sessão
      
      nomeController.text = '';
      telefoneController.text = '';
      formattedUserId.value = '';
      userDataExists.value = false;
      
      debugPrint('Dados da sessão do usuário limpos com sucesso');
    } catch (e) {
      debugPrint('Erro ao limpar dados do usuário: $e');
    }
  }

  String? validateNome(String? value) {
    if (value == null || value.isEmpty) {
      return 'Por favor, digite seu nome';
    }
    if (value.length < 3) {
      return 'O nome deve ter pelo menos 3 caracteres';
    }
    if (!RegExp(r'^[A-Za-zÀ-ÖØ-öø-ÿ\s]+$').hasMatch(value)) {
      return 'O nome deve conter apenas letras';
    }
    return null;
  }

  String? validateTelefone(String? value) {
    if (value == null || value.isEmpty) {
      return 'Por favor, digite seu telefone';
    }
    final cleanPhone = value.replaceAll(RegExp(r'[^0-9]'), '');
    if (cleanPhone.length < 10 || cleanPhone.length > 11) {
      return 'Digite um número de telefone válido';
    }

    // Verificar se começa com DDD válido
    if (!RegExp(r'^([1-9][0-9])').hasMatch(cleanPhone)) {
      return 'O telefone deve iniciar com um DDD válido';
    }

    return null;
  }

  Future<bool> updateUserData(String nome, String telefone) async {
    // This is basically a convenience method that calls saveUserData with isEditing=true
    return saveUserData(nome, telefone, isEditing: true);
  }
}
