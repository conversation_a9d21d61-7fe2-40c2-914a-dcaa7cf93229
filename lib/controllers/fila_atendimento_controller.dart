import 'package:get/get.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'package:flutter/material.dart';
import 'dart:async';
import '../utils/whatsapp_utils.dart';

class FilaAtendimentoController extends GetxController {
  final RxList<ParseObject> pacientesNaFila = <ParseObject>[].obs;
  final RxList<ParseObject> pacientesAtendidos = <ParseObject>[].obs;
  final RxBool isLoading = false.obs;
  final RxString error = ''.obs;

  ParseObject? currentHospital;
  Timer? refreshTimer;
  late final Map<String, dynamic> medicoData;

  FilaAtendimentoController({required this.medicoData});

  @override
  void onInit() {
    super.onInit();
    inicializarDados();
  }

  @override
  void onReady() {
    super.onReady();
    _iniciarAtualizacaoAutomatica();
  }

  @override
  void onClose() {
    refreshTimer?.cancel();
    super.onClose();
  }

  void _iniciarAtualizacaoAutomatica() {
    // Aumentar o intervalo de atualização de 30 segundos para 2 minutos
    refreshTimer = Timer.periodic(const Duration(minutes: 2), (_) {
      // Só atualizar se não estiver carregando e se o usuário estiver vendo a tela
      if (!isLoading.value && Get.currentRoute.contains('fila_atendimento')) {
        carregarPacientes();
      }
    });
  }

  Future<void> inicializarDados() async {
    isLoading.value = true;
    error.value = '';

    try {
      // Execute operations that can run in parallel
      final Future<void> hospitalFuture = _buscarHospitalAtual();

      // Só carregue os pacientes depois de ter o hospital atual
      await hospitalFuture;

      // Carregue pacientes e atendidos em paralelo
      await Future.wait([
        carregarPacientes(),
        carregarPacientesAtendidos(),
      ]);

      error.value = '';
    } catch (e) {
      error.value = 'Erro ao inicializar dados: $e';
      Get.snackbar(
        'Erro',
        error.value,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> _buscarHospitalAtual() async {
    try {
      final currentUser = await ParseUser.currentUser() as ParseUser?;
      if (currentUser == null) throw Exception('Usuário não encontrado');

      final queryHospital =
          QueryBuilder<ParseObject>(ParseObject('consultorio'))
            ..whereEqualTo('user_consultorio', currentUser.toPointer());

      final response = await queryHospital.query();

      if (!response.success ||
          response.results == null ||
          response.results!.isEmpty) {
        throw Exception('Hospital não encontrado');
      }

      currentHospital = response.results!.first;
    } catch (e) {
      throw Exception('Erro ao buscar hospital: $e');
    }
  }

  Future<void> carregarPacientes() async {
    if (currentHospital == null) return;

    try {
      final queryFila = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo(
            'medico', ParseObject('Medico')..objectId = medicoData['id'])
        ..whereEqualTo('consultorio', currentHospital)
        ..whereContainedIn('status', ['aguardando', 'em_atendimento'])
        ..orderByAscending('posicao');

      final response = await queryFila.query();

      if (response.success && response.results != null) {
        pacientesNaFila.value = response.results!.cast<ParseObject>();
      }
    } catch (e) {
      error.value = 'Erro ao carregar pacientes: $e';
    }
  }

  Future<void> carregarPacientesAtendidos() async {
    if (currentHospital == null) return;

    try {
      final hoje = DateTime.now();
      final inicioDoDia = DateTime(hoje.year, hoje.month, hoje.day);

      final queryAtendidos = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo(
            'medico', ParseObject('Medico')..objectId = medicoData['id'])
        ..whereEqualTo('consultorio', currentHospital)
        ..whereContainedIn('status', ['em_atendimento', 'atendido'])
        ..whereGreaterThanOrEqualsTo('data_inicio_atendimento', inicioDoDia)
        ..orderByDescending('data_inicio_atendimento');

      final response = await queryAtendidos.query();
      pacientesAtendidos.value = response.results?.cast<ParseObject>() ?? [];
    } catch (e) {
      print('Erro ao carregar pacientes atendidos: $e');
    }
  }

  Future<void> removerPaciente(ParseObject paciente, int index) async {
    try {
      isLoading.value = true;

      final filaSaida = ParseObject('FilaSaida')
        ..set('fila_id', paciente)
        ..set('motivo_saida', 'removido_secretaria')
        ..set('created_at', DateTime.now());

      await filaSaida.save();

      paciente.set('status', 'removido');
      await paciente.save();

      await _reajustarPosicoes(index);
      await carregarPacientes();

      Get.snackbar(
        'Sucesso',
        'Paciente removido da fila',
        backgroundColor: Colors.orange,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      error.value = 'Erro ao remover paciente: $e';
      Get.snackbar(
        'Erro',
        error.value,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> _reajustarPosicoes(int posicaoRemovida) async {
    try {
      final queryPacientes = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo(
            'medico', ParseObject('Medico')..objectId = medicoData['id'])
        ..whereEqualTo('consultorio', currentHospital)
        ..whereEqualTo('status', 'aguardando')
        ..whereGreaterThan('posicao', posicaoRemovida)
        ..orderByAscending('posicao');

      final response = await queryPacientes.query();

      if (response.success && response.results != null) {
        for (var paciente in response.results!) {
          final posicaoAtual = paciente.get<int>('posicao') ?? 0;
          paciente.set('posicao', posicaoAtual - 1);
          await paciente.save();
        }
      }
    } catch (e) {
      print('Erro ao reajustar posições: $e');
    }
  }

  Future<void> finalizarAtendimento(ParseObject paciente) async {
    try {
      isLoading.value = true;

      final agora = DateTime.now();

      paciente
        ..set('status', 'atendido')
        ..set('data_fim_atendimento', agora);

      await paciente.save();

      await carregarPacientes();

      Get.snackbar(
        'Sucesso',
        'Atendimento finalizado com sucesso',
        backgroundColor: Colors.green,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      error.value = 'Erro ao finalizar atendimento: $e';
      Get.snackbar(
        'Erro',
        error.value,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> iniciarAtendimento(ParseObject paciente) async {
    try {
      isLoading.value = true;

      paciente
        ..set('status', 'em_atendimento')
        ..set('data_inicio_atendimento', DateTime.now());

      await paciente.save();
      await carregarPacientes();

      Get.snackbar(
        'Sucesso',
        'Atendimento iniciado',
        backgroundColor: Colors.green,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      error.value = 'Erro ao iniciar atendimento: $e';
      Get.snackbar(
        'Erro',
        error.value,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> atualizarPosicoes(int oldIndex, int newIndex) async {
    try {
      isLoading.value = true;

      if (oldIndex == newIndex) return;

      final paciente = pacientesNaFila[oldIndex];
      final novaPosicao = newIndex + 1;

      if (newIndex > oldIndex) {
        for (var i = oldIndex + 1; i <= newIndex; i++) {
          final pac = pacientesNaFila[i];
          pac.set('posicao', pac.get<int>('posicao')! - 1);
          await pac.save();
        }
      } else {
        for (var i = newIndex; i < oldIndex; i++) {
          final pac = pacientesNaFila[i];
          pac.set('posicao', pac.get<int>('posicao')! + 1);
          await pac.save();
        }
      }

      paciente.set('posicao', novaPosicao);
      await paciente.save();

      await carregarPacientes();
    } catch (e) {
      error.value = 'Erro ao atualizar posições: $e';
      Get.snackbar(
        'Erro',
        error.value,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> abrirWhatsApp(String telefone) async {
    try {
      final sucesso = await WhatsAppUtils.abrirWhatsApp(telefone);
      if (!sucesso) {
        Get.snackbar(
          'Erro',
          'Não foi possível abrir o WhatsApp',
          backgroundColor: Colors.red,
          colorText: Colors.white,
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      Get.snackbar(
        'Erro',
        'Não foi possível abrir o WhatsApp',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }
}
