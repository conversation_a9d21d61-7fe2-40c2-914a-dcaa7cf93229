import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class FilaStateController {
  static const String _key = 'fila_state';

  static Future<void> salvar<PERSON>tadoF<PERSON>({
    required String filaId,
    required String medicoNome,
    required String especialidade,
    required int posicao,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    final filaState = {
      'filaId': filaId,
      'medicoNome': medicoNome,
      'especialidade': especialidade,
      'posicao': posicao,
      'timestamp': DateTime.now().toIso8601String(),
    };
    await prefs.setString(_key, jsonEncode(filaState));
  }

  static Future<Map<String, dynamic>?> recuperarEstadoFila() async {
    final prefs = await SharedPreferences.getInstance();
    final String? stateString = prefs.getString(_key);
    if (stateString != null) {
      return jsonDecode(stateString);
    }
    return null;
  }

  static Future<void> limparEstadoFila() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_key);
  }
}