import 'package:get/get.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'package:flutter/foundation.dart';
import 'package:fila_app/utils/api_cache_manager.dart';

class NotificationController extends GetxController {
  final RxMap<String, int> notificacoes = <String, int>{}.obs;
  final RxBool isAnimating = false.obs;
  final ApiCacheManager _cacheManager = ApiCacheManager();

  // Controle de acesso
  DateTime _lastCheck = DateTime.now().subtract(const Duration(minutes: 5));
  final int _minCheckIntervalMs = 3000; // 3 segundos entre verificações
  final int _normalIntervalMs = 30000; // 30 segundos em uso normal

  @override
  void onInit() {
    super.onInit();
    _inicializarCacheManager();
  }

  Future<void> _inicializarCacheManager() async {
    await _cacheManager.initialize();
  }

  Future<void> verificarNotificacoes(
      String medicoId, String consultorioId) async {
    // Verificar intervalo mínimo entre verificações
    final agora = DateTime.now();
    final elapsed = agora.difference(_lastCheck).inMilliseconds;

    // Se a verificação é frequente demais, ignorar (exceto se for a primeira)
    if (elapsed < _minCheckIntervalMs && notificacoes.isNotEmpty) {
      debugPrint('Verificação de notificações adiada - muito frequente');
      return;
    }

    // Se o intervalo é normal e não há urgência, usar cache
    final usarCache = elapsed < _normalIntervalMs;

    try {
      final cacheKey = 'notificacoes_$medicoId';

      final resultado =
          await _cacheManager.executeWithCache<Map<String, dynamic>>(
        endpoint: cacheKey,
        params: {
          'medicoId': medicoId,
          'consultorioId': consultorioId,
        },
        fetchFunction: () async {
          // Consulta normal para solicitações pendentes
          final querySolicitacoes =
              QueryBuilder<ParseObject>(ParseObject('FilaSolicitacao'))
                ..whereEqualTo(
                    'medicoId', ParseObject('Medico')..objectId = medicoId)
                ..whereEqualTo('hospitalId',
                    ParseObject('consultorio')..objectId = consultorioId)
                ..whereEqualTo('status', 'pendente');

          final response = await querySolicitacoes.query();

          if (response.success && response.results != null) {
            return {
              'count': response.results!.length,
              'timestamp': DateTime.now().toIso8601String()
            };
          } else {
            return {'count': 0, 'timestamp': DateTime.now().toIso8601String()};
          }
        },
        maxAge: const Duration(seconds: 15),
        forceRefresh: !usarCache,
      );

      final novoNumero = resultado['count'] as int;
      final numeroAntigo = notificacoes[medicoId] ?? 0;

      if (novoNumero > numeroAntigo) {
        isAnimating.value = true;
        await Future.delayed(const Duration(milliseconds: 500));
        isAnimating.value = false;
      }

      notificacoes[medicoId] = novoNumero;
      _lastCheck = agora;
      update();
    } catch (e) {
      debugPrint('Erro ao verificar notificações: $e');
    }
  }

  Future<void> marcarComoProcessada(String solicitacaoId) async {
    try {
      final queryFila =
          QueryBuilder<ParseObject>(ParseObject('FilaSolicitacao'))
            ..whereEqualTo('objectId', solicitacaoId);

      final response = await queryFila.query();

      if (response.success &&
          response.results != null &&
          response.results!.isNotEmpty) {
        final solicitacao = response.results!.first;
        final medicoId = solicitacao.get<ParseObject>('medicoId')?.objectId;

        if (medicoId != null) {
          notificacoes[medicoId] = (notificacoes[medicoId] ?? 1) - 1;

          // Invalidar cache de notificações
          await _cacheManager.invalidateCache('notificacoes_$medicoId', null);

          update();
        }
      }
    } catch (e) {
      debugPrint('Erro ao marcar solicitação como processada: $e');
    }
  }

  Future<List> buscarSolicitacoesPendentes(
      String medicoId, String consultorioId) async {
    try {
      // Usar cache para esta operação com tempo curto
      return await _cacheManager.executeWithCache<List>(
        endpoint: 'solicitacoes_pendentes',
        params: {
          'medicoId': medicoId,
          'consultorioId': consultorioId,
        },
        fetchFunction: () async {
          final querySolicitacoes =
              QueryBuilder<ParseObject>(ParseObject('FilaSolicitacao'))
                ..whereEqualTo(
                    'medicoId', ParseObject('Medico')..objectId = medicoId)
                ..whereEqualTo('hospitalId',
                    ParseObject('consultorio')..objectId = consultorioId)
                ..whereEqualTo('status', 'pendente')
                ..orderByAscending('createdAt');

          final response = await querySolicitacoes.query();

          if (response.success && response.results != null) {
            return response.results!;
          }
          return [];
        },
        maxAge: const Duration(seconds: 15),
      );
    } catch (e) {
      debugPrint('Erro ao buscar solicitações: $e');
      return [];
    }
  }

  void marcarTodasComoLidas(String medicoId) {
    notificacoes[medicoId] = 0;
    update();
  }

  // Função para registrar token de notificações push
  Future<bool> registrarTokenNotificacoes(String deviceId, String token) async {
    try {
      final resultado =
          await _cacheManager.executeCloudFunction<Map<String, dynamic>>(
        functionName: 'registerDeviceForNotifications',
        params: {
          'deviceId': deviceId,
          'token': token,
        },
        minIntervalMs: 60000, // Limite de uma vez por minuto
      );

      return resultado['success'] == true;
    } catch (e) {
      debugPrint('Erro ao registrar token: $e');
      return false;
    }
  }

  // Função para testar notificações push
  Future<bool> enviarNotificacaoTeste(String deviceId) async {
    try {
      final resultado =
          await _cacheManager.executeCloudFunction<Map<String, dynamic>>(
        functionName: 'sendTestNotification',
        params: {
          'deviceId': deviceId,
          'message':
              'Esta é uma notificação de teste enviada em ${DateTime.now()}',
        },
        minIntervalMs: 30000, // Limite de uma vez a cada 30 segundos
      );

      return resultado['success'] == true;
    } catch (e) {
      debugPrint('Erro ao enviar notificação de teste: $e');
      return false;
    }
  }

  // Função para diagnosticar problemas de notificação
  Future<Map<String, dynamic>> diagnosticarNotificacoes(String deviceId) async {
    try {
      return await _cacheManager.executeCloudFunction<Map<String, dynamic>>(
        functionName: 'diagnosticarNotificacoes',
        params: {
          'deviceId': deviceId,
        },
        minIntervalMs: 60000, // Limite de uma vez por minuto
      );
    } catch (e) {
      debugPrint('Erro ao diagnosticar notificações: $e');
      return {'status': 'erro', 'mensagem': 'Erro ao diagnosticar: $e'};
    }
  }
}
