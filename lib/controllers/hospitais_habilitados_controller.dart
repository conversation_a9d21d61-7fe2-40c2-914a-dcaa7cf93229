import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'dart:async';
import '../utils/live_query_manager.dart'; // Importar o gerenciador

class HospitaisHabilitadosController extends GetxController {
  final RxList<ParseObject> hospitals = <ParseObject>[].obs;
  final RxList<bool> selectedHospitals = <bool>[].obs;
  final RxBool isLoading = false.obs;
  final RxString error = ''.obs;
  final RxString searchQuery = ''.obs;
  ParseObject? currentMedico;
  
  // Controle para evitar carregamento automático desnecessário
  final RxBool autoLoadingEnabled = false.obs;
  
  // Controlar se já carregou hospitais pela primeira vez
  final RxBool _hospitaisJaCarregados = false.obs;

  // LiveQuery
  Subscription? hospitalUpdateSubscription;
  final _liveQueryManager = LiveQueryManager(); // Usar o gerenciador

  @override
  void onInit() {
    super.onInit();
    
    // Carregar o médico atual e depois os hospitais na primeira inicialização
    _carregarDadosIniciais();
    
    // Monitorar estado da conexão LiveQuery, apenas para manter atualizado
    _liveQueryManager.connectionState.addListener(_onLiveQueryStateChanged);
  }

  // Método para carregar dados iniciais
  Future<void> _carregarDadosIniciais() async {
    await _loadCurrentMedico();
    
    // Se for a primeira vez, carregar os hospitais
    if (!_hospitaisJaCarregados.value) {
      debugPrint('⭐ Primeira abertura da tela - carregando hospitais automaticamente');
      await loadHospitals(isPrimeiraVez: true);
      _hospitaisJaCarregados.value = true;
    }
  }

  @override
  void onClose() {
    _cancelLiveQuery();
    _liveQueryManager.connectionState.removeListener(_onLiveQueryStateChanged);
    super.onClose();
  }

  // Observer para mudanças no estado do LiveQuery
  void _onLiveQueryStateChanged() {
    final state = _liveQueryManager.connectionState.value;
    debugPrint('Estado do LiveQuery alterado: $state');
    
    // Iniciar LiveQuery apenas se for explicitamente solicitado
    if (state == LiveQueryConnectionState.connected && 
        hospitalUpdateSubscription == null &&
        autoLoadingEnabled.value) {
      _initLiveQuery();
    }
  }
  
  // Carregar apenas os dados do médico atual, sem fazer outras requisições
  Future<void> _loadCurrentMedico() async {
    try {
      isLoading.value = true;
      error.value = '';

      final currentUser = await ParseUser.currentUser() as ParseUser?;
      if (currentUser == null) {
        error.value = 'Usuário não encontrado';
        return;
      }

      final queryMedico = QueryBuilder<ParseObject>(ParseObject('Medico'))
        ..whereEqualTo('user_medico', currentUser.toPointer());
      final medicoResponse = await queryMedico.query();

      if (!medicoResponse.success ||
          medicoResponse.results == null ||
          medicoResponse.results!.isEmpty) {
        error.value = 'Perfil médico não encontrado';
        return;
      }

      currentMedico = medicoResponse.results!.first;
      debugPrint('Médico carregado: ${currentMedico?.objectId}');
      
    } catch (e) {
      error.value = e.toString();
      debugPrint('Erro ao carregar médico: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Inicializar LiveQuery para atualizações em tempo real
  Future<void> _initLiveQuery() async {
    try {
      debugPrint('Inicializando LiveQuery para atualizações de hospitais');

      // Se já existe uma inscrição ativa, não fazer nada
      if (hospitalUpdateSubscription != null) {
        debugPrint('LiveQuery já inicializado');
        return;
      }

      // Criar query para a classe HospitalMedicoUpdate
      final query =
          QueryBuilder<ParseObject>(ParseObject('HospitalMedicoUpdate'));

      // Se já temos o médico atual, filtrar por ele
      if (currentMedico != null) {
        query.whereEqualTo('medicoId', currentMedico!.objectId);
      }

      // Inscrever-se usando o gerenciador
      hospitalUpdateSubscription = await _liveQueryManager.subscribe(query);

      // Configurar handlers para eventos
      if (hospitalUpdateSubscription != null) {
        hospitalUpdateSubscription!.on(LiveQueryEvent.create, (value) {
          if (value is ParseObject) {
            debugPrint('LiveQuery: Nova atualização recebida');
            _handleHospitalUpdate(value);
          }
        });

        debugPrint('LiveQuery inicializado com sucesso');
      } else {
        debugPrint('LiveQuery falhou, verificar estado no gerenciador');
      }
    } catch (e) {
      debugPrint('Erro ao inicializar LiveQuery: $e');
    }
  }

  // Cancelar inscrição do LiveQuery
  void _cancelLiveQuery() {
    debugPrint('Cancelando inscrição do LiveQuery');
    _liveQueryManager.unsubscribe(hospitalUpdateSubscription);
    hospitalUpdateSubscription = null;
  }

  // Tratar atualizações de hospitais
  void _handleHospitalUpdate(ParseObject update) {
    try {
      final hospitalId = update.get<String>('hospitalId');
      final medicoId = update.get<String>('medicoId');
      final action = update.get<String>('action');

      debugPrint(
          'Atualização recebida: hospital=$hospitalId, médico=$medicoId, ação=$action');

      // Verificar se é relevante para o médico atual e se o autoloading está ativado
      if (currentMedico != null && medicoId == currentMedico!.objectId && autoLoadingEnabled.value) {
        // Recarregar a lista de hospitais
        loadHospitals();
      }
    } catch (e) {
      debugPrint('Erro ao processar atualização: $e');
    }
  }
  
  // Método para habilitar/desabilitar atualizações automáticas
  void toggleAutoLoading(bool enable) {
    autoLoadingEnabled.value = enable;
    debugPrint('Atualizações automáticas: ${enable ? 'ATIVADAS' : 'DESATIVADAS'}');
    
    if (enable && hospitalUpdateSubscription == null) {
      _initLiveQuery();
    } else if (!enable && hospitalUpdateSubscription != null) {
      _cancelLiveQuery();
    }
  }

  Future<void> loadHospitals({bool isPrimeiraVez = false}) async {
    try {
      if (isPrimeiraVez) {
        debugPrint('⭐ CARREGANDO HOSPITAIS - Primeira vez na tela');
      } else {
        debugPrint('⭐ CARREGANDO HOSPITAIS - Usuário clicou no botão de atualizar');
      }
      
      isLoading.value = true;
      error.value = '';

      final currentUser = await ParseUser.currentUser() as ParseUser?;
      if (currentUser == null) throw Exception('Usuário não encontrado');
      debugPrint('⭐ Usuário autenticado: ${currentUser.username}');

      final queryMedico = QueryBuilder<ParseObject>(ParseObject('Medico'))
        ..whereEqualTo('user_medico', currentUser.toPointer());
      final medicoResponse = await queryMedico.query();

      if (!medicoResponse.success ||
          medicoResponse.results == null ||
          medicoResponse.results!.isEmpty) {
        throw Exception('Perfil médico não encontrado');
      }

      // Verificar se o médico mudou
      final novoMedico = medicoResponse.results!.first;
      final medicoMudou = currentMedico?.objectId != novoMedico.objectId;
      
      debugPrint('⭐ Médico encontrado: ${novoMedico.objectId}');
      if (medicoMudou) {
        debugPrint('⭐ Médico mudou desde o último carregamento');
      }

      currentMedico = novoMedico;

      // Se o médico mudou, reiniciar o LiveQuery
      if (medicoMudou) {
        debugPrint('Médico mudou, reiniciando LiveQuery');
        _cancelLiveQuery();
        _initLiveQuery();
      }

      debugPrint('⭐ Buscando lista de hospitais ativos');
      final queryHospitals =
          QueryBuilder<ParseObject>(ParseObject('consultorio'))
            ..whereEqualTo('ativo', true)
            ..orderByAscending('nome');

      final response = await queryHospitals.query();

      if (response.success && response.results != null) {
        final hospitais = response.results!;
        debugPrint('⭐ ${hospitais.length} hospitais encontrados');
        hospitals.value = hospitais.cast<ParseObject>();
        selectedHospitals.value = List.generate(hospitais.length, (index) {
          final hospital = hospitais[index];
          final medicosVinculados =
              hospital.get<List>('medicos_vinculados') ?? [];
          
          // Verificar se o médico está vinculado
          final estaVinculado = medicosVinculados.any((medico) {
            // Caso 1: medico é um ParseObject completo
            if (medico is ParseObject &&
                medico.objectId == currentMedico?.objectId) {
              return true;
            }

            // Caso 2: medico é um ponteiro no formato {__type: 'Pointer', className: 'Medico', objectId: 'xyz'}
            if (medico is Map &&
                medico['__type'] == 'Pointer' &&
                medico['objectId'] == currentMedico?.objectId) {
              return true;
            }

            // Caso 3: medico tem propriedade objectId diretamente
            if (medico is Map &&
                medico['objectId'] == currentMedico?.objectId) {
              return true;
            }

            // Caso 4: medico tem propriedade id
            if (medico is Map && medico['id'] == currentMedico?.objectId) {
              return true;
            }

            return false;
          });
          
          if (estaVinculado) {
            debugPrint('⭐ Hospital ${hospital.get<String>('nome')} está vinculado ao médico');
          }
          
          return estaVinculado;
        });
      }
    } catch (e) {
      debugPrint('❌ ERRO ao carregar hospitais: $e');
      error.value = e.toString();
      Get.snackbar(
        'Erro',
        'Erro ao carregar hospitais: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoading.value = false;
      debugPrint('⭐ Carregamento de hospitais concluído');
    }
  }

  Future<void> updateHospitalSelection(int index, bool value) async {
    if (currentMedico == null) return;

    try {
      debugPrint('⭐ ALTERANDO VÍNCULO DE HOSPITAL - Médico: ${currentMedico!.objectId}');
      isLoading.value = true;
      error.value = '';

      // Garantir que estamos usando o hospital correto
      final hospital = hospitals[index];

      // Log para depuração
      debugPrint('⭐ Hospital: ${hospital.get<String>('nome')} (${hospital.objectId})');
      debugPrint('⭐ Ação: ${value ? 'VINCULAR' : 'DESVINCULAR'} médico');

      final params = {
        'medicoId': currentMedico!.objectId,
        'hospitalId': hospital.objectId,
      };

      final ParseCloudFunction function = ParseCloudFunction(
          value ? 'vincularMedicoHospital' : 'desvincularMedicoHospital');

      debugPrint('⭐ Chamando função cloud: ${function.functionName}');
      final result = await function.execute(parameters: params);

      if (result.success) {
        debugPrint('✅ Operação realizada com sucesso');

        // Atualizar o estado local
        selectedHospitals[index] = value;

        // Atualizar a lista de médicos vinculados no objeto hospital
        List medicosVinculados = hospital.get<List>('medicos_vinculados') ?? [];
        
        if (value) {
          // Adicionar médico se não estiver na lista
          final medicoJaVinculado = medicosVinculados.any((medico) {
            if (medico is ParseObject) {
              return medico.objectId == currentMedico!.objectId;
            }
            if (medico is Map && medico['__type'] == 'Pointer') {
              return medico['objectId'] == currentMedico!.objectId;
            }
            if (medico is Map && medico['objectId'] != null) {
              return medico['objectId'] == currentMedico!.objectId;
            }
            if (medico is Map && medico['id'] != null) {
              return medico['id'] == currentMedico!.objectId;
            }
            return false;
          });

          if (!medicoJaVinculado) {
            // Criar um ponteiro para o médico
            final medicoPointer = {
              '__type': 'Pointer',
              'className': 'Medico',
              'objectId': currentMedico!.objectId
            };
            medicosVinculados.add(medicoPointer);
            hospital.set('medicos_vinculados', medicosVinculados);
          }
          debugPrint('✅ Médico adicionado à lista de médicos vinculados do hospital');
        } else {
          // Remover médico da lista
          medicosVinculados = medicosVinculados.where((medico) {
            if (medico is ParseObject) {
              return medico.objectId != currentMedico!.objectId;
            }
            if (medico is Map && medico['__type'] == 'Pointer') {
              return medico['objectId'] != currentMedico!.objectId;
            }
            if (medico is Map && medico['objectId'] != null) {
              return medico['objectId'] != currentMedico!.objectId;
            }
            if (medico is Map && medico['id'] != null) {
              return medico['id'] != currentMedico!.objectId;
            }
            return true;
          }).toList();
          hospital.set('medicos_vinculados', medicosVinculados);
          debugPrint('✅ Médico removido da lista de médicos vinculados do hospital');
        }

        // Não precisamos mais recarregar manualmente, o LiveQuery fará isso
        debugPrint('Operação concluída, aguardando notificação do LiveQuery');

        Get.snackbar(
          'Sucesso',
          value
              ? 'Hospital habilitado com sucesso'
              : 'Hospital desabilitado com sucesso',
          backgroundColor: value ? Colors.green : Colors.orange,
          colorText: Colors.white,
          snackPosition: SnackPosition.BOTTOM,
        );
      } else {
        debugPrint('❌ Erro na operação: ${result.error?.message}');
        throw Exception(result.error?.message ?? 'Erro ao atualizar vínculo');
      }
    } catch (e) {
      debugPrint('❌ Exceção: $e');
      error.value = e.toString();
      Get.snackbar(
        'Erro',
        'Erro ao atualizar seleção: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoading.value = false;
      debugPrint('⭐ Operação de vínculo concluída');
    }
  }

  List<ParseObject> getFilteredHospitals() {
    if (searchQuery.isEmpty) return hospitals;

    return hospitals.where((hospital) {
      final nome = hospital.get<String>('nome')?.toLowerCase() ?? '';
      final cidade = hospital.get<String>('cidade')?.toLowerCase() ?? '';
      final estado = hospital.get<String>('estado')?.toLowerCase() ?? '';
      final query = searchQuery.value.toLowerCase();

      return nome.contains(query) ||
          cidade.contains(query) ||
          estado.contains(query);
    }).toList();
  }

  int getOriginalIndex(ParseObject hospital) {
    // Encontrar o índice do hospital na lista original
    final index = hospitals.indexWhere((h) => h.objectId == hospital.objectId);

    // Se não encontrar (o que seria estranho), retorna 0 como fallback
    if (index == -1) {
      debugPrint(
          'AVISO: Hospital não encontrado na lista original: ${hospital.objectId}');
      return 0;
    }

    return index;
  }
}
