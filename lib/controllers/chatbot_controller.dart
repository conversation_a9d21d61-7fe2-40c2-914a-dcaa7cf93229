import 'package:get/get.dart';
import 'package:flutter/material.dart';
import '../models/chat_message.dart';
import '../services/ai_service.dart';
import 'dart:math';

class ChatbotController extends GetxController {
  final messages = <ChatMessage>[].obs;
  final isTyping = false.obs;
  final TextEditingController textController = TextEditingController();
  final ScrollController scrollController = ScrollController();

  // Instância do serviço AI
  final AIService _aiService = AIService();

  // Histórico para API do OpenAI
  final List<Map<String, String>> _messageHistory = [];

  // Adicione estas propriedades na classe ChatbotController
  final isConnectingToSupport = false.obs;
  final isConnectedToSupport = false.obs;
  final supportAgentName = "".obs;

  // Lista de nomes de atendentes para simulação
  final List<String> _supportAgentNames = [
    "<PERSON>e",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>"
  ];

  // Contador de mensagens no suporte humano
  final _supportMessageCount = 0.obs;

  @override
  void onInit() {
    super.onInit();
    // Add welcome message when initialized
    messages.addAll(ChatMessage.getInitialMessages());

    // Adicionar mensagem de boas-vindas ao histórico
    _messageHistory.add({
      "role": "assistant",
      "content": ChatMessage.getInitialMessages().first.text
    });
  }

  @override
  void onClose() {
    textController.dispose();
    scrollController.dispose();
    super.onClose();
  }

  void sendMessage(String text) async {
    if (text.trim().isEmpty) return;

    // Add user message
    final userMessage = ChatMessage(
      text: text,
      type: ChatMessageType.user,
    );
    messages.add(userMessage);
    textController.clear();

    // Scroll to bottom
    _scrollToBottom();

    // Se estiver conectado ao suporte, processar de forma diferente
    if (isConnectedToSupport.value) {
      await _generateSupportAgentResponse(text);
      return;
    }

    // Adicionar ao histórico se não estiver no suporte
    _messageHistory.add({"role": "user", "content": text});

    // Show typing indicator
    isTyping.value = true;
    _addTypingIndicator();

    try {
      // Obter resposta da IA
      final response = await _aiService.getCompletion(text, _messageHistory);

      _removeTypingIndicator();

      // Processar resposta para verificar necessidade de encaminhamento
      final processedResponse = await _processAIResponse(response);

      // Add bot response
      final botMessage = ChatMessage(
        text: processedResponse,
        type: ChatMessageType.bot,
      );
      messages.add(botMessage);

      // Iniciar conexão com suporte se foi solicitado
      if (response.startsWith("ENCAMINHAR_PARA_SUPORTE")) {
        await Future.delayed(const Duration(seconds: 1));
        await _connectToSupportAgent();
      } else {
        // Adicionar ao histórico (limitado a últimas 20 mensagens para economizar tokens)
        _messageHistory
            .add({"role": "assistant", "content": processedResponse});
        if (_messageHistory.length > 20) {
          _messageHistory.removeRange(
              2, 4); // Manter a primeira mensagem do sistema
        }
      }
    } catch (e) {
      _removeTypingIndicator();
      // Fallback para respostas simples em caso de falha
      final fallbackResponse = _findSimpleAnswer(text);

      final botMessage = ChatMessage(
        text: fallbackResponse,
        type: ChatMessageType.bot,
      );
      messages.add(botMessage);

      // Iniciar conexão com suporte se foi solicitado pelo fallback
      if (fallbackResponse.startsWith("ENCAMINHAR_PARA_SUPORTE")) {
        await Future.delayed(const Duration(seconds: 1));
        await _connectToSupportAgent();
      }
    } finally {
      isTyping.value = false;
      _scrollToBottom();
    }
  }

  void _addTypingIndicator() {
    messages.add(ChatMessage(
      text: '',
      type: ChatMessageType.bot,
      isTyping: true,
    ));
    _scrollToBottom();
  }

  void _removeTypingIndicator() {
    messages.removeWhere((message) => message.isTyping);
  }

  void _scrollToBottom() {
    // Use Future.delayed to ensure the list has been updated in the UI
    Future.delayed(const Duration(milliseconds: 50), () {
      if (scrollController.hasClients) {
        scrollController.animateTo(
          scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  // Método para respostas simples como fallback
  String _findSimpleAnswer(String query) {
    final lowerQuery = query.toLowerCase();

    // Verificar se o usuário está solicitando suporte humano
    if (_containsAny(lowerQuery, [
      'falar com humano',
      'atendente',
      'pessoa real',
      'suporte humano',
      'agente',
      'chat humano'
    ])) {
      return "ENCAMINHAR_PARA_SUPORTE Solicitação direta de atendimento humano";
    }

    // Check for greetings
    if (_containsAny(lowerQuery,
        ['olá', 'oi', 'bom dia', 'boa tarde', 'boa noite', 'hello', 'hi'])) {
      return 'Olá! Como posso ajudar você hoje?';
    }

    // Check for app functionality questions
    if (_containsAny(lowerQuery, ['como', 'funciona', 'aplicativo', 'app'])) {
      return 'O Saúde Sem Espera permite que você entre na fila de atendimento médico virtualmente, acompanhe sua posição em tempo real e receba notificações sobre o andamento do atendimento.';
    }

    // Check for fila-related questions
    if (_containsAny(
        lowerQuery, ['fila', 'atendimento', 'entrar', 'consulta'])) {
      return 'Para entrar na fila de atendimento:\n1. Acesse a seção "Paciente"\n2. Escaneie o QR Code do médico/hospital\n3. Aguarde a confirmação\n4. Acompanhe sua posição na fila';
    }

    // Check for account-related questions
    if (_containsAny(
        lowerQuery, ['conta', 'perfil', 'cadastro', 'dados', 'atualizar'])) {
      return 'Para atualizar seus dados:\n1. Acesse seu perfil\n2. Clique em "Editar"\n3. Atualize as informações\n4. Salve as alterações';
    }

    // Check for login issues
    if (_containsAny(
        lowerQuery, ['login', 'senha', 'entrar', 'acesso', 'esqueci'])) {
      return 'Se tiver problemas com login:\n1. Verifique seu e-mail e senha\n2. Use a opção "Esqueci a senha"\n3. Verifique sua conexão\n4. Limpe o cache do app';
    }

    // Check for cancelation questions
    if (_containsAny(lowerQuery, ['cancelar', 'desistir', 'sair'])) {
      return 'Para cancelar seu lugar na fila, acesse a tela de acompanhamento e toque no botão "Sair da fila". Sua vaga será liberada imediatamente.';
    }

    // Check for complex issues or complaints
    if (_containsAny(lowerQuery, [
      'problema',
      'não funciona',
      'erro',
      'bug',
      'travando',
      'falha',
      'reclamação',
      'queixa'
    ])) {
      return "ENCAMINHAR_PARA_SUPORTE Problema técnico reportado pelo usuário";
    }

    // Check for support requests
    if (_containsAny(lowerQuery, ['ajuda', 'suporte', 'problema', 'contato'])) {
      return 'Para obter ajuda personalizada, você pode entrar em contato pelo e-mail <EMAIL> ou pelo telefone 0800-123-4567. Estamos disponíveis 24/7.';
    }

    // Check for medical questions
    if (_containsAny(lowerQuery, [
      'médico',
      'doença',
      'sintoma',
      'tratamento',
      'remédio',
      'medicamento',
      'saúde',
      'dor'
    ])) {
      return "ENCAMINHAR_PARA_SUPORTE Dúvida médica que requer atendimento especializado";
    }

    // Fallback response
    return 'Não tenho uma resposta específica para isso. Você pode reformular sua pergunta ou explorar as categorias de ajuda disponíveis na tela principal.';
  }

  bool _containsAny(String text, List<String> keywords) {
    return keywords.any((keyword) => text.contains(keyword));
  }

  // Método para simular conexão com suporte humano
  Future<void> _connectToSupportAgent() async {
    isConnectingToSupport.value = true;

    // Simular conexão com suporte (em produção, isso seria uma chamada real à API)
    await Future.delayed(const Duration(seconds: 2));

    // Selecionar um atendente aleatoriamente
    final random = Random();
    final agentIndex = random.nextInt(_supportAgentNames.length);

    // Atualizar status de conexão
    isConnectingToSupport.value = false;
    isConnectedToSupport.value = true;
    supportAgentName.value = _supportAgentNames[agentIndex];
    _supportMessageCount.value = 0;

    // Adicionar mensagem do sistema sobre conexão
    final systemMessage = ChatMessage(
      text: "Você foi conectado ao atendente ${supportAgentName.value}.",
      type: ChatMessageType.system,
    );
    messages.add(systemMessage);

    // Adicionar primeira mensagem do atendente
    await Future.delayed(const Duration(milliseconds: 1000));
    isTyping.value = true;
    _addTypingIndicator();

    await Future.delayed(const Duration(milliseconds: 1500));

    _removeTypingIndicator();

    final supportResponse = ChatMessage(
      text:
          "Olá, sou ${supportAgentName.value}, especialista em suporte. Li o histórico da sua conversa e estou aqui para ajudá-lo. Como posso resolver seu problema hoje?",
      type: ChatMessageType.bot,
    );

    messages.add(supportResponse);
    isTyping.value = false;
    _scrollToBottom();
  }

  // Método para gerar resposta do atendente humano
  Future<void> _generateSupportAgentResponse(String userMessage) async {
    _supportMessageCount.value++;

    isTyping.value = true;
    _addTypingIndicator();

    // Simular tempo de digitação do atendente humano (mais longo que o bot)
    final random = Random();
    final typingDelay = Duration(milliseconds: 1500 + random.nextInt(1500));
    await Future.delayed(typingDelay);

    _removeTypingIndicator();

    String response;

    // Respostas baseadas no contador de mensagens para simular uma conversa real
    if (_supportMessageCount.value == 1) {
      response =
          "Obrigado pelas informações. Estou verificando seu caso nos nossos sistemas. Pode me confirmar qual unidade de saúde você está tentando acessar?";
    } else if (_supportMessageCount.value == 2) {
      response =
          "Perfeito. Estou analisando os dados da sua conta e da unidade. Só um momento, por favor.";
    } else if (_supportMessageCount.value == 3) {
      response =
          "Identifiquei o problema e já estou tomando as medidas necessárias para resolvê-lo. Isso deve levar aproximadamente 24 horas para ser concluído. Há mais alguma informação que você gostaria de adicionar?";
    } else if (_supportMessageCount.value == 4) {
      response =
          "Ótimo. Registrei todas as informações no nosso sistema com o protocolo #${10000 + random.nextInt(90000)}. Você receberá uma notificação assim que o problema for resolvido. Posso ajudar com mais alguma coisa hoje?";
    } else if (_supportMessageCount.value >= 5) {
      response =
          "Entendi. Se precisar de mais assistência no futuro, não hesite em contatar novamente nosso suporte. Estamos sempre à disposição para ajudar. Tenha um ótimo dia!";
    } else {
      response =
          "Estou analisando sua solicitação. Por favor, me dê mais detalhes para que eu possa ajudar de forma mais eficiente.";
    }

    final supportResponse = ChatMessage(
      text: response,
      type: ChatMessageType.bot,
    );

    messages.add(supportResponse);
    isTyping.value = false;
    _scrollToBottom();
  }

  // Método para processar a resposta da IA e verificar se precisa encaminhar para suporte
  Future<String> _processAIResponse(String response) async {
    if (response.startsWith("ENCAMINHAR_PARA_SUPORTE")) {
      // Extrair o motivo do encaminhamento
      final reason =
          response.replaceFirst("ENCAMINHAR_PARA_SUPORTE", "").trim();

      // Retornar mensagem indicando que será conectado ao suporte
      return "Entendo que preciso transferir você para um atendente especializado. $reason\n\nVou conectá-lo com um de nossos atendentes humanos agora.";
    }

    return response;
  }

  // Método para desconectar do suporte
  void disconnectFromSupport() {
    isConnectedToSupport.value = false;
    supportAgentName.value = "";

    // Adicionar mensagem do sistema
    final systemMessage = ChatMessage(
      text: "Chat com suporte encerrado. Como posso ajudar você agora?",
      type: ChatMessageType.bot,
    );
    messages.add(systemMessage);
  }
}
