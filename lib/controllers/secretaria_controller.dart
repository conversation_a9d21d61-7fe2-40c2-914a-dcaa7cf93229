// lib/controllers/secretaria_controller.dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'package:fila_app/models/fila.dart';
import '../utils/whatsapp_utils.dart';

class SecretariaController extends GetxController {
  final RxList<ParseObject> medicos = <ParseObject>[].obs;
  final RxBool isLoading = false.obs;
  final RxString error = ''.obs;
  final RxList<ParseObject> mensagensPredefinidas = <ParseObject>[].obs;
  final RxList<ParseObject> pacientesAtendidos = <ParseObject>[].obs;
  final RxBool hasMorePacientesAtendidos = false.obs;
  final Rx<ParseObject?> medicoSelecionado = Rx<ParseObject?>(null);

  // Lista de mensagens predefinidas padrão
  final List<Map<String, dynamic>> mensagensPadroes = [
    {
      'titulo': 'Atendimento atrasado',
      'texto':
          'O atendimento do médico está atrasado. Pedimos sua compreensão.',
      'prioridade': 'media',
      'icone': 'clock'
    },
    {
      'titulo': 'Médico ausente',
      'texto':
          'O médico teve uma emergência e não poderá atender no horário previsto.',
      'prioridade': 'alta',
      'icone': 'doctor'
    },
    {
      'titulo': 'Consulta antecipada',
      'texto':
          'O atendimento está adiantado. Por favor, dirija-se ao consultório.',
      'prioridade': 'media',
      'icone': 'fast'
    },
    {
      'titulo': 'Prioridade emergências',
      'texto': 'Casos emergenciais estão sendo atendidos com prioridade.',
      'prioridade': 'alta',
      'icone': 'emergency'
    },
    {
      'titulo': 'Interrupção temporária',
      'texto': 'O atendimento será pausado momentaneamente.',
      'prioridade': 'media',
      'icone': 'pause'
    },
    {
      'titulo': 'Comparecer à recepção',
      'texto': 'Por favor, compareça à recepção.',
      'prioridade': 'alta',
      'icone': 'reception'
    },
  ];

  // Objetos Parse
  ParseObject? currentSecretaria;
  ParseObject? currentHospital;

  // Rastreamento de mensagens enviadas recentemente para evitar duplicatas
  final RxList<Map<String, dynamic>> mensagensRecentes =
      <Map<String, dynamic>>[].obs;

  @override
  void onInit() {
    super.onInit();
    // Usar Future.microtask ou delayed para adiar as chamadas reativas
    Future.microtask(() {
      carregarDadosSecretaria();
      _checkAndRequestSecretariaRole();
    });

    // Usar um timer para a correção de status
    Future.delayed(const Duration(seconds: 5), () {
      if (currentHospital != null) {
        corrigirStatusEmFilaUsuarios().then((result) {
          if (result['success']) {
            debugPrint("Status em_fila corrigido: ${result['message']}");
          } else {
            debugPrint("Erro ao corrigir status em_fila: ${result['error']}");
          }
        });
      }
    });
  }

  // Verificar e solicitar role de secretária se necessário
  Future<void> _checkAndRequestSecretariaRole() async {
    try {
      final currentUser = await ParseUser.currentUser() as ParseUser?;
      if (currentUser == null) return;

      // Verificar se o usuário já tem a role de secretária
      final roles = currentUser.get<List>('roles') ?? [];
      final hasSecretariaRole = roles.contains('role:secretaria');

      if (!hasSecretariaRole) {
        // Solicitar adição da role de secretária
        try {
          final params = <String, dynamic>{
            'userId': currentUser.objectId,
            'role': 'role:secretaria'
          };

          await ParseCloudFunction('addUserRole').execute(parameters: params);
          debugPrint('Role de secretária adicionada com sucesso');
        } catch (e) {
          debugPrint('Erro ao adicionar role de secretária: $e');
        }
      }
    } catch (e) {
      debugPrint('Erro ao verificar roles do usuário: $e');
    }
  }

  Future<void> carregarDadosSecretaria() async {
    try {
      isLoading.value = true;
      error.value = '';

      final currentUser = await ParseUser.currentUser() as ParseUser?;
      if (currentUser == null) throw Exception('Usuário não encontrado');

      // Buscar perfil da secretária
      final querySecretaria =
          QueryBuilder<ParseObject>(ParseObject('Secretaria'))
            ..whereEqualTo('user_secretaria', currentUser.toPointer())
            ..includeObject(['consultorio']);

      final secretariaResponse = await querySecretaria.query();

      if (!secretariaResponse.success ||
          secretariaResponse.results == null ||
          secretariaResponse.results!.isEmpty) {
        throw Exception('Perfil de secretária não encontrado');
      }

      currentSecretaria = secretariaResponse.results!.first;
      currentHospital = currentSecretaria!.get<ParseObject>('consultorio');

      if (currentHospital == null) {
        throw Exception('Hospital não encontrado');
      }

      // Se chegou aqui, tudo certo, vamos carregar os médicos
      await carregarMedicos();

      // Carregar mensagens predefinidas
      await carregarMensagensPredefinidas();
    } catch (e) {
      error.value = 'Erro ao carregar dados: $e';
      debugPrint('Erro ao carregar dados da secretária: $e');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> carregarMedicos() async {
    try {
      isLoading.value = true;
      error.value = '';
      
      // Ensure currentHospital is loaded
      if (currentHospital == null) {
        await carregarDadosSecretaria();
        if (currentHospital == null) {
          throw Exception('Consultório não encontrado');
        }
      }

      // Clear previous data to avoid duplications
      medicos.clear();
      
      // Get the doctors directly from the hospital's medicos_vinculados array
      final medicosVinculados = currentHospital!.get<List?>('medicos_vinculados');
      
      if (medicosVinculados == null || medicosVinculados.isEmpty) {
        debugPrint('Nenhum médico vinculado ao consultório');
        return;
      }
      
      // Extract doctor IDs from the array
      final medicosIds = <String>[];
      for (var medicoRef in medicosVinculados) {
        String? medicoId;
        if (medicoRef is Map<String, dynamic> && 
            medicoRef['__type'] == 'Pointer' && 
            medicoRef['className'] == 'Medico') {
          medicoId = medicoRef['objectId'];
        } else if (medicoRef is ParseObject) {
          medicoId = medicoRef.objectId;
        }
        
        if (medicoId != null && !medicosIds.contains(medicoId)) {
          medicosIds.add(medicoId);
        }
      }
      
      if (medicosIds.isEmpty) {
        debugPrint('Nenhum ID de médico pôde ser extraído');
        return;
      }
      
      // Log extracted IDs for debugging
      debugPrint('IDs de médicos extraídos: ${medicosIds.join(", ")}');
      
      // Use batch query to get all doctors at once
      final medicosQuery = QueryBuilder<ParseObject>(ParseObject('Medico'))
        ..whereContainedIn('objectId', medicosIds)
        ..whereEqualTo('ativo', true);
      
      final response = await medicosQuery.query();
      
      if (response.success && response.results != null) {
        // Fix: Cast the results list to a list of ParseObject
        final resultsList = response.results!.cast<ParseObject>();
        medicos.assignAll(resultsList);
        debugPrint('Carregados ${medicos.length} médicos vinculados');
      } else {
        debugPrint('Nenhum médico ativo encontrado para os IDs vinculados');
      }
    } catch (e) {
      error.value = 'Erro ao carregar médicos: $e';
      debugPrint('Erro ao carregar médicos: $e');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> carregarMensagensPredefinidas() async {
    if (currentHospital == null) return;

    try {
      final query =
          QueryBuilder<ParseObject>(ParseObject('MensagemPredefinida'))
            ..whereEqualTo('consultorio', currentHospital);

      final response = await query.query();

      if (response.success && response.results != null) {
        mensagensPredefinidas.value = response.results!.cast<ParseObject>();
      } else {
        mensagensPredefinidas.clear();
      }

      // Se não houver mensagens predefinidas, criar as padrões
      if (mensagensPredefinidas.isEmpty) {
        await _criarMensagensPadroes();
      }
    } catch (e) {
      debugPrint('Erro ao carregar mensagens predefinidas: $e');
    }
  }

  Future<void> _criarMensagensPadroes() async {
    if (currentHospital == null) return;

    try {
      for (final mensagem in mensagensPadroes) {
        final novaMensagem = ParseObject('MensagemPredefinida')
          ..set('titulo', mensagem['titulo'])
          ..set('texto', mensagem['texto'])
          ..set('prioridade', mensagem['prioridade'] ?? 'media')
          ..set('icone', mensagem['icone'] ?? 'info')
          ..set('consultorio', currentHospital);

        await novaMensagem.save();
      }

      // Recarregar as mensagens
      await carregarMensagensPredefinidas();
    } catch (e) {
      debugPrint('Erro ao criar mensagens padrões: $e');
    }
  }

  Future<bool> enviarMensagemParaFila(
      String medicoId, String titulo, String texto,
      {String prioridade = 'media', String icone = 'info'}) async {
    if (currentHospital == null) {
      error.value = 'Hospital não identificado';
      return false;
    }

    try {
      isLoading.value = true;
      error.value = '';

      // Verificar se a mesma mensagem foi enviada recentemente (últimos 30 segundos)
      final agora = DateTime.now();
      final mensagemIdentica = mensagensRecentes.firstWhereOrNull((m) =>
          m['medicoId'] == medicoId &&
          m['titulo'] == titulo &&
          m['texto'] == texto &&
          agora.difference(m['timestamp']).inSeconds < 30);

      if (mensagemIdentica != null) {
        error.value =
            'Esta mensagem foi enviada recentemente. Aguarde alguns instantes para enviar novamente.';
        debugPrint('Mensagem duplicada detectada: $titulo');
        return false;
      }

      // Verificar se o médico existe
      final medicoQuery = QueryBuilder<ParseObject>(ParseObject('Medico'))
        ..whereEqualTo('objectId', medicoId);

      final medicoResponse = await medicoQuery.query();
      if (!medicoResponse.success ||
          medicoResponse.results == null ||
          medicoResponse.results!.isEmpty) {
        error.value = 'Médico não encontrado';
        debugPrint('Médico não encontrado para envio de mensagem: $medicoId');
        return false;
      }

      final medico = medicoResponse.results!.first;
      debugPrint(
          'Enviando mensagem para fila do médico: ${medico.get<String>('nome')}');

      // Criar o objeto de mensagem com os campos corretos
      final mensagem = ParseObject('MensagemFila')
        ..set(
            'medico',
            ParseObject('Medico')
              ..objectId =
                  medicoId) // Corrigido: 'medico' em vez de 'medico_id'
        ..set('consultorio',
            currentHospital) // Corrigido: 'consultorio' em vez de 'consultorio_id'
        ..set('titulo', titulo)
        ..set('texto', texto) // Corrigido: 'texto' em vez de 'mensagem'
        ..set('prioridade', prioridade)
        ..set('icone', icone)
        ..set('data_envio', DateTime.now()); // Adicionado: campo data_envio

      final result = await mensagem.save();

      if (!result.success) {
        throw Exception(result.error?.message ?? 'Erro ao enviar mensagem');
      }

      // Verificar se há pacientes na fila para receber a mensagem
      final queryFila = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('medico', ParseObject('Medico')..objectId = medicoId)
        ..whereEqualTo('consultorio', currentHospital)
        ..whereContainedIn('status', ['aguardando', 'em_atendimento']);

      final filaResponse = await queryFila.query();
      if (filaResponse.success && filaResponse.results != null) {
        debugPrint(
            'Mensagem enviada para ${filaResponse.results!.length} pacientes na fila');
      } else {
        debugPrint('Não há pacientes na fila para receber a mensagem');
      }

      // Notificar pacientes (via Push Notification ou Canal de Mensagens)
      // Esta parte depende da implementação específica de notificações do seu app
      try {
        final cloudParams = {
          'medicoId': medicoId,
          'consultorioId': currentHospital!.objectId,
          'titulo': titulo,
          'texto': texto,
          'prioridade': prioridade
        };

        await ParseCloudFunction('enviarNotificacoesFilaPacientes')
            .execute(parameters: cloudParams);
        debugPrint('Função cloud de notificação acionada com sucesso');
      } catch (notificationError) {
        debugPrint(
            'Aviso: Erro ao enviar notificações push: $notificationError');
        // Não falhar a função inteira se apenas as notificações falharem
      }

      // Registrar mensagem como enviada recentemente
      mensagensRecentes.add({
        'medicoId': medicoId,
        'titulo': titulo,
        'texto': texto,
        'timestamp': agora
      });

      // Limpar mensagens antigas (mais de 5 minutos)
      mensagensRecentes
          .removeWhere((m) => agora.difference(m['timestamp']).inMinutes > 5);

      debugPrint('Mensagem para fila registrada com sucesso: $titulo');
      return true;
    } catch (e) {
      error.value = e.toString().replaceAll('Exception: ', '');
      debugPrint('Erro ao enviar mensagem: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Obter o ícone correspondente para uma mensagem predefinida
  String getIconeParaMensagem(String titulo) {
    final mensagemPadrao =
        mensagensPadroes.firstWhereOrNull((m) => m['titulo'] == titulo);
    return mensagemPadrao?['icone'] ?? 'info';
  }

  // Obter a prioridade correspondente para uma mensagem predefinida
  String getPrioridadeParaMensagem(String titulo) {
    final mensagemPadrao =
        mensagensPadroes.firstWhereOrNull((m) => m['titulo'] == titulo);
    return mensagemPadrao?['prioridade'] ?? 'media';
  }

  Future<List<ParseObject>> obterFilaPorMedico(String medicoId) async {
    try {
      if (currentHospital == null) return [];

      debugPrint("===== INICIANDO BUSCA DE FILA PARA MÉDICO =====");
      debugPrint("Médico ID: $medicoId");
      debugPrint("Hospital ID: ${currentHospital!.objectId}");
      
      // Verificar se o médico está vinculado ao hospital atual antes de fazer requisições
      if (!medicoEstaVinculadoAoHospitalAtual(medicoId)) {
        debugPrint("AVISO: Médico com ID $medicoId não está vinculado ao hospital atual. Evitando requisições desnecessárias.");
        return [];
      }

      // Primeiro, verificamos se o médico existe
      final medicoQuery = QueryBuilder<ParseObject>(ParseObject('Medico'))
        ..whereEqualTo('objectId', medicoId);

      final medicoResponse = await medicoQuery.query();

      if (!medicoResponse.success ||
          medicoResponse.results == null ||
          medicoResponse.results!.isEmpty) {
        debugPrint("ERRO: Médico não encontrado com ID: $medicoId");
        return [];
      }

      final medico = medicoResponse.results!.first;
      final bool isAtivo = medico.get<bool>('ativo') ?? false;
      
      debugPrint(
          "Médico encontrado: ${medico.get<String>('nome')}, Ativo: $isAtivo");
          
      // Se o médico não estiver ativo, não buscar sua fila
      if (!isAtivo) {
        debugPrint("AVISO: Médico com ID $medicoId não está ativo. Evitando requisições desnecessárias.");
        return [];
      }

      // Garantir que não haja problemas de permissão nas filas
      await _garantirPermissoesFilas();

      List<ParseObject> resultadosFila = [];
      bool sucessoNaBusca = false;

      // TENTATIVA 1: Usar a abordagem direta
      try {
        debugPrint("TENTATIVA 1: Usando query direta");

        final queryFila1 = QueryBuilder<ParseObject>(ParseObject('Fila'))
          ..whereEqualTo('medico', ParseObject('Medico')..objectId = medicoId)
          ..whereEqualTo('consultorio', currentHospital)
          ..whereContainedIn('status', ['aguardando', 'em_atendimento'])
          ..orderByAscending('posicao')
          ..setLimit(100);

        final response1 = await queryFila1.query();

        if (response1.success && response1.results != null) {
          resultadosFila = response1.results!.cast<ParseObject>();
          debugPrint(
              "TENTATIVA 1 SUCESSO: Encontrados ${resultadosFila.length} pacientes");

          // Detalhar pacientes encontrados
          for (var paciente in resultadosFila) {
            final nome = paciente.get<String>('nome') ?? 'Sem nome';
            final status = paciente.get<String>('status') ?? 'desconhecido';
            final posicao = paciente.get<int>('posicao') ?? 0;
            final idPaciente = paciente.get<String>('idPaciente') ?? 'sem ID';
            debugPrint(
                "  - Paciente: $nome (ID: $idPaciente), Status: $status, Posição: $posicao");
          }

          sucessoNaBusca = true;
        } else {
          debugPrint(
              "TENTATIVA 1 FALHA: ${response1.error?.message ?? 'Nenhum paciente encontrado'}");
        }
      } catch (e) {
        debugPrint("ERRO TENTATIVA 1: $e");
      }

      // Se a primeira tentativa falhou, tentar abordagem diferente
      if (!sucessoNaBusca) {
        try {
          debugPrint("TENTATIVA 2: Busca por Cloud Function");

          // Parâmetros para a Cloud Function
          final params = {
            'medicoId': medicoId,
            'consultorioId': currentHospital!.objectId,
          };

          // Chamar a Cloud Function para buscar pacientes na fila
          final response2 = await ParseCloudFunction('obterFilaPorMedicoCloud')
              .execute(parameters: params);

          if (response2.success && response2.result != null) {
            final List<dynamic> resultados =
                response2.result['pacientes'] ?? [];

            resultadosFila = [];
            debugPrint(
                "TENTATIVA 2 SUCESSO: Encontrados ${resultados.length} pacientes via Cloud Function");

            // Converter resultados para ParseObject
            for (var resultado in resultados) {
              try {
                final paciente = ParseObject('Fila')..fromJson(resultado);
                resultadosFila.add(paciente);

                final nome = paciente.get<String>('nome') ?? 'Sem nome';
                final status = paciente.get<String>('status') ?? 'desconhecido';
                final posicao = paciente.get<int>('posicao') ?? 0;
                debugPrint(
                    "  - Paciente: $nome, Status: $status, Posição: $posicao");
              } catch (e) {
                debugPrint("Erro ao converter resultado: $e");
              }
            }

            // Ordenar os resultados por posição
            resultadosFila.sort((a, b) {
              final posA = a.get<int>('posicao') ?? 999;
              final posB = b.get<int>('posicao') ?? 999;
              return posA.compareTo(posB);
            });

            sucessoNaBusca = true;
          } else {
            debugPrint(
                "TENTATIVA 2 FALHA: ${response2.error?.message ?? 'Erro na Cloud Function'}");
          }
        } catch (e) {
          debugPrint("ERRO TENTATIVA 2: $e");
        }
      }

      // Se nenhuma tentativa funcionou, usar a busca manual completa
      if (!sucessoNaBusca) {
        try {
          debugPrint("TENTATIVA 3: Busca completa manual");

          // Buscar todas as filas ativas
          final queryFila3 = QueryBuilder<ParseObject>(ParseObject('Fila'))
            ..whereContainedIn('status', ['aguardando', 'em_atendimento'])
            ..setLimit(1000);

          final response3 = await queryFila3.query();

          if (response3.success && response3.results != null) {
            final allFilas = response3.results!.cast<ParseObject>();
            debugPrint(
                "TENTATIVA 3: Encontradas ${allFilas.length} filas ativas no total");

            // Filtrar manualmente
            resultadosFila = allFilas.where((fila) {
              // Verificar médico
              final filaMedico = fila.get<ParseObject>('medico');
              final medicoCerto =
                  filaMedico != null && filaMedico.objectId == medicoId;

              // Verificar consultório
              final filaConsultorio = fila.get<ParseObject>('consultorio');
              final consultorioCerto = filaConsultorio != null &&
                  filaConsultorio.objectId == currentHospital!.objectId;

              return medicoCerto && consultorioCerto;
            }).toList();

            debugPrint(
                "TENTATIVA 3 SUCESSO: Após filtro, encontrados ${resultadosFila.length} pacientes");

            // Ordenar por posição
            resultadosFila.sort((a, b) {
              final posA = a.get<int>('posicao') ?? 999;
              final posB = b.get<int>('posicao') ?? 999;
              return posA.compareTo(posB);
            });

            // Detalhar pacientes encontrados
            for (var paciente in resultadosFila) {
              final nome = paciente.get<String>('nome') ?? 'Sem nome';
              final status = paciente.get<String>('status') ?? 'desconhecido';
              final posicao = paciente.get<int>('posicao') ?? 0;
              debugPrint(
                  "  - Paciente: $nome, Status: $status, Posição: $posicao");
            }
          } else {
            debugPrint(
                "TENTATIVA 3 FALHA: ${response3.error?.message ?? 'Erro na busca completa'}");
          }
        } catch (e) {
          debugPrint("ERRO TENTATIVA 3: $e");
        }
      }

      debugPrint("===== BUSCA DE FILA FINALIZADA =====");
      debugPrint("Total de pacientes encontrados: ${resultadosFila.length}");

      return resultadosFila;
    } catch (e) {
      debugPrint('Erro crítico ao obter fila por médico: $e');
      return [];
    }
  }

  // Método privado para garantir permissões às filas
  Future<void> _garantirPermissoesFilas() async {
    try {
      debugPrint("Garantindo permissões de acesso às filas");

      // Chamar Cloud Function para garantir permissões
      final resultado = await ParseCloudFunction('garantirPermissoesFilas')
          .execute(parameters: {'consultorioId': currentHospital!.objectId});

      if (resultado.success) {
        debugPrint(
            "Permissões de filas atualizadas: ${resultado.result['message'] ?? 'sucesso'}");
      } else {
        debugPrint("Erro ao atualizar permissões: ${resultado.error?.message}");
      }
    } catch (e) {
      debugPrint("Erro ao garantir permissões: $e");
      // Ignoramos erro para não interromper fluxo principal
    }
  }

  Future<Map<String, dynamic>> obterMetricasMedico(String medicoId) async {
    try {
      if (currentHospital == null) return {};
      
      // Verificar se o médico está vinculado ao hospital atual antes de fazer requisições
      if (!medicoEstaVinculadoAoHospitalAtual(medicoId)) {
        debugPrint("Médico com ID $medicoId não está vinculado ao hospital atual. Evitando requisições desnecessárias.");
        return {
          'tempoMedioEspera': 0,
          'tempoMedioAtendimento': 0,
          'totalPacientes': 0,
          'pacientesAtendidos': 0,
        };
      }

      // Buscar todos os pacientes da fila do médico para calcular métricas
      final queryFila = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('medico', ParseObject('Medico')..objectId = medicoId)
        ..whereEqualTo('consultorio', currentHospital);

      final response = await queryFila.query();

      if (response.success && response.results != null) {
        final pacientes = response.results!;

        // Contadores iniciais
        int totalPacientes = pacientes.length;
        int pacientesAtendidos = 0;
        double somaTempoEspera = 0.0; // Alterado para double
        double somaTempoAtendimento = 0.0; // Alterado para double
        int contadorTempoEspera = 0;
        int contadorTempoAtendimento = 0;

        // Processar cada paciente para calcular as métricas
        for (var paciente in pacientes) {
          final status = paciente.get<String>('status');

          // Contar pacientes com status 'atendido'
          if (status == 'atendido') {
            pacientesAtendidos++;

            // Calcular tempos de espera e atendimento
            final dataEntrada = paciente.get<DateTime>('data_entrada');
            final dataInicio =
                paciente.get<DateTime>('data_inicio_atendimento');
            final dataFim = paciente.get<DateTime>('data_fim_atendimento');

            if (dataEntrada != null && dataInicio != null) {
              final diffEspera = dataInicio.difference(dataEntrada).inMinutes;
              if (diffEspera > 0) {
                somaTempoEspera +=
                    diffEspera.toDouble(); // Convertido para double
                contadorTempoEspera++;
              }
            }

            if (dataInicio != null && dataFim != null) {
              final diffAtendimento =
                  dataFim.difference(dataInicio).inMinutes;
              if (diffAtendimento > 0) {
                somaTempoAtendimento += diffAtendimento.toDouble();
                contadorTempoAtendimento++;
              }
            }
          }
        }

        // Calcular médias
        int tempoMedioEspera = 0;
        int tempoMedioAtendimento = 0;

        if (contadorTempoEspera > 0) {
          tempoMedioEspera = (somaTempoEspera / contadorTempoEspera).floor();
        }

        if (contadorTempoAtendimento > 0) {
          tempoMedioAtendimento =
              (somaTempoAtendimento / contadorTempoAtendimento).floor();
        }

        // Logging para debug
        debugPrint('Métricas calculadas diretamente da classe Fila:');
        debugPrint('Total pacientes: $totalPacientes');
        debugPrint('Pacientes atendidos: $pacientesAtendidos');
        debugPrint('Tempo médio de espera: $tempoMedioEspera min');
        debugPrint('Tempo médio de atendimento: $tempoMedioAtendimento min');

        return {
          'tempoMedioEspera': tempoMedioEspera,
          'tempoMedioAtendimento': tempoMedioAtendimento,
          'totalPacientes': totalPacientes,
          'pacientesAtendidos': pacientesAtendidos,
        };
      }

      return {
        'tempoMedioEspera': 0,
        'tempoMedioAtendimento': 0,
        'totalPacientes': 0,
        'pacientesAtendidos': 0,
      };
    } catch (e) {
      debugPrint('Erro ao obter métricas: $e');
      return {
        'tempoMedioEspera': 0,
        'tempoMedioAtendimento': 0,
        'totalPacientes': 0,
        'pacientesAtendidos': 0,
      };
    }
  }

  Future<bool> enviarMensagemFila(String medicoId, String mensagem,
      [String? tipo]) async {
    try {
      if (currentHospital == null || currentSecretaria == null) return false;

      final novaMensagem = ParseObject('MensagemFila')
        ..set('mensagem', mensagem)
        ..set('tipo', tipo ?? 'personalizada')
        ..set('medico_id', ParseObject('Medico')..objectId = medicoId)
        ..set('consultorio_id', currentHospital)
        ..set('secretaria_id', currentSecretaria)
        ..set('data_envio', DateTime.now())
        ..set('ativa', true);

      final response = await novaMensagem.save();

      return response.success;
    } catch (e) {
      debugPrint('Erro ao enviar mensagem: $e');
      return false;
    }
  }

  Future<bool> reordenarFila(
      String medicoId, List<ParseObject> novaOrdem) async {
    try {
      isLoading.value = true;

      // Atualizar as posições no banco
      for (int i = 0; i < novaOrdem.length; i++) {
        final paciente = novaOrdem[i];
        paciente.set('posicao', i + 1);
        await paciente.save();
      }

      return true;
    } catch (e) {
      debugPrint('Erro ao reordenar fila: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  Future<bool> iniciarAtendimento(ParseObject paciente) async {
    try {
      paciente
        ..set('status', 'em_atendimento')
        ..set('data_inicio_atendimento', DateTime.now());

      final response = await paciente.save();
      return response.success;
    } catch (e) {
      debugPrint('Erro ao iniciar atendimento: $e');
      return false;
    }
  }

  Future<bool> finalizarAtendimento(ParseObject paciente) async {
    try {
      paciente
        ..set('status', 'atendido')
        ..set('data_fim_atendimento', DateTime.now());

      final response = await paciente.save();
      return response.success;
    } catch (e) {
      debugPrint('Erro ao finalizar atendimento: $e');
      return false;
    }
  }

  Future<bool> removerPaciente(ParseObject paciente) async {
    try {
      // Registrar saída
      final filaSaida = ParseObject('FilaSaida')
        ..set('fila_id', paciente)
        ..set('motivo_saida', 'removido_secretaria')
        ..set('created_at', DateTime.now());

      await filaSaida.save();

      // Atualizar status
      paciente.set('status', 'removido');
      paciente.set('data_saida', DateTime.now());
      final response = await paciente.save();

      // Reajustar posições dos pacientes na fila
      if (response.success) {
        final posicaoRemovida = paciente.get<int>('posicao') ?? 0;
        final medicoId = paciente.get<ParseObject>('medico')?.objectId;

        if (medicoId != null && posicaoRemovida > 0) {
          final queryPacientes = QueryBuilder<ParseObject>(ParseObject('Fila'))
            ..whereEqualTo('medico', ParseObject('Medico')..objectId = medicoId)
            ..whereEqualTo('consultorio', currentHospital)
            ..whereEqualTo('status', 'aguardando')
            ..whereGreaterThan('posicao', posicaoRemovida)
            ..orderByAscending('posicao');

          final pacientesResponse = await queryPacientes.query();

          if (pacientesResponse.success && pacientesResponse.results != null) {
            for (var pacient in pacientesResponse.results!) {
              final posicaoAtual = pacient.get<int>('posicao') ?? 0;
              pacient.set('posicao', posicaoAtual - 1);
              await pacient.save();
            }
          }
        }
      }

      return response.success;
    } catch (e) {
      debugPrint('Erro ao remover paciente: $e');
      return false;
    }
  }

  Future<void> carregarPacientesAtendidos(String medicoId,
      {int limit = 20, int skip = 0}) async {
    try {
      isLoading.value = true;

      final hoje = DateTime.now();
      final inicioDoDia = DateTime(hoje.year, hoje.month, hoje.day);

      final queryAtendidos = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('medico', ParseObject('Medico')..objectId = medicoId)
        ..whereEqualTo('consultorio', currentHospital)
        ..whereEqualTo('status', 'atendido')
        ..whereGreaterThanOrEqualsTo('data_entrada', inicioDoDia)
        ..orderByDescending('data_fim_atendimento');
      queryAtendidos.setLimit(limit); // Define o limite de resultados
      queryAtendidos
          .setAmountToSkip(skip); // Define o número de resultados a pular

      final response = await queryAtendidos.query();

      // Agregar novos resultados ou substituir dependendo do skip
      if (skip == 0) {
        pacientesAtendidos.value = response.results?.cast<ParseObject>() ?? [];
      } else {
        pacientesAtendidos.addAll(response.results?.cast<ParseObject>() ?? []);
      }

      // Verificar se há mais resultados
      hasMorePacientesAtendidos.value =
          (response.results?.length ?? 0) >= limit;
    } catch (e) {
      debugPrint('Erro ao carregar pacientes atendidos: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Método para reenviar credenciais de uma secretária
  Future<Map<String, dynamic>> reenviarCredenciais(String secretariaId) async {
    try {
      isLoading.value = true;
      error.value = '';

      debugPrint(
          'Iniciando reenvio de credenciais para secretária ID: $secretariaId');

      // Chamar a função Cloud para gerar o link de redefinição
      final params = <String, dynamic>{
        'secretariaId': secretariaId,
      };

      final resetResponse =
          await ParseCloudFunction('generateResetLinkForSecretaria')
              .execute(parameters: params);

      if (resetResponse.success) {
        final result = resetResponse.result as Map<String, dynamic>;

        if (result['success'] == true) {
          // Extrair detalhes do resultado
          final email = result['email'] as String?;
          final resetLink = result['resetLink'] as String?;

          return {
            'success': true,
            'message': 'Link de redefinição de senha enviado com sucesso.',
            'email': email,
            'resetLink': resetLink,
            'details': 'O link é válido por 24 horas.'
          };
        } else {
          throw Exception(
              result['message'] ?? 'Erro ao gerar link de redefinição');
        }
      } else {
        throw Exception(
          'Erro ao chamar função de redefinição: ${resetResponse.error?.message}',
        );
      }
    } catch (e) {
      error.value = e.toString().replaceAll('Exception: ', '');
      debugPrint('Erro ao reenviar credenciais: $e');
      return {
        'success': false,
        'message': 'Falha ao gerar link de redefinição.',
        'error': e.toString()
      };
    } finally {
      isLoading.value = false;
    }
  }

  // Método para verificar e reparar pacientes nas filas
  Future<Map<String, dynamic>> verificarERepararFilas(String medicoId) async {
    try {
      if (currentHospital == null) {
        return {'success': false, 'error': 'Hospital não encontrado'};
      }

      debugPrint(
          "Verificando e reparando filas para o médico ID: $medicoId no hospital: ${currentHospital!.objectId}");

      // Primeiro, corrigir inconsistências em em_fila dos usuários
      await corrigirStatusEmFilaUsuarios();

      // Depois, chamar a Cloud Function para verificar e reparar a consistência das filas
      final params = <String, dynamic>{
        'medicoId': medicoId,
        'consultorioId': currentHospital!.objectId,
      };

      final response = await ParseCloudFunction('verificarERepararFilas')
          .execute(parameters: params);

      if (response.success) {
        final resultado = response.result as Map<String, dynamic>? ?? {};
        debugPrint("Filas verificadas com sucesso: ${resultado['message']}");
        debugPrint("Pacientes encontrados: ${resultado['count']}");
        return {
          'success': true,
          'count': resultado['count'] ?? 0,
          'message': resultado['message'] ?? 'Filas verificadas',
        };
      } else {
        debugPrint("Erro ao verificar filas: ${response.error?.message}");
        return {
          'success': false,
          'error': response.error?.message ?? 'Erro desconhecido',
        };
      }
    } catch (e) {
      debugPrint("Erro ao verificar e reparar filas: $e");
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  // Método para adicionar um novo paciente à fila
  Future<Map<String, dynamic>> adicionarPacienteAFila({
    required String nome,
    required String telefone,
    String idPaciente = '', // Opcional, para usuários não cadastrados
    required int posicao,
    required String medicoId,
  }) async {
    try {
      if (currentHospital == null) {
        return {'success': false, 'error': 'Hospital não encontrado'};
      }

      debugPrint('Adicionando paciente $nome à fila do médico $medicoId');

      // Criar objetos parse para médico e consultório
      final medico = ParseObject('Medico')..objectId = medicoId;

      // Criar a fila com os parâmetros corretos
      final fila = await Fila.createFila(
        nome: nome,
        telefone: telefone,
        idPaciente: idPaciente,
        posicao: posicao,
        medico: medico,
        consultorio: currentHospital!,
      );

      // Salvar a fila criada
      final response = await fila.save();

      if (response.success) {
        debugPrint('Paciente adicionado com sucesso: ${fila.objectId}');
        return {
          'success': true,
          'message': 'Paciente adicionado com sucesso',
          'filaId': fila.objectId,
        };
      } else {
        throw Exception('Erro ao salvar fila: ${response.error?.message}');
      }
    } catch (e) {
      debugPrint('Erro ao adicionar paciente à fila: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  // Método para corrigir inconsistências em em_fila dos usuários
  Future<Map<String, dynamic>> corrigirStatusEmFilaUsuarios() async {
    try {
      if (currentHospital == null) {
        return {'success': false, 'error': 'Hospital não encontrado'};
      }

      debugPrint("Iniciando correção de status em_fila de usuários");
      int corrigidos = 0;

      // 1. Encontrar todos os usuários com em_fila = true
      final queryUsuariosEmFila =
          QueryBuilder<ParseObject>(ParseObject('Usuario'))
            ..whereEqualTo('em_fila', true);

      final usuariosEmFilaResponse = await queryUsuariosEmFila.query();

      if (usuariosEmFilaResponse.success &&
          usuariosEmFilaResponse.results != null &&
          usuariosEmFilaResponse.results!.isNotEmpty) {
        final usuariosEmFila =
            usuariosEmFilaResponse.results!.cast<ParseObject>();
        debugPrint(
            "Encontrados ${usuariosEmFila.length} usuários com em_fila = true");

        // Para cada usuário com status em_fila = true, verificar se realmente está em uma fila
        for (var usuario in usuariosEmFila) {
          final deviceId = usuario.get<String>('deviceId');

          if (deviceId == null || deviceId.isEmpty) {
            debugPrint("Usuário sem deviceId: ${usuario.objectId}");
            continue;
          }

          // Verificar se o usuário realmente está em alguma fila
          final queryFila = QueryBuilder<ParseObject>(ParseObject('Fila'))
            ..whereEqualTo('idPaciente', deviceId)
            ..whereContainedIn('status', ['aguardando', 'em_atendimento']);

          final filaResponse = await queryFila.query();

          // Se não encontrar nenhuma fila ativa para o usuário, corrigir o status
          if (!filaResponse.success ||
              filaResponse.results == null ||
              filaResponse.results!.isEmpty) {
            debugPrint(
                "Usuário $deviceId não está em nenhuma fila ativa, corrigindo status");

            usuario.set('em_fila', false);
            usuario.set('ultima_fila', DateTime.now());

            // Configurar ACL pública
            final acl = ParseACL();
            acl.setPublicReadAccess(allowed: true);
            acl.setPublicWriteAccess(allowed: true);
            usuario.setACL(acl);

            final saveResponse = await usuario.save();

            if (saveResponse.success) {
              corrigidos++;
              debugPrint("Status do usuário corrigido com sucesso");
            } else {
              debugPrint(
                  "Erro ao corrigir status: ${saveResponse.error?.message}");
            }
          } else {
            debugPrint(
                "Usuário $deviceId está em uma fila ativa, status correto");
          }
        }
      } else {
        debugPrint("Nenhum usuário com status em_fila = true encontrado");
      }

      // 2. Verificar usuários que deveriam estar em fila mas estão com em_fila = false
      final queryFilasAtivas = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereContainedIn('status', ['aguardando', 'em_atendimento']);

      final filasAtivasResponse = await queryFilasAtivas.query();

      if (filasAtivasResponse.success &&
          filasAtivasResponse.results != null &&
          filasAtivasResponse.results!.isNotEmpty) {
        final filasAtivas = filasAtivasResponse.results!.cast<ParseObject>();
        debugPrint("Encontradas ${filasAtivas.length} filas ativas");

        // Para cada fila ativa, verificar se o usuário está com em_fila = true
        for (var fila in filasAtivas) {
          final idPaciente = fila.get<String>('idPaciente');

          if (idPaciente == null || idPaciente.isEmpty) {
            debugPrint("Fila sem idPaciente: ${fila.objectId}");
            continue;
          }

          // Verificar status do usuário
          final queryUsuario = QueryBuilder<ParseObject>(ParseObject('Usuario'))
            ..whereEqualTo('deviceId', idPaciente);

          final usuarioResponse = await queryUsuario.query();

          if (usuarioResponse.success &&
              usuarioResponse.results != null &&
              usuarioResponse.results!.isNotEmpty) {
            final usuario = usuarioResponse.results!.first;
            final emFila = usuario.get<bool>('em_fila') ?? false;

            if (!emFila) {
              debugPrint(
                  "Usuário $idPaciente está em fila mas com em_fila = false, corrigindo");

              usuario.set('em_fila', true);

              // Configurar ACL pública
              final acl = ParseACL();
              acl.setPublicReadAccess(allowed: true);
              acl.setPublicWriteAccess(allowed: true);
              usuario.setACL(acl);

              final saveResponse = await usuario.save();

              if (saveResponse.success) {
                corrigidos++;
                debugPrint("Status do usuário corrigido para em_fila = true");
              } else {
                debugPrint(
                    "Erro ao corrigir status: ${saveResponse.error?.message}");
              }
            }
          }
        }
      }

      return {
        'success': true,
        'count': corrigidos,
        'message':
            'Status em_fila verificado e corrigido para $corrigidos usuários',
      };
    } catch (e) {
      debugPrint("Erro ao corrigir status em_fila dos usuários: $e");
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  // Método para abrir WhatsApp
  Future<bool> abrirWhatsAppSecretaria(String telefone) async {
    try {
      final sucesso = await WhatsAppUtils.abrirWhatsApp(telefone);
      if (!sucesso) {
        error.value = 'Não foi possível abrir o WhatsApp';
        return false;
      }
      return true;
    } catch (e) {
      error.value = 'Erro ao abrir WhatsApp: $e';
      debugPrint('Erro ao abrir WhatsApp: $e');
      return false;
    }
  }

  // Verificar se um médico está vinculado ao hospital atual
  bool medicoEstaVinculadoAoHospitalAtual(String medicoId) {
    try {
      if (currentHospital == null) return false;

      // Verificar se o médico está na lista de médicos vinculados ao hospital
      final medicosVinculados = currentHospital!.get<List>('medicos_vinculados');
      if (medicosVinculados != null) {
        for (var medico in medicosVinculados) {
          if (medico is ParseObject && medico.objectId == medicoId) {
            return true;
          } else if (medico is String && medico == medicoId) {
            return true;
          }
        }
      }

      // Se não encontrou nos médicos vinculados, o médico não está vinculado
      return false;
    } catch (e) {
      debugPrint('Erro ao verificar vínculo do médico: $e');
      return false;
    }
  }
}
