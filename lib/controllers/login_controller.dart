import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import '../services/push_notification_service.dart';
import '../controllers/user_data_controller.dart';

class LoginController extends GetxController {
  // Observables
  final _isLoading = false.obs;
  final _obscureText = true.obs;
  final _emailError = RxString('');
  final _passwordError = RxString('');

  // Getters
  bool get isLoading => _isLoading.value;
  bool get obscureText => _obscureText.value;
  String get emailError => _emailError.value;
  String get passwordError => _passwordError.value;

  // Methods
  void togglePasswordVisibility() => _obscureText.value = !_obscureText.value;

  void clearErrors() {
    _emailError.value = '';
    _passwordError.value = '';
  }

  Future<void> handleLogin(String email, String password) async {
    if (!_validateInputs(email, password)) return;

    _isLoading.value = true;

    try {
      final user = ParseUser(email, password, email);
      final response = await user.login();

      if (response.success) {
        final userType = response.results?.first.get<String>('tipo');

        if (userType == 'medico') {
          await _handleMedicoLogin(user);
        } else if (userType == 'consultorio') {
          await _handleConsultorioLogin(user);
        } else if (userType == 'secretaria') {
          await _handleSecretariaLogin(user);
        } else if (userType == 'admin') {
          await _handleAdminLogin(user);
        } else {
          throw Exception('Tipo de usuário inválido');
        }

        // Registrar para notificações push após login bem-sucedido
        await _registerForPushNotifications();
      } else {
        throw Exception(response.error?.message ?? 'Erro ao fazer login');
      }
    } catch (e) {
      _handleError(e.toString());
    } finally {
      _isLoading.value = false;
    }
  }

  // Método melhorado para registrar notificações após login
  Future<void> _registerForPushNotifications() async {
    try {
      print('🔔 Iniciando registro para notificações após login bem-sucedido');

      // Pequeno atraso para garantir que a navegação já ocorreu
      await Future.delayed(const Duration(milliseconds: 500));

      // Atualizar dados do usuário antes do registro
      final userDataController = Get.find<UserDataController>();
      await userDataController.checkUserData();

      final userData = await userDataController.getUserData();
      if (userData == null) {
        print(
            '⚠️ Dados do usuário não disponíveis para registro de notificações');
        return;
      }

      print('👤 Registrando notificações para usuário: ${userData['userId']}');
      // Registrar para notificações push com sistema melhorado
      final pushService = Get.find<PushNotificationService>();

      // Não é necessário verificar permissões explicitamente - isso é feito dentro do registerAfterLogin
      // await pushService.checkAndRequestPermissions();

      // Registra o dispositivo com retry pattern incorporado
      await pushService.registerAfterLogin();

      // Monitorar status de registro
      bool registroCompletado = false;
      int tentativas = 0;

      while (!registroCompletado && tentativas < 3) {
        tentativas++;
        await Future.delayed(const Duration(seconds: 1));

        // Verificar se o registro foi concluído
        if (pushService.isRegistered.value) {
          registroCompletado = true;
          print(
              '✅ Registro de notificações confirmado na tentativa $tentativas');

          // Se estiver em produção, enviar diagnóstico automaticamente
          if (tentativas > 1) {
            try {
              // Enviar relatório discretamente em caso de dificuldades no registro
              await pushService.sendDiagnosticReport();
            } catch (e) {
              print('⚠️ Erro ao enviar diagnóstico: $e');
            }
          }
        } else if (tentativas == 3) {
          print(
              '⚠️ Registro de notificações não confirmado após $tentativas tentativas');
        }
      }

      print('✅ Registro para notificações concluído');
    } catch (e) {
      print('❌ Erro ao registrar para notificações push: $e');
      // Não interromper o fluxo de login em caso de falha no registro de notificações
    }
  }

  Future<void> _handleMedicoLogin(ParseUser user) async {
    final queryMedico = QueryBuilder<ParseObject>(ParseObject('Medico'))
      ..whereEqualTo('user_medico', user.toPointer());

    final medicoResponse = await queryMedico.query();

    if (!medicoResponse.success ||
        medicoResponse.results == null ||
        medicoResponse.results!.isEmpty) {
      throw Exception('Perfil médico não encontrado');
    }

    final medico = medicoResponse.results!.first;
    final ativo = medico.get<bool>('ativo') ?? false;

    if (!ativo) {
      throw Exception('Conta desativada. Entre em contato com o suporte.');
    }

    Get.offAllNamed('/medico');
  }

  Future<void> _handleConsultorioLogin(ParseUser user) async {
    final queryConsultorio =
        QueryBuilder<ParseObject>(ParseObject('consultorio'))
          ..whereEqualTo('user_consultorio', user.toPointer());

    final consultorioResponse = await queryConsultorio.query();

    if (!consultorioResponse.success ||
        consultorioResponse.results == null ||
        consultorioResponse.results!.isEmpty) {
      throw Exception('Perfil de consultório não encontrado');
    }

    Get.offAllNamed('/hospital');
  }

  Future<void> _handleSecretariaLogin(ParseUser user) async {
    final querySecretaria = QueryBuilder<ParseObject>(ParseObject('Secretaria'))
      ..whereEqualTo('user_secretaria', user.toPointer());

    final secretariaResponse = await querySecretaria.query();

    if (!secretariaResponse.success ||
        secretariaResponse.results == null ||
        secretariaResponse.results!.isEmpty) {
      throw Exception('Perfil de secretária não encontrado');
    }

    final secretaria = secretariaResponse.results!.first;
    final ativo = secretaria.get<bool>('ativo') ?? false;

    if (!ativo) {
      throw Exception('Conta desativada. Entre em contato com o suporte.');
    }

    Get.offAllNamed('/home_secretaria');
  }

  Future<void> _handleAdminLogin(ParseUser user) async {
    // Verificar se o usuário tem permissões de administrador
    final isAdmin = user.get<bool>('isAdmin') ?? false;

    if (!isAdmin) {
      throw Exception('Este usuário não possui permissões de administrador');
    }

    Get.offAllNamed('/admin_hospitais');
  }

  bool _validateInputs(String email, String password) {
    clearErrors();
    bool isValid = true;

    if (email.isEmpty) {
      _emailError.value = 'Por favor, insira seu e-mail';
      isValid = false;
    } else if (!GetUtils.isEmail(email)) {
      _emailError.value = 'Por favor, insira um e-mail válido';
      isValid = false;
    }

    if (password.isEmpty) {
      _passwordError.value = 'Por favor, insira sua senha';
      isValid = false;
    }

    return isValid;
  }

  void _handleError(String error) {
    Get.snackbar(
      'Erro',
      error.replaceAll('Exception:', '').trim(),
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Get.theme.colorScheme.error,
      colorText: Get.theme.colorScheme.onError,
      duration: const Duration(seconds: 3),
    );
  }

  Future<void> logout() async {
    try {
      final user = await ParseUser.currentUser() as ParseUser?;
      if (user != null) {
        // Limpar dados de notificação antes do logout
        try {
          final pushService = Get.find<PushNotificationService>();
          await pushService.clearNotificationData();
          print('✅ Dados de notificação limpos com sucesso');
        } catch (notifError) {
          print('❌ Erro ao limpar dados de notificação: $notifError');
        }

        // Executar logout
        await user.logout();
        debugPrint('✅ Logout realizado com sucesso');

        // Atualizar UserDataController após logout
        try {
          final userDataController = Get.find<UserDataController>();
          await userDataController.clearUserData();
          await userDataController.checkUserData();
        } catch (e) {
          print('❌ Erro ao atualizar dados de usuário após logout: $e');
        }
      } else {
        debugPrint('⚠️ Nenhum usuário logado para fazer logout');
      }
    } catch (e) {
      debugPrint('❌ Erro ao fazer logout: $e');
    }
  }
}
