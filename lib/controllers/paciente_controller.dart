import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:fila_app/controllers/user_data_controller.dart';
import '../models/fila.dart'; // Importe o modelo Fila

class PacienteController extends GetxController {
  final RxBool isLoading = false.obs;
  final RxBool showScanner = false.obs;
  final RxString errorMessage = ''.obs;
  final RxBool emFila = false.obs;
  final Rx<Map<String, dynamic>?> filaAtual = Rx<Map<String, dynamic>?>(null);
  final RxString _userId = ''.obs;

  String get userId => _userId.value;

  bool isProcessing = false; // Sem .obs se não for usado para reativar a UI
  MobileScannerController? scannerController;
  late UserDataController userDataController;

  get isScanning => null;

  @override
  void onInit() {
    super.onInit();
    // Inicializando o UserDataController
    userDataController = Get.put(UserDataController());
    _carregarDadosUsuario();
  }

  Future<void> _carregarDadosUsuario() async {
    try {
      final userData = await userDataController.getUserData();
      if (userData != null && userData.containsKey('userId')) {
        _userId.value = userData['userId'];
      }
    } catch (e) {
      debugPrint('Erro ao carregar ID do usuário: $e');
    }
  }

  @override
  void onClose() {
    scannerController?.dispose();
    super.onClose();
  }

  void toggleScanner() {
    if (!showScanner.value) {
      errorMessage.value = '';
      scannerController = MobileScannerController();
    } else {
      scannerController?.dispose();
      scannerController = null;
    }
    showScanner.toggle();
  }

  Future<void> processQRCode(String rawData) async {
    if (isProcessing) return;

    isProcessing = true;
    errorMessage.value = '';

    try {
      debugPrint('\n========== PROCESSANDO QR CODE ==========');
      debugPrint('Dados brutos recebidos: ${rawData.length} caracteres');

      Map<String, dynamic> qrData;
      try {
        qrData = json.decode(rawData);
        debugPrint('JSON decodificado com sucesso: $qrData');
      } catch (e) {
        debugPrint('Erro ao decodificar JSON: $e');
        throw Exception(
            'Código QR inválido. Por favor, tente novamente ou solicite um novo QR code.');
      }

      // Validar dados essenciais
      if (!qrData.containsKey('qrId') ||
          !qrData.containsKey('medicoId') ||
          !qrData.containsKey('hospitalId')) {
        debugPrint('Campos obrigatórios ausentes no QR code: $qrData');
        throw Exception(
            'Código QR incompleto ou inválido. Por favor, solicite à recepção que gere um novo QR Code.');
      }

      // Verificar validade do QR Code no banco
      debugPrint(
          'Verificando QR Code no banco de dados. ID: ${qrData['qrId']}');
      final queryQRCode = QueryBuilder<ParseObject>(ParseObject('QRCodeGerado'))
        ..whereEqualTo('qr_id', qrData['qrId'])
        ..whereEqualTo('valido', true)
        ..whereEqualTo(
            'medico_id', ParseObject('Medico')..objectId = qrData['medicoId'])
        ..whereEqualTo('consultorio_id',
            ParseObject('consultorio')..objectId = qrData['hospitalId']);

      final qrResponse = await queryQRCode.query();
      debugPrint(
          'Resposta da query: ${qrResponse.success}, Resultados: ${qrResponse.count}');

      if (!qrResponse.success) {
        throw Exception(
            'Erro ao verificar QR Code: ${qrResponse.error?.message}');
      }

      if (qrResponse.results == null || qrResponse.results!.isEmpty) {
        throw Exception(
            'QR Code inválido ou expirado. Por favor, solicite à recepção que gere um novo QR Code.');
      }

      debugPrint(
          'QR Code válido encontrado: ${qrResponse.results!.first.objectId}');

      // Obter dados do usuário
      final userData = await userDataController.getUserData();
      if (userData == null) {
        throw Exception(
            'Dados do usuário não encontrados. Por favor, reinicie o aplicativo.');
      }

      // Usar o ID simplificado nos dados do usuário
      final idPaciente = userData['userId'];
      debugPrint('Processando QR code para usuário: $idPaciente');

      // Verificar solicitação pendente
      debugPrint(
          'Verificando solicitações pendentes para este médico/hospital');
      final querySolicitacao =
          QueryBuilder<ParseObject>(ParseObject('FilaSolicitacao'))
            ..whereEqualTo('medicoId',
                ParseObject('Medico')..objectId = qrData['medicoId'])
            ..whereEqualTo('hospitalId',
                ParseObject('consultorio')..objectId = qrData['hospitalId'])
            ..whereEqualTo('idPaciente', idPaciente)
            ..whereContainedIn('status', ['pendente', 'aprovado']);

      final solicitacaoResponse = await querySolicitacao.query();
      debugPrint('Solicitações encontradas: ${solicitacaoResponse.count}');

      if (solicitacaoResponse.success &&
          solicitacaoResponse.results != null &&
          solicitacaoResponse.results!.isNotEmpty) {
        throw Exception(
            'Você já possui uma solicitação para este médico. Aguarde a secretária processar seu pedido.');
      }

      // Verificar se está em alguma fila usando a classe Usuario
      debugPrint('Verificando se usuário já está em uma fila (Usuario)');
      final queryUsuario = QueryBuilder<ParseObject>(ParseObject('Usuario'))
        ..whereEqualTo('deviceId', idPaciente)
        ..whereEqualTo('em_fila', true);

      final usuarioResponse = await queryUsuario.query();
      debugPrint('Resultado da verificação Usuario: ${usuarioResponse.count}');

      if (usuarioResponse.success &&
          usuarioResponse.results != null &&
          usuarioResponse.results!.isNotEmpty) {
        debugPrint(
            'Usuário já está em uma fila (verificado na classe Usuario)');
        throw Exception(
            'Você já está em uma fila. Finalize ou cancele seu atendimento atual antes de entrar em uma nova fila.');
      }

      // Verificar também na classe Fila diretamente (método mais confiável)
      debugPrint('Verificando se usuário já está em uma fila (Fila)');
      final queryFila = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('idPaciente', idPaciente)
        ..whereContainedIn('status', ['aguardando', 'em_atendimento']);

      final filaResponse = await queryFila.query();
      debugPrint('Resultado da verificação Fila: ${filaResponse.count}');

      if (filaResponse.success &&
          filaResponse.results != null &&
          filaResponse.results!.isNotEmpty) {
        debugPrint('Usuário já está em uma fila (verificado na classe Fila)');
        throw Exception(
            'Você já está em uma fila. Finalize ou cancele seu atendimento atual antes de entrar em uma nova fila.');
      }

      // Gerar solicitacao_id simplificado
      final agora = DateTime.now();
      final solicitacaoId =
          'S-${agora.day}${agora.month}${agora.hour}${agora.minute}${agora.second}';
      debugPrint('ID de solicitação gerado: $solicitacaoId');

      // Buscar informações adicionais do usuário
      String nome = userData['nome'] ?? 'Paciente';
      String telefone = userData['telefone'] ?? '';
      debugPrint('Dados do usuário: nome=$nome, telefone=$telefone');

      // Se não tiver dados completos localmente, tentar buscar na classe Usuario
      if (nome == 'Paciente' || telefone.isEmpty) {
        try {
          debugPrint('Buscando dados complementares na classe Usuario');
          final queryUsuarioInfo =
              QueryBuilder<ParseObject>(ParseObject('Usuario'))
                ..whereEqualTo('deviceId', idPaciente);

          final usuarioInfoResponse = await queryUsuarioInfo.query();
          debugPrint('Resultado da busca: ${usuarioInfoResponse.count}');

          if (usuarioInfoResponse.success &&
              usuarioInfoResponse.results != null &&
              usuarioInfoResponse.results!.isNotEmpty) {
            final usuarioInfo = usuarioInfoResponse.results!.first;
            if (nome == 'Paciente' && usuarioInfo.get<String>('nome') != null) {
              nome = usuarioInfo.get<String>('nome')!;
            }
            if (telefone.isEmpty &&
                usuarioInfo.get<String>('telefone') != null) {
              telefone = usuarioInfo.get<String>('telefone')!;
            }

            // Atualizar dados locais para futuras referências
            if (nome != 'Paciente' || telefone.isNotEmpty) {
              await userDataController.saveUserData(nome, telefone);
              debugPrint(
                  'Dados salvos localmente: nome=$nome, telefone=$telefone');
            }
          }
        } catch (e) {
          debugPrint('Erro ao buscar informações do usuário: $e');
          // Continuar com os dados que temos
        }
      }

      // Criar nova solicitação com os dados do usuário
      debugPrint('Criando nova solicitação na classe FilaSolicitacao');
      final solicitacao = ParseObject('FilaSolicitacao')
        ..set('solicitacao_id', solicitacaoId)
        ..set('medicoId', ParseObject('Medico')..objectId = qrData['medicoId'])
        ..set('hospitalId',
            ParseObject('consultorio')..objectId = qrData['hospitalId'])
        ..set('status', 'pendente')
        ..set('idPaciente', idPaciente)
        // Adicionar dados do usuário
        ..set('nome', nome)
        ..set('telefone', telefone);

      // Definir ACL ampla para evitar problemas de permissão
      final acl = ParseACL();
      acl.setPublicReadAccess(allowed: true);
      acl.setPublicWriteAccess(allowed: true);
      solicitacao.setACL(acl);

      final result = await solicitacao.save();
      debugPrint('Resultado do salvamento: ${result.success}');

      if (!result.success) {
        throw Exception(result.error?.message ??
            'Erro ao criar solicitação. Tente novamente.');
      }

      // Criar notificação
      debugPrint('Criando notificação de nova solicitação');
      final notificacao = ParseObject('Notificacao')
        ..set('tipo', 'nova_solicitacao')
        ..set('solicitacao_id', solicitacaoId)
        ..set('medico_id', qrData['medicoId'])
        ..set('consultorio_id', qrData['hospitalId'])
        ..set('lida', false);

      final notificacaoAcl = ParseACL();
      notificacaoAcl.setPublicReadAccess(allowed: true);
      notificacaoAcl.setPublicWriteAccess(allowed: true);
      notificacao.setACL(notificacaoAcl);

      await notificacao.save();
      debugPrint('Notificação criada');

      // Atualizar objeto Usuario para registrar o scan do QR code
      try {
        debugPrint('Atualizando registro de Usuario');
        final queryUsuarioUpdate =
            QueryBuilder<ParseObject>(ParseObject('Usuario'))
              ..whereEqualTo('deviceId', idPaciente);

        final usuarioUpdateResponse = await queryUsuarioUpdate.query();

        if (usuarioUpdateResponse.success &&
            usuarioUpdateResponse.results != null &&
            usuarioUpdateResponse.results!.isNotEmpty) {
          final usuario = usuarioUpdateResponse.results!.first;
          usuario.set('ultimo_qrcode', qrResponse.results!.first);
          usuario.set('ultimoAcesso', DateTime.now());
          await usuario.save();
          debugPrint('Atualizado último QR code do usuário');
        } else {
          // Criar um novo registro se não existir
          debugPrint('Criando novo registro de Usuario');
          final novoUsuario = ParseObject('Usuario')
            ..set('deviceId', idPaciente)
            ..set('nome', nome)
            ..set('telefone', telefone)
            ..set('data_cadastro', DateTime.now())
            ..set('ultima_atualizacao', DateTime.now())
            ..set('ultimoAcesso', DateTime.now())
            ..set('em_fila', false)
            ..set('ultimo_qrcode', qrResponse.results!.first);

          final usuarioAcl = ParseACL();
          usuarioAcl.setPublicReadAccess(allowed: true);
          usuarioAcl.setPublicWriteAccess(allowed: true);
          novoUsuario.setACL(usuarioAcl);

          await novoUsuario.save();
          debugPrint('Criado novo registro de usuário ao escanear QR code');
        }
      } catch (e) {
        // Ignorar erros aqui, pois não é crítico
        debugPrint(
            'Aviso: Erro ao atualizar objeto Usuario com referência ao QR code: $e');
      }

      debugPrint(
          'Solicitação processada com sucesso! Redirecionando para tela de carregamento');
      // Redirecionar para tela de carregamento
      Get.offNamed(
        '/carregando',
        arguments: {
          'solicitacaoId': solicitacaoId,
          'medicoId': qrData['medicoId'],
          'hospitalId': qrData['hospitalId'],
        },
      );

      debugPrint('========== FIM DO PROCESSAMENTO ==========\n');
    } catch (e) {
      debugPrint('ERRO AO PROCESSAR QR CODE: $e');
      errorMessage.value = e.toString().replaceAll('Exception: ', '');
      isProcessing = false;
    } finally {
      if (scannerController != null) {
        scannerController!.dispose();
        scannerController = MobileScannerController();
      }
    }
  }

  // Verificar o status atual de uma solicitação
  Future<Map<String, dynamic>?> verificarStatusSolicitacao(
      String solicitacaoId) async {
    isLoading.value = true;
    try {
      final query = QueryBuilder<ParseObject>(ParseObject('FilaSolicitacao'))
        ..whereEqualTo('solicitacao_id', solicitacaoId);

      final response = await query.query();

      if (response.success &&
          response.results != null &&
          response.results!.isNotEmpty) {
        final solicitacao = response.results!.first;
        return {
          'status': solicitacao.get<String>('status'),
          'objectId': solicitacao.objectId,
          'medicoId': solicitacao.get<ParseObject>('medicoId')?.objectId,
          'hospitalId': solicitacao.get<ParseObject>('hospitalId')?.objectId,
        };
      }
      return null;
    } catch (e) {
      errorMessage.value = 'Erro ao verificar status: ${e.toString()}';
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  // Cancelar uma solicitação pendente
  Future<bool> cancelarSolicitacao(String solicitacaoId) async {
    isLoading.value = true;
    try {
      // Obter dados do usuário
      final userData = await userDataController.getUserData();
      if (userData == null) {
        throw Exception('Dados do usuário não encontrados.');
      }

      final idPaciente = userData['userId'];

      // Usar a Cloud Function para cancelar
      final params = <String, dynamic>{
        'solicitacaoId': solicitacaoId,
        'deviceId': idPaciente
      };

      final response = await ParseCloudFunction('cancelarSolicitacaoPaciente')
          .execute(parameters: params);

      if (!response.success) {
        throw Exception(
            response.error?.message ?? 'Erro ao cancelar solicitação');
      }

      return true;
    } catch (e) {
      errorMessage.value = 'Erro ao cancelar solicitação: ${e.toString()}';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Resetar o estado do controller
  void resetController() {
    errorMessage.value = '';
    if (showScanner.value) {
      toggleScanner();
    }
    isProcessing = false;
  }

  void showQRDialog(Map<String, dynamic> qrData) {
    Get.dialog(
      AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: const Text(
          'Deseja entrar na fila do\nDr(a):',
          style: TextStyle(
            fontFamily: 'Georgia',
            fontSize: 24,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              qrData['medicoNome'],
              style: const TextStyle(
                fontFamily: 'Georgia',
                fontSize: 32,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            Text(
              "(${qrData['especialidade']})",
              style: const TextStyle(
                fontFamily: 'Georgia',
                fontSize: 24,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: Get.back,
            child: const Text('Cancelar'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              processQRCode(json.encode(qrData));
            },
            child: const Text('Confirmar'),
          ),
        ],
      ),
    );
  }

  // Cancelar espera na fila
  Future<bool> cancelarEsperaFila(String filaId) async {
    try {
      isLoading.value = true;

      // Usar o método Fila.updateStatus para cancelar a espera
      final success = await Fila.updateStatus(
        filaId: filaId,
        status: 'cancelado',
        idPaciente: userId,
      );

      if (success) {
        emFila.value = false;
        filaAtual.value = null;
        await _atualizarStatusPacienteLocal(false);
        return true;
      } else {
        throw Exception('Erro ao cancelar espera na fila');
      }
    } catch (e) {
      debugPrint('Erro ao cancelar espera na fila: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Atualizar status do paciente localmente
  Future<void> _atualizarStatusPacienteLocal(bool emFila) async {
    try {
      // Obter dados do usuário para verificação
      final userData = await userDataController.getUserData();
      if (userData == null || !userData.containsKey('userId')) {
        throw Exception('Dados do usuário não encontrados.');
      }

      final deviceId = userData['userId'];

      // Primeiro verificar na classe Usuario (novo padrão)
      final queryUsuario = QueryBuilder<ParseObject>(ParseObject('Usuario'))
        ..whereEqualTo('deviceId', deviceId);

      final usuarioResponse = await queryUsuario.query();

      if (usuarioResponse.success &&
          usuarioResponse.results != null &&
          usuarioResponse.results!.isNotEmpty) {
        final usuario = usuarioResponse.results!.first;
        usuario.set('em_fila', emFila);
        if (!emFila) {
          usuario.set('ultima_fila', DateTime.now());
        }
        await usuario.save();
        debugPrint(
            'Status do usuário atualizado na classe Usuario: em_fila=$emFila');
      } else {
        debugPrint(
            'Usuário não encontrado na classe Usuario, verificando na classe Paciente');

        // Retrocompatibilidade: verificar na classe Paciente
        final pacienteQuery = QueryBuilder<ParseObject>(ParseObject('Paciente'))
          ..whereEqualTo('userId', deviceId);

        final pacienteResponse = await pacienteQuery.query();

        if (pacienteResponse.success &&
            pacienteResponse.results != null &&
            pacienteResponse.results!.isNotEmpty) {
          final paciente = pacienteResponse.results!.first;
          paciente.set('em_fila', emFila);
          if (!emFila) {
            paciente.set('ultima_fila', DateTime.now());
          }
          await paciente.save();
          debugPrint(
              'Status do usuário atualizado na classe Paciente: em_fila=$emFila');
        } else {
          debugPrint(
              'Usuário não encontrado em nenhuma classe. Pulando atualização de status.');
        }
      }
    } catch (e) {
      debugPrint('Erro ao atualizar status do paciente: $e');
    }
  }
}
