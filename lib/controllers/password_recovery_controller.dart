import 'package:flutter/foundation.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';

class PasswordRecoveryController {
  // Método para enviar e-mail de recuperação de senha usando o método oficial do Parse Server
  Future<Map<String, dynamic>> sendPasswordResetEmail(String email) async {
    try {
      debugPrint('Iniciando solicitação de redefinição de senha para: $email');

      // Usar o método oficial do Parse Server para redefinição de senha
      final ParseUser user = ParseUser(null, null, email);
      final ParseResponse response = await user.requestPasswordReset();

      if (response.success) {
        return {
          'success': true,
          'message': 'E-mail de recuperação enviado com sucesso!'
        };
      } else {
        return {
          'success': false,
          'error': response.error?.message ?? 'Erro ao enviar e-mail de recuperação'
        };
      }
    } catch (e) {
      debugPrint('Erro ao solicitar redefinição de senha: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  // Método para verificar tipos de usuário com base no email (opcional)
  Future<String?> checkUserType(String email) async {
    try {
      final queryUser = QueryBuilder<ParseUser>(ParseUser.forQuery())
        ..whereEqualTo('email', email);

      final response = await queryUser.query();

      if (response.success &&
          response.results != null &&
          response.results!.isNotEmpty) {
        final user = response.results!.first as ParseUser;
        return user.get<String>('tipo');
      }

      return null;
    } catch (e) {
      debugPrint('Erro ao verificar tipo de usuário: $e');
      return null;
    }
  }
}
