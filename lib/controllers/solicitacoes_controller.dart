import 'package:get/get.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'package:intl/intl.dart';
import 'package:flutter/material.dart';
import 'dart:async';

class SolicitacoesController extends GetxController {
  final RxList<ParseObject> solicitacoes = <ParseObject>[].obs;
  final RxBool isLoading = true.obs;
  final RxString error = ''.obs;
  ParseObject? currentHospital;
  Timer? refreshTimer;
  final String medicoId;

  SolicitacoesController({required this.medicoId});

  @override
  void onInit() {
    super.onInit();
    buscarHospitalAtual();
  }

  @override
  void onClose() {
    refreshTimer?.cancel();
    super.onClose();
  }

  Future<void> buscarHospitalAtual() async {
    try {
      isLoading.value = true;
      error.value = '';
      
      final currentUser = await ParseUser.currentUser() as ParseUser?;
      if (currentUser == null) throw Exception('Usuário não encontrado');

      final queryHospital = QueryBuilder<ParseObject>(ParseObject('consultorio'))
        ..whereEqualTo('user_consultorio', currentUser.toPointer());

      final response = await queryHospital.query();
      
      if (!response.success || response.results == null || response.results!.isEmpty) {
        throw Exception('Hospital não encontrado');
      }

      currentHospital = response.results!.first;
      await carregarSolicitacoes();
    } catch (e) {
      error.value = 'Erro ao buscar hospital: $e';
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> carregarSolicitacoes() async {
    if (currentHospital == null) return;
    
    try {
      isLoading.value = true;
      error.value = '';
      
      final querySolicitacoes = QueryBuilder<ParseObject>(ParseObject('FilaSolicitacao'))
        ..whereEqualTo('medicoId', ParseObject('Medico')..objectId = medicoId)
        ..whereEqualTo('hospitalId', currentHospital)
        ..whereEqualTo('status', 'pendente')
        ..orderByAscending('createdAt');

      final response = await querySolicitacoes.query();
      solicitacoes.value = (response.results as List<dynamic>).cast<ParseObject>();
    } catch (e) {
      error.value = 'Erro ao carregar solicitações: $e';
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> aprovarSolicitacao(ParseObject solicitacao) async {
    try {
      isLoading.value = true;
      error.value = '';

      final notificacao = ParseObject('Notificacao')
        ..set('tipo', 'entrada_fila')
        ..set('solicitacao_id', solicitacao.get<String>('solicitacao_id'))
        ..set('medico_id', medicoId)
        ..set('consultorio_id', currentHospital!.objectId)
        ..set('lida', false);

      await notificacao.save();

      // Atualizar status da solicitação
      solicitacao.set('status', 'aprovado');
      await solicitacao.save();

      // Remover a solicitação da lista local
      solicitacoes.remove(solicitacao);

      // Ir para tela de inserir paciente
      Get.offNamed(
        '/inserir_paciente_fila',
        arguments: {
          'nomeMedico': Get.arguments['nome'],
          'especialidade': Get.arguments['especialidade'],
          'id': solicitacao.get<String>('idPaciente'),
          'solicitacaoId': solicitacao.objectId,
        },
      );

    } catch (e) {
      error.value = 'Erro ao aprovar solicitação: $e';
      Get.snackbar(
        'Erro',
        error.value,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> recusarSolicitacao(ParseObject solicitacao) async {
    try {
      isLoading.value = true;

      final notificacao = ParseObject('Notificacao')
        ..set('tipo', 'cancelamento')
        ..set('solicitacao_id', solicitacao.get<String>('solicitacao_id'))
        ..set('medico_id', medicoId)
        ..set('consultorio_id', currentHospital!.objectId)
        ..set('lida', false);

      await notificacao.save();
      
      solicitacao.set('status', 'rejeitado');
      await solicitacao.save();
      
      solicitacoes.remove(solicitacao);

      Get.snackbar(
        'Sucesso',
        'Solicitação recusada',
        backgroundColor: Colors.orange,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      error.value = 'Erro ao recusar solicitação: $e';
      Get.snackbar(
        'Erro',
        error.value,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoading.value = false;
    }
  }

  String formatarDataHora(DateTime? dataHora) {
    if (dataHora == null) return 'Data não disponível';
    return DateFormat('dd/MM/yyyy HH:mm').format(dataHora);
  }
}