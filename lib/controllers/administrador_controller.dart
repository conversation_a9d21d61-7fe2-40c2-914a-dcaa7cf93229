import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'package:flutter/material.dart';

class AdministradorController {
  // Buscar dados do consultório
  Future<Map<String, dynamic>> buscarDadosConsultorio() async {
    try {
      final currentUser = await ParseUser.currentUser() as ParseUser;
      debugPrint('Buscando dados do consultório para o usuário: ${currentUser.objectId}');

      final QueryBuilder<ParseObject> query = QueryBuilder<ParseObject>(ParseObject('consultorio'));
      query.whereEqualTo('user_consultorio', currentUser.toPointer());

      debugPrint('Executando query...');
      final response = await query.query();
      debugPrint('Resposta recebida: ${response.results?.length} resultados');

      if (response.success && response.results != null && response.results!.isNotEmpty) {
        final hospital = response.results!.first;
        debugPrint('Dados do hospital encontrados: ${hospital.objectId}');

        return {
          'nome': hospital.get<String>('nome') ?? '',
          'email': currentUser.emailAddress ?? '',
          'cpfCnpj': hospital.get<String>('cnpj') ?? '',
          'tipo': hospital.get<String>('tipo') ?? '',
          'telefone': hospital.get<String>('telefone') ?? '',
          'latitude': hospital.get<double>('latitude'),
          'longitude': hospital.get<double>('longitude'),
          'ativo': hospital.get<bool>('ativo') ?? true,
        };
      } else {
        debugPrint('Nenhum hospital encontrado, retornando dados vazios');
        return {
          'nome': currentUser.username ?? '',
          'email': currentUser.emailAddress ?? '',
          'cpfCnpj': '',
          'tipo': 'Particular',
          'telefone': '',
          'latitude': null,
          'longitude': null,
          'ativo': true,
        };
      }
    } catch (e) {
      debugPrint('Erro ao buscar dados: $e');
      rethrow;
    }
  }

  // Atualizar dados do consultório
  Future<void> atualizarDadosConsultorio(Map<String, dynamic> novosDados) async {
    try {
      final currentUser = await ParseUser.currentUser();
      if (currentUser == null) {
        throw Exception('Usuário não logado');
      }

      final QueryBuilder<ParseObject> query = QueryBuilder<ParseObject>(ParseObject('consultorio'));
      query.whereEqualTo('user_consultorio', currentUser.toPointer());

      final response = await query.query();

      bool emailChanged = false;

      if (response.success && response.results != null && response.results!.isNotEmpty) {
        final consultorio = response.results!.first;
        final Map<String, dynamic> camposAlterados = {};

        // Verificar alteração no e-mail
        if (novosDados['email'] != currentUser.emailAddress) {
          currentUser.emailAddress = novosDados['email'];

          final saveUserResponse = await currentUser.save();

          if (!saveUserResponse.success) {
            throw Exception('Erro ao atualizar o e-mail do usuário: ${saveUserResponse.error?.message}');
          }

          emailChanged = true;
          debugPrint('E-mail atualizado.');
        }

        // Comparar e identificar campos alterados
        if (novosDados['nome'] != consultorio.get<String>('nome')) {
          camposAlterados['nome'] = novosDados['nome'];
        }
        if (novosDados['cpfCnpj'] != consultorio.get<String>('cnpj')) {
          camposAlterados['cnpj'] = novosDados['cpfCnpj'];
        }
        if (novosDados['tipo'] != consultorio.get<String>('tipo')) {
          camposAlterados['tipo'] = novosDados['tipo'];
        }
        if (novosDados['telefone'] != consultorio.get<String>('telefone')) {
          camposAlterados['telefone'] = novosDados['telefone'];
        }
        if (novosDados['latitude'] != consultorio.get<double>('latitude')) {
          camposAlterados['latitude'] = novosDados['latitude'];
        }
        if (novosDados['longitude'] != consultorio.get<double>('longitude')) {
          camposAlterados['longitude'] = novosDados['longitude'];
        }
        if (novosDados['ativo'] != consultorio.get<bool>('ativo')) {
          camposAlterados['ativo'] = novosDados['ativo'];
        }

        // Atualizar apenas campos modificados
        if (camposAlterados.isNotEmpty || emailChanged) {
          camposAlterados.forEach((key, value) {
            consultorio.set(key, value);
          });

          final saveResponse = await consultorio.save();
          if (saveResponse.success) {
            debugPrint('Campos atualizados com sucesso: ${camposAlterados.keys.join(', ')}');
          } else {
            throw Exception('Erro ao salvar alterações: ${saveResponse.error?.message}');
          }
        } else {
          debugPrint('Nenhum campo foi modificado');
        }
      } else {
        // Criar novo consultório se não existir
        final novoConsultorio = ParseObject('consultorio')
          ..set('user_consultorio', currentUser.toPointer())
          ..set('nome', novosDados['nome'])
          ..set('cnpj', novosDados['cpfCnpj'])
          ..set('tipo', novosDados['tipo'])
          ..set('telefone', novosDados['telefone'])
          ..set('latitude', novosDados['latitude'])
          ..set('longitude', novosDados['longitude'])
          ..set('ativo', novosDados['ativo']);

        await novoConsultorio.save();
        debugPrint('Novo consultório criado com sucesso');
      }
    } catch (e) {
      debugPrint('Erro ao atualizar dados: $e');
      rethrow;
    }
  }
}
