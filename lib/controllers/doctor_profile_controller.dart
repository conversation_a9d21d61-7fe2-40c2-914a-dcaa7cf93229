import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'package:flutter/foundation.dart' show debugPrint;
import 'dart:io';
import 'package:flutter_dotenv/flutter_dotenv.dart';

class DoctorProfileController {
  // Buscar dados do médico
  Future<Map<String, dynamic>> fetchDoctorProfile() async {
    try {
      // Obter o usuário atual
      final currentUser = await ParseUser.currentUser() as ParseUser?;
      
      if (currentUser == null) {
        return {'success': false, 'error': 'Usuário não autenticado'};
      }
      
      // Buscar o perfil do médico usando o ID do usuário atual
      final queryDoctor = QueryBuilder<ParseObject>(ParseObject('Medico'))
        ..whereEqualTo('user_medico', currentUser.toPointer());
      
      final response = await queryDoctor.query();
      
      if (response.success && response.results != null && response.results!.isNotEmpty) {
        final doctor = response.results!.first;
        
        // Obter a URL da imagem de perfil, se existir
        String? profileImageUrl;
        if (doctor.get<ParseFile>('profileImage') != null) {
          profileImageUrl = doctor.get<ParseFile>('profileImage')!.url;
          
          // No Back4App, às vezes é necessário adicionar o protocolo https se não estiver presente
          if (profileImageUrl != null && !profileImageUrl.startsWith('http')) {
            profileImageUrl = 'https://$profileImageUrl';
          }
        }
        
        // Obter as perguntas personalizadas da pesquisa
        final List<dynamic> perguntasObj = doctor.get<List<dynamic>>('perguntasPersonalizadas') ?? [];
        final List<String> perguntas = perguntasObj.map((p) => p.toString()).toList();
        
        // Obter e formatar o CPF para exibição
        var cpf = doctor.get<num>('cpf')?.toString();
        
        debugPrint('Dados do médico obtidos:');
        debugPrint('Nome: ${doctor.get<String>('nome')}');
        debugPrint('CRM: ${doctor.get<String>('crm')}');
        debugPrint('CPF: $cpf');
        debugPrint('Especialidade: ${doctor.get<String>('especialidade')}');
        debugPrint('Telefone: ${doctor.get<String>('telefone')}');
        
        return {
          'success': true,
          'data': {
            'id': doctor.objectId,
            'nome': doctor.get<String>('nome') ?? '',
            'email': currentUser.emailAddress ?? '',
            'crm': doctor.get<String>('crm') ?? '',
            'cpf': cpf ?? '',
            'especialidade': doctor.get<String>('especialidade') ?? '',
            'ativo': doctor.get<bool>('ativo') ?? true,
            'profileImageUrl': profileImageUrl,
            'telefone': doctor.get<String>('telefone') ?? '',
            'pesquisaSatisfacaoAtiva': doctor.get<bool>('pesquisaSatisfacaoAtiva') ?? false,
            'perguntasPersonalizadas': perguntas,
          }
        };
      } else {
        // Retornar dados padrão para criar o perfil pela primeira vez
        return {
          'success': true,
          'data': {
            'id': '',
            'nome': currentUser.username ?? '',
            'email': currentUser.emailAddress ?? '',
            'crm': '',
            'cpf': '',
            'especialidade': '',
            'ativo': true,
            'profileImageUrl': null,
            'telefone': '',
            'pesquisaSatisfacaoAtiva': false,
            'perguntasPersonalizadas': <String>[],
          }
        };
      }
    } catch (e) {
      debugPrint('Erro ao buscar perfil: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  // Atualizar dados do médico
  Future<Map<String, dynamic>> updateDoctorProfile({
    required String nome,
    required String email,
    required String crm,
    required int cpf,
    required String especializacao,
    required bool ativo,
    File? profileImage,
    String? telefone,
    bool? pesquisaSatisfacaoAtiva,
    List<String>? perguntasPersonalizadas,
  }) async {
    try {
      // Obter o usuário atual
      final currentUser = await ParseUser.currentUser() as ParseUser?;
      
      if (currentUser == null) {
        return {'success': false, 'error': 'Usuário não autenticado'};
      }
      
      // Atualizar o e-mail do usuário, se necessário
      if (currentUser.emailAddress != email) {
        currentUser.emailAddress = email;
        final userResult = await currentUser.save();
        if (!userResult.success) {
          return {'success': false, 'error': 'Erro ao atualizar e-mail'};
        }
      }
      
      // Buscar o perfil do médico existente
      final queryDoctor = QueryBuilder<ParseObject>(ParseObject('Medico'))
        ..whereEqualTo('user_medico', currentUser.toPointer());
      
      final response = await queryDoctor.query();
      
      late ParseObject doctor;
      
      if (response.success && response.results != null && response.results!.isNotEmpty) {
        // Atualizar perfil existente
        doctor = response.results!.first;
      } else {
        // Criar novo perfil se não existir
        doctor = ParseObject('Medico')
          ..set('user_medico', currentUser.toPointer());
      }
      
      // Atualizar os dados do médico - usando os mesmos nomes de campo da tela de cadastro
      doctor.set('nome', nome);
      doctor.set('crm', crm);
      doctor.set('cpf', cpf);
      doctor.set('especialidade', especializacao); // Alterado para 'especialidade' para combinar com a tela de cadastro
      doctor.set('ativo', ativo);
      
      // Novos campos
      if (telefone != null) {
        doctor.set('telefone', telefone);
      }
      
      if (pesquisaSatisfacaoAtiva != null) {
        doctor.set('pesquisaSatisfacaoAtiva', pesquisaSatisfacaoAtiva);
      }
      
      if (perguntasPersonalizadas != null) {
        doctor.set('perguntasPersonalizadas', perguntasPersonalizadas);
      }
      
      // Fazer upload da imagem, se fornecida
      if (profileImage != null) {
        // Para o Back4App, às vezes é melhor especificar o nome do arquivo
        final fileExtension = profileImage.path.split('.').last;
        final fileName = 'profile_${currentUser.objectId}_${DateTime.now().millisecondsSinceEpoch}.$fileExtension';
        
        final parseFile = ParseFile(profileImage, name: fileName);
        final fileResponse = await parseFile.save();
        
        if (fileResponse.success) {
          doctor.set('profileImage', parseFile);
        } else {
          return {'success': false, 'error': 'Erro ao fazer upload da imagem: ${fileResponse.error?.message}'};
        }
      }
      
      // Salvar o perfil do médico
      final doctorResponse = await doctor.save();
      
      if (doctorResponse.success) {
        return {'success': true};
      } else {
        return {'success': false, 'error': doctorResponse.error?.message ?? 'Erro ao salvar perfil'};
      }
    } catch (e) {
      debugPrint('Erro ao atualizar perfil: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  // Método para verificar se o médico está ativo
  Future<bool> isDoctorActive() async {
    try {
      // Obter o usuário atual
      final currentUser = await ParseUser.currentUser() as ParseUser?;
      
      if (currentUser == null) {
        debugPrint('Erro ao verificar status: Usuário não autenticado');
        return false;
      }
      
      // Buscar o perfil do médico usando o ID do usuário atual
      final queryDoctor = QueryBuilder<ParseObject>(ParseObject('Medico'))
        ..whereEqualTo('user_medico', currentUser.toPointer());
      
      final response = await queryDoctor.query();
      
      if (response.success && response.results != null && response.results!.isNotEmpty) {
        currentDoctor = response.results!.first;
        final isActive = currentDoctor!.get<bool>('ativo') ?? true;
        debugPrint('Status atual do médico: ${isActive ? 'Ativo' : 'Inativo'}');
        return isActive;
      }
      
      debugPrint('Médico não encontrado ao verificar status');
      return false;
    } catch (e) {
      debugPrint('Erro ao verificar status do médico: $e');
      return false;
    }
  }
  
  // Armazenar referência para objeto do médico atual
  ParseObject? currentDoctor;
  
  // Método para obter hospitais vinculados ao médico
  Future<List<dynamic>> getLinkedHospitals() async {
    try {
      if (currentDoctor == null) {
        // Se o médico atual não estiver disponível, buscar primeiro
        await isDoctorActive(); // Apenas chama o método para inicializar currentDoctor
        if (currentDoctor == null) {
          debugPrint('Não foi possível obter o objeto do médico');
          return [];
        }
      }
      
      debugPrint('Buscando hospitais vinculados para o médico: ${currentDoctor!.objectId}');
      
      // Chamar função Cloud para buscar hospitais
      final ParseCloudFunction function = ParseCloudFunction('buscarHospitaisDisponiveis');
      final result = await function.execute(parameters: {
        'medicoId': currentDoctor!.objectId
      });
      
      if (result.success && result.result != null) {
        final resultData = result.result as Map<String, dynamic>;
        if (resultData['success'] == true && resultData['hospitais'] != null) {
          final List<dynamic> hospitais = resultData['hospitais'];
          debugPrint('Hospitais vinculados: ${hospitais.length}');
          
          // Filtrar apenas hospitais onde o médico está vinculado
          final hospitaisVinculados = hospitais.where((h) => h['vinculado'] == true).toList();
          debugPrint('Hospitais com vínculo ativo: ${hospitaisVinculados.length}');
          
          return hospitaisVinculados;
        }
      }
      
      debugPrint('Nenhum hospital vinculado encontrado');
      return [];
    } catch (e) {
      debugPrint('Erro ao buscar hospitais vinculados: $e');
      return [];
    }
  }
}