import 'package:get/get.dart';
import 'package:fila_app/modules/admin/controllers/admin_controller.dart';
import 'package:fila_app/controllers/login_controller.dart';
import 'package:fila_app/modules/admin/screens/admin_screen.dart';

// Este arquivo serve como um ponto de entrada para o módulo de administração
// Facilita a organização e inicialização de dependências

class AdminModule {
  static void init() {
    // Garantir que os controllers estejam registrados
    if (!Get.isRegistered<AdminController>()) {
      Get.put(AdminController());
    }

    if (!Get.isRegistered<LoginController>()) {
      Get.put(LoginController());
    }
  }

  static List<GetPage> routes() {
    return [
      GetPage(
        name: '/admin_hospitais',
        page: () => const AdminScreen(),
        binding: BindingsBuilder(() {
          init();
        }),
      ),
    ];
  }
}
