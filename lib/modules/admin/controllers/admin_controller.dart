import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:uuid/uuid.dart';

class AdminController extends GetxController {
  final RxList<ParseObject> hospitais = <ParseObject>[].obs;
  final RxBool isLoading = false.obs;
  final RxString error = ''.obs;
  final RxString successMessage = ''.obs;
  final RxBool isEditMode = false.obs;
  final RxString currentHospitalId = ''.obs;
  final RxBool loadingLocation = false.obs;

  // Controllers para formulário
  final nomeController = TextEditingController();
  final emailController = TextEditingController();
  final cnpjController = TextEditingController();
  final senhaController = TextEditingController();
  final tipoController = TextEditingController();
  final telefoneController = TextEditingController();
  final enderecoController = TextEditingController();
  final latitudeController = TextEditingController();
  final longitudeController = TextEditingController();

  // Estado do mapa
  final Rx<LatLng?> selectedLocation = Rx<LatLng?>(null);
  final RxSet<Marker> markers = <Marker>{}.obs;

  @override
  void onInit() {
    super.onInit();
    carregarHospitais();
    gerarSenhaPadrao();
  }

  @override
  void onClose() {
    nomeController.dispose();
    emailController.dispose();
    cnpjController.dispose();
    senhaController.dispose();
    tipoController.dispose();
    telefoneController.dispose();
    enderecoController.dispose();
    latitudeController.dispose();
    longitudeController.dispose();
    super.onClose();
  }

  // Gerar senha aleatória para facilitar cadastro
  void gerarSenhaPadrao() {
    final uuid = const Uuid().v4();
    senhaController.text = uuid.substring(0, 8);
  }

  // Verifica se o formulário tem alterações não salvas
  bool formHasChanges() {
    if (isEditMode.value && currentHospitalId.value.isNotEmpty) {
      // Buscar hospital atual
      final hospital = hospitais.firstWhere(
        (h) => h.objectId == currentHospitalId.value,
        orElse: () => ParseObject('consultorio'),
      );

      // Verificar se houve alterações nos campos
      if (hospital.objectId != null) {
        if (nomeController.text != (hospital.get<String>('nome') ?? '')) {
          return true;
        }
        if (cnpjController.text != (hospital.get<String>('cnpj') ?? '')) {
          return true;
        }
        if (tipoController.text != (hospital.get<String>('tipo') ?? '')) {
          return true;
        }
        if (telefoneController.text !=
            (hospital.get<String>('telefone') ?? '')) {
          return true;
        }
        if (enderecoController.text !=
            (hospital.get<String>('endereco') ?? '')) {
          return true;
        }

        // Verificar coordenadas (com tolerância para diferenças de precisão)
        final dbLat = hospital.get<double>('latitude');
        final dbLng = hospital.get<double>('longitude');

        if (dbLat != null && dbLng != null) {
          try {
            final formLat = double.parse(latitudeController.text);
            final formLng = double.parse(longitudeController.text);

            // Comparar com tolerância
            const tolerance = 0.0000001;
            if ((formLat - dbLat).abs() > tolerance) return true;
            if ((formLng - dbLng).abs() > tolerance) return true;
          } catch (e) {
            // Se não conseguir converter, considere que houve alteração
            return true;
          }
        } else {
          // Se as coordenadas estiverem vazias no banco mas preenchidas no form
          if (latitudeController.text.isNotEmpty ||
              longitudeController.text.isNotEmpty) {
            return true;
          }
        }

        // Verificar se a senha foi alterada
        if (senhaController.text.isNotEmpty) return true;
      }
    }

    return false;
  }

  // -------------------------------------------------
  // Métodos para gerenciamento de hospitais
  // -------------------------------------------------

  Future<void> carregarHospitais() async {
    try {
      isLoading.value = true;
      error.value = '';

      // Criar query para buscar todos os hospitais
      final QueryBuilder<ParseObject> query = QueryBuilder<ParseObject>(
        ParseObject('consultorio'),
      );

      // Ordenar por nome
      query.orderByAscending('nome');

      final response = await query.query();

      if (response.success && response.results != null) {
        hospitais.value = List<ParseObject>.from(response.results!);
      } else {
        throw Exception(
          response.error?.message ?? 'Erro ao carregar hospitais',
        );
      }
    } catch (e) {
      error.value = e.toString();
      debugPrint('Erro ao carregar hospitais: $e');
    } finally {
      isLoading.value = false;
    }
  }

  Future<bool> cadastrarHospital() async {
    try {
      isLoading.value = true;
      error.value = '';

      // Validar dados (removendo a verificação do campo endereço)
      if (nomeController.text.isEmpty ||
          emailController.text.isEmpty ||
          cnpjController.text.isEmpty ||
          (!isEditMode.value && senhaController.text.isEmpty) ||
          tipoController.text.isEmpty ||
          telefoneController.text.isEmpty ||
          latitudeController.text.isEmpty ||
          longitudeController.text.isEmpty) {
        error.value = 'Todos os campos são obrigatórios';
        return false;
      }

      // Verificar se estamos editando ou criando
      if (isEditMode.value && currentHospitalId.value.isNotEmpty) {
        return await _atualizarHospital();
      } else {
        return await _criarNovoHospital();
      }
    } catch (e) {
      error.value = e.toString().replaceAll('Exception: ', '');
      debugPrint('Erro ao cadastrar hospital: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  Future<bool> _criarNovoHospital() async {
    // Verificar se o email já está em uso
    final queryEmail = QueryBuilder<ParseUser>(ParseUser.forQuery())
      ..whereEqualTo('email', emailController.text.trim());

    final emailResponse = await queryEmail.query();

    if (emailResponse.success &&
        emailResponse.results != null &&
        emailResponse.results!.isNotEmpty) {
      error.value = 'Este e-mail já está em uso';
      return false;
    }

    // Criar usuário no Parse
    final user = ParseUser(
      emailController.text.trim(), // username
      senhaController.text.trim(), // password
      emailController.text.trim(), // email
    )
      ..set('tipo', 'consultorio')
      ..set('dataCadastro', DateTime.now());

    final userResult = await user.signUp();

    if (!userResult.success) {
      throw Exception(userResult.error?.message ?? 'Erro ao criar usuário');
    }

    // Criar o hospital vinculado ao usuário (remover campo endereço)
    final hospital = ParseObject('consultorio')
      ..set('nome', nomeController.text.trim())
      ..set('cnpj', cnpjController.text.trim())
      ..set('tipo', tipoController.text.trim())
      ..set('telefone', telefoneController.text.trim())
      ..set('latitude', double.parse(latitudeController.text.trim()))
      ..set('longitude', double.parse(longitudeController.text.trim()))
      ..set('ativo', true)
      ..set('dataCadastro', DateTime.now())
      ..set('user_consultorio', user);

    final hospitalResult = await hospital.save();

    if (!hospitalResult.success) {
      // Se falhar, tentar excluir o usuário criado para evitar inconsistências
      await user.destroy();
      throw Exception(
        hospitalResult.error?.message ?? 'Erro ao criar hospital',
      );
    }

    // Limpar campos do formulário
    limparFormulario();

    // Recarregar lista de hospitais
    await carregarHospitais();

    successMessage.value = 'Hospital cadastrado com sucesso!';
    return true;
  }

  Future<bool> _atualizarHospital() async {
    try {
      if (emailController.text.isEmpty) {
        error.value = 'O e-mail é obrigatório';
        return false;
      }

      // Buscar o hospital existente
      final hospital = ParseObject('consultorio')
        ..objectId = currentHospitalId.value;

      // Atualizar os dados do hospital
      hospital
        ..set('nome', nomeController.text.trim())
        ..set('cnpj', cnpjController.text.trim())
        ..set('tipo', tipoController.text.trim())
        ..set('telefone', telefoneController.text.trim())
        ..set('latitude', double.parse(latitudeController.text.trim()))
        ..set('longitude', double.parse(longitudeController.text.trim()));

      final hospitalResult = await hospital.save();

      if (!hospitalResult.success) {
        throw Exception(
          hospitalResult.error?.message ?? 'Erro ao atualizar hospital',
        );
      }

      // Buscar o hospital de novo para garantir que estamos com os dados atualizados
      final queryHospital =
          QueryBuilder<ParseObject>(ParseObject('consultorio'))
            ..whereEqualTo('objectId', currentHospitalId.value)
            ..includeObject(['user_consultorio']);

      final response = await queryHospital.query();

      if (!response.success ||
          response.results == null ||
          response.results!.isEmpty) {
        throw Exception('Falha ao atualizar: Hospital não encontrado');
      }

      final hospitalAtual = response.results!.first;
      final user = hospitalAtual.get<ParseUser>('user_consultorio');

      // Verificar se existe usuário associado
      if (user == null) {
        // Criar um novo usuário já que não existe
        if (senhaController.text.isEmpty) {
          // Se não tiver senha definida, gerar uma
          gerarSenhaPadrao();
        }

        // Criar usuário no Parse
        final newUser = ParseUser(
          emailController.text.trim(), // username
          senhaController.text.trim(), // password
          emailController.text.trim(), // email
        )
          ..set('tipo', 'consultorio')
          ..set('dataCadastro', DateTime.now());

        final userResult = await newUser.signUp();

        if (!userResult.success) {
          throw Exception(userResult.error?.message ?? 'Erro ao criar usuário');
        }

        // Vincular o novo usuário ao hospital
        hospital.set('user_consultorio', newUser);
        await hospital.save();

        successMessage.value =
            'Hospital atualizado e novo usuário criado com sucesso!';

        // Limpar campos do formulário
        limparFormulario();

        // Recarregar lista de hospitais
        await carregarHospitais();

        return true;
      }

      // Atualizar email do usuário - usando método mais robusto
      if (user.emailAddress != emailController.text.trim()) {
        // Crie um objeto ParseUser com o objectId correto
        final userToUpdate = ParseUser.forQuery()..objectId = user.objectId;

        // Defina o novo email
        userToUpdate.emailAddress = emailController.text.trim();

        // Salve as alterações
        final userResult = await userToUpdate.save();
        if (!userResult.success) {
          throw Exception(
            'Erro ao atualizar email: ${userResult.error?.message}',
          );
        }
      }

      // Atualizar senha se uma nova senha foi fornecida
      if (senhaController.text.isNotEmpty) {
        // Criar um objeto ParseUser com o objectId correto para atualizar a senha
        final userToUpdate = ParseUser.forQuery()
          ..objectId = user.objectId
          ..password = senhaController.text.trim();

        final passwordResult = await userToUpdate.save();
        if (!passwordResult.success) {
          throw Exception(
            'Erro ao atualizar senha: ${passwordResult.error?.message}',
          );
        }
      }

      // Limpar campos do formulário
      limparFormulario();

      // Recarregar lista de hospitais
      await carregarHospitais();

      successMessage.value = 'Hospital atualizado com sucesso!';
      return true;
    } catch (e) {
      error.value = e.toString().replaceAll('Exception: ', '');
      debugPrint('Erro ao atualizar hospital: $e');
      return false;
    }
  }

  Future<bool> atualizarStatusHospital(
    String hospitalId,
    bool novoStatus,
  ) async {
    try {
      isLoading.value = true;

      final hospital = ParseObject('consultorio')
        ..objectId = hospitalId
        ..set('ativo', novoStatus);

      final result = await hospital.save();

      if (result.success) {
        // Atualizar a lista local de forma que o GetX detecte a mudança
        final index = hospitais.indexWhere((h) => h.objectId == hospitalId);
        if (index >= 0) {
          // Criar uma cópia do objeto atual
          final hospitalAtualizado = ParseObject('consultorio')
            ..objectId = hospitalId;

          // Buscar o hospital atualizado do servidor para ter todos os dados atualizados
          await hospitalAtualizado.fetch();

          // Substituir o objeto na lista
          hospitais[index] = hospitalAtualizado;

          // Forçar atualização da UI - crítico para a atualização visual
          hospitais.refresh();

          // Adicionar log para debug
          debugPrint(
              'Status do hospital $hospitalId atualizado para: $novoStatus');
        }

        successMessage.value = novoStatus
            ? 'Hospital ativado com sucesso!'
            : 'Hospital desativado com sucesso!';
        return true;
      } else {
        throw Exception(result.error?.message ?? 'Erro ao atualizar status');
      }
    } catch (e) {
      error.value = e.toString().replaceAll('Exception: ', '');
      debugPrint('Erro ao atualizar status: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // -------------------------------------------------
  // Métodos para localização e mapas
  // -------------------------------------------------

  Future<void> obterLocalizacaoAtual() async {
    try {
      loadingLocation.value = true;

      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        throw Exception('Serviços de localização desativados');
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw Exception('Permissão de localização negada');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw Exception('Permissão de localização permanentemente negada');
      }

      // Obter a posição atual
      final position = await Geolocator.getCurrentPosition();
      setLocalizacao(LatLng(position.latitude, position.longitude));

      // Obter o endereço a partir das coordenadas
      await obterEnderecoDaCoordenada(
        LatLng(position.latitude, position.longitude),
      );
    } catch (e) {
      error.value = 'Erro ao obter localização: $e';
      debugPrint('Erro ao obter localização: $e');
    } finally {
      loadingLocation.value = false;
    }
  }

  Future<void> obterEnderecoDaCoordenada(LatLng coordenada) async {
    try {
      final placemarks = await placemarkFromCoordinates(
        coordenada.latitude,
        coordenada.longitude,
      );

      if (placemarks.isNotEmpty) {
        final place = placemarks.first;
        final endereco =
            '${place.thoroughfare ?? ''} ${place.subThoroughfare ?? ''}, '
            '${place.subLocality ?? ''}, ${place.locality ?? ''}, '
            '${place.administrativeArea ?? ''} ${place.postalCode ?? ''}, '
            '${place.country ?? ''}';

        enderecoController.text = endereco
            .trim()
            .replaceAll(RegExp(r', ,'), ',')
            .replaceAll(RegExp(r'^ ,|, $'), '');
      }
    } catch (e) {
      debugPrint('Erro ao obter endereço: $e');
    }
  }

  Future<void> obterCoordenadasDoEndereco(String endereco) async {
    try {
      loadingLocation.value = true;

      final locations = await locationFromAddress(endereco);

      if (locations.isNotEmpty) {
        final location = locations.first;
        setLocalizacao(LatLng(location.latitude, location.longitude));
      }
    } catch (e) {
      error.value = 'Erro ao obter coordenadas: $e';
      debugPrint('Erro ao obter coordenadas: $e');
    } finally {
      loadingLocation.value = false;
    }
  }

  void setLocalizacao(LatLng position) {
    selectedLocation.value = position;
    latitudeController.text = position.latitude.toString();
    longitudeController.text = position.longitude.toString();

    markers.clear();
    markers.add(
      Marker(
        markerId: const MarkerId('selected_location'),
        position: position,
        draggable: true,
        onDragEnd: (newPosition) {
          setLocalizacao(newPosition);
          obterEnderecoDaCoordenada(newPosition);
        },
      ),
    );
  }

  // Atualiza os marcadores quando a localização é alterada
  void updateMarkers() {
    if (selectedLocation.value == null) {
      markers.clear();
      return;
    }

    markers.clear();
    markers.add(
      Marker(
        markerId: const MarkerId('selected_location'),
        position: selectedLocation.value!,
        draggable: true,
        onDragEnd: (newPosition) {
          // Atualizar coordenadas
          latitudeController.text = newPosition.latitude.toString();
          longitudeController.text = newPosition.longitude.toString();
          selectedLocation.value = newPosition;
        },
      ),
    );
  }

  // -------------------------------------------------
  // Métodos de gerenciamento do formulário
  // -------------------------------------------------

  void limparFormulario() {
    nomeController.clear();
    emailController.clear();
    cnpjController.clear();
    gerarSenhaPadrao(); // Regenerar senha
    tipoController.text = '';
    telefoneController.clear();
    enderecoController.clear(); // Ainda limpa, mas não usamos no cadastro
    latitudeController.clear();
    longitudeController.clear();
    selectedLocation.value = null;
    markers.clear();
    isEditMode.value = false;
    currentHospitalId.value = '';
  }

  void prepararEdicao(ParseObject hospital) {
    currentHospitalId.value = hospital.objectId!;
    isEditMode.value = true;

    nomeController.text = hospital.get<String>('nome') ?? '';
    cnpjController.text = hospital.get<String>('cnpj') ?? '';
    tipoController.text = hospital.get<String>('tipo') ?? '';
    telefoneController.text = hospital.get<String>('telefone') ?? '';
    enderecoController.text = hospital.get<String>('endereco') ?? '';

    final latitude = hospital.get<double>('latitude');
    final longitude = hospital.get<double>('longitude');

    if (latitude != null && longitude != null) {
      setLocalizacao(LatLng(latitude, longitude));
    }

    // Buscar o email do usuário associado com uma query completa
    _carregarDadosUsuarioAssociado(hospital);

    // Limpar campo de senha quando editando
    senhaController.clear();
  }

  Future<void> prepararEdicaoHospital(ParseObject hospital) async {
    try {
      isLoading.value = true;
      error.value = '';

      // Buscar email diretamente na tabela User
      final emailDireto = await buscarEmailDiretoTabelaUser(hospital.objectId!);
      if (emailDireto != null && emailDireto.isNotEmpty) {
        emailController.text = emailDireto;
      }

      // Buscar detalhes completos do hospital
      final query = QueryBuilder<ParseObject>(ParseObject('consultorio'))
        ..whereEqualTo('objectId', hospital.objectId)
        ..includeObject(['user_consultorio']);

      final response = await query.query();
      if (!response.success ||
          response.results == null ||
          response.results!.isEmpty) {
        throw Exception('Hospital não encontrado');
      }

      final hospitalDetail = response.results!.first;

      // Buscar usuário associado
      final user = hospitalDetail.get<ParseUser>('user_consultorio');
      if (user != null && emailController.text.isEmpty) {
        if (user.emailAddress != null && user.emailAddress!.isNotEmpty) {
          emailController.text = user.emailAddress!;
        } else if (user.get<String>('email') != null) {
          emailController.text = user.get<String>('email')!;
        } else if (user.username != null) {
          emailController.text = user.username!;
        }
      }

      // Preencher o formulário com os dados do hospital
      await prepararEdicaoCompleta(hospital);

      // Configurar modo de edição
      isEditMode.value = true;
      currentHospitalId.value = hospital.objectId!;

      // Definir localização
      if (latitudeController.text.isNotEmpty &&
          longitudeController.text.isNotEmpty) {
        try {
          final lat = double.parse(latitudeController.text);
          final lng = double.parse(longitudeController.text);
          selectedLocation.value = LatLng(lat, lng);
          updateMarkers();
        } catch (e) {
          // Ignorar erro de parse e tentar obter as coordenadas diretamente do objeto
          final latitude = hospitalDetail.get<double>('latitude');
          final longitude = hospitalDetail.get<double>('longitude');
          if (latitude != null && longitude != null) {
            latitudeController.text = latitude.toString();
            longitudeController.text = longitude.toString();
            selectedLocation.value = LatLng(latitude, longitude);
            updateMarkers();
          }
        }
      }

      // SOLUÇÃO: Navegar para a tela de edição após carregar os dados
      // Usamos Get.find para obter a instância atual da AdminScreen
      final currentView = Get.find<RxInt>(tag: 'currentView');
      if (currentView != null) {
        currentView.value = 1; // Trocar para a tela de formulário
      } else {
        // Log de erro caso não encontre a variável
        debugPrint(
            'Não foi possível mudar para a tela de edição - currentView não encontrado');
      }
    } catch (e) {
      error.value = e.toString();
    } finally {
      isLoading.value = false;
    }
  }

  // Método mais completo para preparar edição com async/await
  Future<void> prepararEdicaoCompleta(ParseObject hospital) async {
    try {
      isLoading.value = true;
      error.value = '';

      // Definir modo de edição e ID
      currentHospitalId.value = hospital.objectId!;
      isEditMode.value = true;

      debugPrint('Preparando edição para o hospital ${hospital.objectId}');

      // Buscar hospital completo com include para ter todos os dados
      final queryHospital =
          QueryBuilder<ParseObject>(ParseObject('consultorio'))
            ..whereEqualTo('objectId', hospital.objectId)
            ..includeObject(['user_consultorio']);

      final response = await queryHospital.query();

      debugPrint(
        'Query resposta: ${response.success}, resultados: ${response.results?.length}',
      );

      if (!response.success ||
          response.results == null ||
          response.results!.isEmpty) {
        throw Exception('Hospital não encontrado ou erro ao buscar dados.');
      }

      // Obter hospital com dados completos
      final hospitalCompleto = response.results!.first;

      // Preencher dados do hospital
      nomeController.text = hospitalCompleto.get<String>('nome') ?? '';
      cnpjController.text = hospitalCompleto.get<String>('cnpj') ?? '';
      tipoController.text = hospitalCompleto.get<String>('tipo') ?? '';
      telefoneController.text = hospitalCompleto.get<String>('telefone') ?? '';

      // Preencher dados de localização
      final latitude = hospitalCompleto.get<double>('latitude');
      final longitude = hospitalCompleto.get<double>('longitude');

      if (latitude != null && longitude != null) {
        latitudeController.text = latitude.toString();
        longitudeController.text = longitude.toString();
        setLocalizacao(LatLng(latitude, longitude));
      }

      // Buscar o usuário associado ao consultório
      ParseUser? userConsultorio = hospitalCompleto.get<ParseUser>('user_consultorio');
      
      // Se encontrar o usuário vinculado diretamente, usar seu email
      if (userConsultorio != null) {
        String? email = userConsultorio.emailAddress;
        
        // Tentar diferentes campos de email
        if (email == null || email.isEmpty) {
          email = userConsultorio.get<String>('email');
        }
        
        if (email == null || email.isEmpty) {
          email = userConsultorio.username;
        }
        
        if (email != null && email.isNotEmpty) {
          emailController.text = email;
          debugPrint('Email encontrado do user_consultorio: $email');
        }
        
        // Limpar a senha, pois já existe um usuário
        senhaController.clear();
      } else {
        // Se não encontrar usuário vinculado, buscar por outras estratégias
        String? emailEncontrado = await buscarEmailDiretoTabelaUser(hospital.objectId!);
        
        if (emailEncontrado != null && emailEncontrado.isNotEmpty) {
          emailController.text = emailEncontrado;
          debugPrint('Email encontrado por busca direta: $emailEncontrado');
          // Limpar a senha, pois provavelmente já existe um usuário
          senhaController.clear();
        } else {
          // Se nenhum email for encontrado, gerar uma senha para novo usuário
          emailController.clear();
          gerarSenhaPadrao();
          debugPrint('Nenhum email encontrado. Gerando senha para novo usuário.');
        }
      }

      debugPrint(
        'Formulário preenchido - Nome: ${nomeController.text}, CNPJ: ${cnpjController.text}, ' +
        'Tipo: ${tipoController.text}, Email: ${emailController.text}, ' +
        'Latitude: ${latitudeController.text}, Longitude: ${longitudeController.text}'
      );
      
    } catch (e) {
      error.value = 'Erro ao carregar dados para edição: $e';
      debugPrint('Erro ao carregar dados para edição: $e');
      limparFormulario();
      rethrow;
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> _carregarDadosUsuarioAssociado(ParseObject hospital) async {
    try {
      // Obter informações completas do usuário associado ao hospital
      final queryHospital =
          QueryBuilder<ParseObject>(ParseObject('consultorio'))
            ..whereEqualTo('objectId', hospital.objectId)
            ..includeObject(['user_consultorio']);

      final response = await queryHospital.query();

      if (response.success &&
          response.results != null &&
          response.results!.isNotEmpty) {
        final hospitalCompleto = response.results!.first;
        final user = hospitalCompleto.get<ParseUser>('user_consultorio');

        if (user != null) {
          emailController.text = user.emailAddress ?? '';
          // Outros campos do usuário se necessário
        }
      }
    } catch (e) {
      debugPrint('Erro ao carregar dados do usuário: $e');
    }
  }

  // -------------------------------------------------
  // Métodos de gerenciamento de usuários e credenciais
  // -------------------------------------------------

  Future<bool> copyToClipboard(String text) async {
    try {
      await Clipboard.setData(ClipboardData(text: text));
      return true;
    } catch (e) {
      error.value = 'Erro ao copiar para a área de transferência: $e';
      return false;
    }
  }

  Future<bool> reenviarCredenciais(String hospitalId) async {
    try {
      isLoading.value = true;
      error.value = '';
      debugPrint(
        'Iniciando reenvio de credenciais para hospital ID: $hospitalId',
      );

      // Primeiro, buscar o hospital para obter os dados necessários
      final queryHospital =
          QueryBuilder<ParseObject>(ParseObject('consultorio'))
            ..whereEqualTo('objectId', hospitalId)
            ..includeObject(['user_consultorio']);

      final response = await queryHospital.query();

      if (!response.success ||
          response.results == null ||
          response.results!.isEmpty) {
        throw Exception('Hospital não encontrado');
      }

      final hospital = response.results!.first;
      final user = hospital.get<ParseUser>('user_consultorio');
      final nomeHospital = hospital.get<String>('nome') ?? 'Hospital';

      String userId;

      // Se encontrou o usuário, obter seu ID
      if (user != null) {
        userId = user.objectId!;
      } else {
        // Se não encontrou usuário, verificar se conseguimos encontrar pelo email
        final email = await buscarEmailDiretoTabelaUser(hospitalId);

        if (email == null || email.isEmpty) {
          throw Exception(
            'Não foi possível identificar o usuário associado a este consultório',
          );
        }

        // Buscar ou criar usuário com este email
        final userQuery = QueryBuilder<ParseUser>(ParseUser.forQuery())
          ..whereEqualTo('email', email);

        final userResponse = await userQuery.query();

        if (userResponse.success &&
            userResponse.results != null &&
            userResponse.results!.isNotEmpty) {
          // Usar o usuário existente
          userId = (userResponse.results!.first as ParseUser).objectId!;
        } else {
          throw Exception(
            'Não foi possível encontrar um usuário associado a este consultório',
          );
        }
      }

      // Chamar a função Cloud para gerar o link de redefinição
      final params = <String, dynamic>{
        'userId': userId,
        'userType': 'consultorio',
      };

      final resetResponse = await ParseCloudFunction('generateResetLinkForUser')
          .execute(parameters: params);

      if (resetResponse.success) {
        final result = resetResponse.result as Map<String, dynamic>;

        if (result['success'] == true) {
          // Extrair email e link para mostrar ao usuário
          final email = result['email'] as String?;
          final resetLink = result['resetLink'] as String?;

          // Definir mensagem de sucesso com detalhes
          successMessage.value =
              'Link de redefinição de senha enviado com sucesso para o consultório "$nomeHospital".\n\n'
                      'Email: ${email ?? "Não disponível"}\n\n' +
                  (resetLink != null ? 'Link: $resetLink\n\n' : '') +
                  'O link é válido por 24 horas.';
          return true;
        } else {
          throw Exception(
              result['message'] ?? 'Erro ao gerar link de redefinição');
        }
      } else {
        throw Exception(
          'Erro ao chamar função de redefinição: ${resetResponse.error?.message}',
        );
      }
    } catch (e) {
      error.value = e.toString().replaceAll('Exception: ', '');
      debugPrint('Erro ao reenviar credenciais: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  Future<bool> redefinirSenhaUsuario(String hospitalId) async {
    try {
      isLoading.value = true;
      error.value = '';

      // Gerar nova senha aleatória
      final uuid = const Uuid().v4();
      final novaSenha = uuid.substring(0, 8);

      debugPrint(
        'Iniciando redefinição de senha para hospital ID: $hospitalId',
      );
      debugPrint('Nova senha gerada: $novaSenha');

      // Buscar o hospital
      final queryHospital =
          QueryBuilder<ParseObject>(ParseObject('consultorio'))
            ..whereEqualTo('objectId', hospitalId)
            ..includeObject(['user_consultorio']);

      final response = await queryHospital.query();

      if (!response.success ||
          response.results == null ||
          response.results!.isEmpty) {
        throw Exception('Hospital não encontrado');
      }

      final hospital = response.results!.first;
      String? email;
      String? username;
      ParseUser? userToUpdate;

      // Verificar se o hospital tem um usuário associado
      final user = hospital.get<ParseUser>('user_consultorio');

      if (user != null) {
        // Usar o usuário associado
        userToUpdate = ParseUser.forQuery()..objectId = user.objectId;
        email = user.emailAddress ?? user.get<String>('email') ?? user.username;
        username = user.username;
        debugPrint(
          'Usuário encontrado (ID: ${user.objectId}, username: $username)',
        );
      } else {
        // Se não tiver usuário associado, buscar por nome do hospital
        final nomeHospital = hospital.get<String>('nome') ?? '';
        debugPrint('Buscando usuário pelo nome do hospital: $nomeHospital');

        final queryUser = QueryBuilder<ParseUser>(ParseUser.forQuery())
          ..whereEqualTo('username', nomeHospital);

        final userResponse = await queryUser.query();

        if (userResponse.success &&
            userResponse.results != null &&
            userResponse.results!.isNotEmpty) {
          final foundUser = userResponse.results!.first as ParseUser;
          userToUpdate = ParseUser.forQuery()..objectId = foundUser.objectId;
          email = foundUser.emailAddress ??
              foundUser.get<String>('email') ??
              foundUser.username;
          username = foundUser.username;
          debugPrint(
            'Usuário encontrado pelo nome (ID: ${foundUser.objectId}, username: $username)',
          );
        } else {
          throw Exception(
            'Não foi possível encontrar um usuário associado a este hospital',
          );
        }
      }

      // Definir a nova senha
      userToUpdate.password = novaSenha;

      // Salvar a alteração
      final updateResult = await userToUpdate.save();

      if (!updateResult.success) {
        throw Exception(
          updateResult.error?.message ?? 'Erro ao atualizar senha',
        );
      }

      // Tudo OK, retornar a nova senha
      successMessage.value =
          'Senha redefinida com sucesso para "${hospital.get<String>('nome')}".\n\nNOVA SENHA: $novaSenha\n\nEmail do usuário: $email';
      return true;
    } catch (e) {
      error.value = e.toString().replaceAll('Exception: ', '');
      debugPrint('Erro ao redefinir senha: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  Future<Map<String, dynamic>?> gerarNovaCredencial(String hospitalId) async {
    try {
      isLoading.value = true;
      error.value = '';
      debugPrint(
        'Iniciando geração de nova credencial para hospital ID: $hospitalId',
      );

      // 1. Buscar o hospital e seu nome
      final queryHospital =
          QueryBuilder<ParseObject>(ParseObject('consultorio'))
            ..whereEqualTo('objectId', hospitalId)
            ..includeObject(['user_consultorio']);

      final response = await queryHospital.query();

      if (!response.success ||
          response.results == null ||
          response.results!.isEmpty) {
        throw Exception('Hospital não encontrado');
      }

      final hospital = response.results!.first;
      final nome = hospital.get<String>('nome') ?? 'Hospital';
      debugPrint('Nome do hospital: $nome');

      // 2. Verificar se há user_consultorio diretamente
      String novaSenha = _gerarSenhaAleatoria();
      ParseUser? user = hospital.get<ParseUser>('user_consultorio');
      String? email;

      if (user != null) {
        // Se já tem user_consultorio, usar ele
        debugPrint(
          'Usuário encontrado diretamente no user_consultorio: ${user.objectId}',
        );
        email = user.emailAddress ?? user.get<String>('email') ?? user.username;

        // Atualizar senha
        user.password = novaSenha;
        final userUpdateResult = await user.save();

        if (!userUpdateResult.success) {
          throw Exception(
            'Erro ao atualizar senha: ${userUpdateResult.error?.message}',
          );
        }

        debugPrint('Senha atualizada com sucesso para usuário existente');
      } else {
        // Se não tem user_consultorio direto, buscar usuario pelo nome/email
        email = await buscarEmailDiretoTabelaUser(hospitalId);

        if (email != null && email.isNotEmpty) {
          // Tentar encontrar o usuário
          final queryUser = QueryBuilder<ParseUser>(ParseUser.forQuery())
            ..whereEqualTo('email', email);

          final userResponse = await queryUser.query();

          if (userResponse.success &&
              userResponse.results != null &&
              userResponse.results!.isNotEmpty) {
            user = userResponse.results!.first as ParseUser;
            debugPrint('Usuário encontrado por email: ${user.objectId}');

            // Atualizar a senha
            user.password = novaSenha;
            final userUpdateResult = await user.save();

            if (!userUpdateResult.success) {
              throw Exception(
                'Erro ao atualizar senha: ${userUpdateResult.error?.message}',
              );
            }

            // Vincular o usuário ao hospital se ainda não estiver vinculado
            hospital.set('user_consultorio', user);
            await hospital.save();

            debugPrint('Usuário vinculado ao hospital e senha atualizada');
          } else {
            // Criar novo usuário com o email encontrado
            debugPrint('Criando novo usuário com email encontrado: $email');

            user = ParseUser(
              nome, // username - usar o nome do hospital
              novaSenha, // password
              email, // email
            )
              ..set('tipo', 'consultorio')
              ..set('dataCadastro', DateTime.now());

            final userResult = await user.signUp();

            if (!userResult.success) {
              throw Exception(
                userResult.error?.message ?? 'Erro ao criar usuário',
              );
            }

            // Vincular o novo usuário ao hospital
            hospital.set('user_consultorio', user);
            final hospitalUpdateResult = await hospital.save();

            if (!hospitalUpdateResult.success) {
              throw Exception(
                'Erro ao vincular usuário ao hospital: ${hospitalUpdateResult.error?.message}',
              );
            }

            debugPrint('Novo usuário criado e vinculado ao hospital');
          }
        } else {
          // Não encontrou email, criar um com nome do hospital
          email = '${nome.toLowerCase().replaceAll(' ', '_')}@exemplo.com';
          debugPrint('Criando novo usuário com email padrão: $email');

          user = ParseUser(
            nome, // username
            novaSenha, // password
            email, // email
          )
            ..set('tipo', 'consultorio')
            ..set('dataCadastro', DateTime.now());

          final userResult = await user.signUp();

          if (!userResult.success) {
            throw Exception(
              userResult.error?.message ?? 'Erro ao criar usuário',
            );
          }

          // Vincular o novo usuário ao hospital
          hospital.set('user_consultorio', user);
          final hospitalUpdateResult = await hospital.save();

          if (!hospitalUpdateResult.success) {
            throw Exception(
              'Erro ao vincular usuário ao hospital: ${hospitalUpdateResult.error?.message}',
            );
          }

          debugPrint(
            'Novo usuário criado com email padrão e vinculado ao hospital',
          );
        }
      }

      // Retornar as credenciais geradas
      return {'email': email, 'senha': novaSenha, 'nome': nome};
    } catch (e) {
      error.value = e.toString().replaceAll('Exception: ', '');
      debugPrint('Erro ao gerar nova credencial: $e');
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  // -------------------------------------------------
  // Métodos de busca de email
  // -------------------------------------------------

  Future<void> buscarEmailConsultorio(String hospitalId) async {
    try {
      debugPrint('Buscando email para consultório ID: $hospitalId');

      // Limpar qualquer erro anterior
      error.value = '';

      // Passo 1: Buscar o consultório diretamente
      final QueryBuilder<ParseObject> queryConsultorio =
          QueryBuilder<ParseObject>(ParseObject('consultorio'));
      queryConsultorio.whereEqualTo('objectId', hospitalId);
      queryConsultorio.includeObject(['user_consultorio']);

      final response = await queryConsultorio.query();

      if (response.success &&
          response.results != null &&
          response.results!.isNotEmpty) {
        final consultorio = response.results!.first;

        // Passo 2: Verificar se tem campo user_consultorio
        final userPointer = consultorio.get<ParseObject>('user_consultorio');

        if (userPointer != null) {
          debugPrint(
            'Consultório tem user_consultorio: ${userPointer.objectId}',
          );

          // Passo 3: Buscar o usuário completo
          final QueryBuilder<ParseUser> userQuery = QueryBuilder<ParseUser>(
            ParseUser.forQuery(),
          );
          userQuery.whereEqualTo('objectId', userPointer.objectId);

          final userResponse = await userQuery.query();

          if (userResponse.success &&
              userResponse.results != null &&
              userResponse.results!.isNotEmpty) {
            final user = userResponse.results!.first as ParseUser;

            // Verificar email ou username
            String? email = user.emailAddress;

            if (email == null || email.isEmpty) {
              email = user.get<String>('email');
            }

            if (email == null || email.isEmpty) {
              email = user.username;
            }

            if (email != null && email.isNotEmpty) {
              emailController.text = email;
              debugPrint('Email encontrado: $email');
              return;
            }
          }
        }

        // Se chegou aqui, não foi possível encontrar o email pelo user_consultorio
        debugPrint('Não foi possível encontrar email pelo user_consultorio');

        // MODIFICAÇÃO: Não gerar email automaticamente, deixar vazio
        // para permitir que o usuário insira um novo
        emailController.text = '';
        debugPrint(
          'Email não encontrado. Campo deixado vazio para entrada manual.',
        );
      } else {
        debugPrint('Erro ao buscar consultório: ${response.error?.message}');
      }
    } catch (e) {
      debugPrint('Erro ao buscar email: $e');
      error.value = 'Erro ao buscar email: $e';
    }
  }

  Future<String?> carregarEmailDoHospital(String hospitalId) async {
    try {
      debugPrint('Buscando email para o hospital ID: $hospitalId');

      // Abordagem 1: Buscar direto pelo hospital e incluir o user_consultorio
      final queryHospital =
          QueryBuilder<ParseObject>(ParseObject('consultorio'))
            ..whereEqualTo('objectId', hospitalId)
            ..includeObject(['user_consultorio']);

      final result = await queryHospital.query();

      if (result.success &&
          result.results != null &&
          result.results!.isNotEmpty) {
        final hospital = result.results!.first;
        final user = hospital.get<ParseUser>('user_consultorio');

        if (user != null) {
          final email =
              user.emailAddress ?? user.get<String>('email') ?? user.username;
          if (email != null && email.isNotEmpty) {
            debugPrint('Email encontrado (método 1): $email');
            return email;
          }
        }
      }

      // Abordagem 2: Buscar todos os usuários com tipo "consultorio" e verificar se algum está relacionado
      final queryUsers = QueryBuilder<ParseUser>(ParseUser.forQuery())
        ..whereEqualTo('tipo', 'consultorio');

      final usersResult = await queryUsers.query();

      if (usersResult.success && usersResult.results != null) {
        for (var userObject in usersResult.results!) {
          final user = userObject as ParseUser;

          // Verificar se este usuário está relacionado ao hospital
          final queryRelation =
              QueryBuilder<ParseObject>(ParseObject('consultorio'))
                ..whereEqualTo('objectId', hospitalId)
                ..whereEqualTo('user_consultorio', user.toPointer());

          final relationResult = await queryRelation.query();

          if (relationResult.success &&
              relationResult.results != null &&
              relationResult.results!.isNotEmpty) {
            final email =
                user.emailAddress ?? user.get<String>('email') ?? user.username;
            if (email != null && email.isNotEmpty) {
              debugPrint('Email encontrado (método 2): $email');
              return email;
            }
          }
        }
      }

      // Abordagem 3: Verificar se há secretárias vinculadas a este hospital
      final querySecretarias =
          QueryBuilder<ParseObject>(ParseObject('Secretaria'))
            ..whereEqualTo(
              'consultorio',
              (ParseObject('consultorio')..objectId = hospitalId).toPointer(),
            )
            ..includeObject(['user_secretaria']);

      final secretariasResult = await querySecretarias.query();

      if (secretariasResult.success &&
          secretariasResult.results != null &&
          secretariasResult.results!.isNotEmpty) {
        final secretaria = secretariasResult.results!.first;
        final secretariaUser = secretaria.get<ParseUser>('user_secretaria');

        if (secretariaUser != null) {
          final email = secretariaUser.emailAddress ??
              secretariaUser.get<String>('email') ??
              secretariaUser.username;
          if (email != null && email.isNotEmpty) {
            debugPrint('Email encontrado (método 3): $email');
            return email;
          }
        }
      }

      debugPrint('Nenhum email encontrado para o hospital $hospitalId');
      return null;
    } catch (e) {
      debugPrint('Erro ao buscar email: $e');
      return null;
    }
  }

  Future<String?> buscarEmailPorNomeConsultorio(String nomeConsultorio) async {
    try {
      debugPrint('Buscando usuário pelo nome do consultório: $nomeConsultorio');

      // Buscar usuário cujo username corresponde ao nome do consultório
      final queryUser = QueryBuilder<ParseUser>(ParseUser.forQuery())
        ..whereEqualTo('username', nomeConsultorio);

      final userResult = await queryUser.query();

      if (userResult.success &&
          userResult.results != null &&
          userResult.results!.isNotEmpty) {
        final user = userResult.results!.first as ParseUser;
        final email = user.emailAddress ?? user.get<String>('email');

        if (email != null && email.isNotEmpty) {
          debugPrint(
            'Email encontrado para o consultório "$nomeConsultorio": $email',
          );
          return email;
        }
      }

      // Se não encontrou com nome exato, tente buscar por uma correspondência parcial
      final queryUserPartial = QueryBuilder<ParseUser>(ParseUser.forQuery())
        ..whereContains('username', nomeConsultorio);

      final userPartialResult = await queryUserPartial.query();

      if (userPartialResult.success &&
          userPartialResult.results != null &&
          userPartialResult.results!.isNotEmpty) {
        // Iterar sobre resultados para encontrar a melhor correspondência
        for (var result in userPartialResult.results!) {
          final user = result as ParseUser;
          final username = user.username;

          // Se o username contém o nome do consultório, considere como correspondência
          if (username != null &&
              (username.contains(nomeConsultorio) ||
                  nomeConsultorio.contains(username))) {
            final email = user.emailAddress ?? user.get<String>('email');

            if (email != null && email.isNotEmpty) {
              debugPrint(
                'Email encontrado por correspondência parcial: $email (username: $username)',
              );
              return email;
            }
          }
        }
      }

      debugPrint(
        'Nenhum usuário encontrado com username correspondente a "$nomeConsultorio"',
      );
      return null;
    } catch (e) {
      debugPrint('Erro ao buscar email por nome do consultório: $e');
      return null;
    }
  }

  Future<String?> buscarEmailDiretoTabelaUser(String hospitalId) async {
    try {
      debugPrint(
        'Buscando usuário para hospital ID: $hospitalId com busca direta',
      );

      // 1. Primeiro, buscar o hospital e verificar se já tem user_consultorio
      final queryHospital =
          QueryBuilder<ParseObject>(ParseObject('consultorio'))
            ..whereEqualTo('objectId', hospitalId)
            ..includeObject(['user_consultorio']);

      final responseHospital = await queryHospital.query();

      if (!responseHospital.success ||
          responseHospital.results == null ||
          responseHospital.results!.isEmpty) {
        debugPrint('Hospital não encontrado com ID: $hospitalId');
        return null;
      }

      final hospital = responseHospital.results!.first;
      final userConsultorio = hospital.get<ParseUser>('user_consultorio');

      // Se o hospital já tem um usuário vinculado, usar o email dele
      if (userConsultorio != null) {
        final email = userConsultorio.emailAddress ??
            userConsultorio.get<String>('email');
        if (email != null && email.isNotEmpty) {
          debugPrint(
            'Email encontrado do user_consultorio diretamente: $email',
          );
          return email;
        }
      }

      // 2. Se não tem user_consultorio ou não tem email, buscar pelo nome do hospital
      final nomeHospital = hospital.get<String>('nome');

      if (nomeHospital == null || nomeHospital.isEmpty) {
        debugPrint('Hospital sem nome: $hospitalId');
        return null;
      }

      debugPrint('Nome do hospital encontrado: $nomeHospital');

      // 3. Buscar diretamente na tabela _User pelo username exatamente igual ao nome do hospital
      final queryExactUsername = QueryBuilder<ParseUser>(ParseUser.forQuery())
        ..whereEqualTo('username', nomeHospital);

      final responseExact = await queryExactUsername.query();

      if (responseExact.success &&
          responseExact.results != null &&
          responseExact.results!.isNotEmpty) {
        final user = responseExact.results!.first as ParseUser;
        final email = user.emailAddress ?? user.get<String>('email');

        if (email != null && email.isNotEmpty) {
          debugPrint('Email encontrado com username exato: $email');
          return email;
        }
      }

      // 4. Tentar buscar qualquer usuário com tipo "consultorio"
      final queryConsultorioType = QueryBuilder<ParseUser>(ParseUser.forQuery())
        ..whereEqualTo('tipo', 'consultorio');

      final responseConsultorioType = await queryConsultorioType.query();

      if (responseConsultorioType.success &&
          responseConsultorioType.results != null) {
        // Primeiro procurar por usuários que contenham o nome do hospital
        for (var userObject in responseConsultorioType.results!) {
          final user = userObject as ParseUser;
          final username = user.username ?? '';

          if (username.toLowerCase().contains(nomeHospital.toLowerCase()) ||
              nomeHospital.toLowerCase().contains(username.toLowerCase())) {
            final email = user.emailAddress ?? user.get<String>('email');
            if (email != null && email.isNotEmpty) {
              debugPrint(
                'Email encontrado de usuário tipo consultorio com nome similar: $email',
              );
              return email;
            }
          }
        }
      }

      // 5. ÚLTIMA OPÇÃO: verificar por secretárias vinculadas a este hospital
      // Colocando essa opção por último para priorizar o email do consultório
      final querySecretaria =
          QueryBuilder<ParseObject>(ParseObject('Secretaria'))
            ..whereEqualTo(
              'consultorio',
              (ParseObject('consultorio')..objectId = hospitalId).toPointer(),
            )
            ..includeObject(['user_secretaria']);

      final responseSecretaria = await querySecretaria.query();

      if (responseSecretaria.success &&
          responseSecretaria.results != null &&
          responseSecretaria.results!.isNotEmpty) {
        final secretaria = responseSecretaria.results!.first;
        final secretariaUser = secretaria.get<ParseUser>('user_secretaria');

        if (secretariaUser != null) {
          final email = secretariaUser.emailAddress ??
              secretariaUser.get<String>('email');

          if (email != null && email.isNotEmpty) {
            debugPrint(
              'Email encontrado através da secretária (ÚLTIMA OPÇÃO): $email',
            );
            return email;
          }
        }
      }

      // Nenhum email encontrado
      debugPrint('Nenhum email encontrado para o hospital $nomeHospital');
      return null;
    } catch (e) {
      debugPrint('Erro ao buscar email: $e');
      return null;
    }
  }

  // -------------------------------------------------
  // Outros métodos auxiliares
  // -------------------------------------------------

  // Método para excluir um hospital/consultório
  Future<bool> excluirHospital(String hospitalId) async {
    try {
      isLoading.value = true;
      error.value = '';

      // Verificar se há secretárias vinculadas ao consultório
      final querySecretaria = QueryBuilder<ParseObject>(
        ParseObject('Secretaria'),
      )..whereEqualTo(
          'consultorio',
          (ParseObject('consultorio')..objectId = hospitalId).toPointer(),
        );

      final secretariaResponse = await querySecretaria.query();

      if (secretariaResponse.success &&
          secretariaResponse.results != null &&
          secretariaResponse.results!.isNotEmpty) {
        error.value =
            'Não é possível excluir. Existem secretárias vinculadas a este consultório.';
        return false;
      }

      // Verificar se há médicos vinculados ao consultório
      final hospital = ParseObject('consultorio')..objectId = hospitalId;
      final result = await hospital.fetch();

      if ((result as ParseResponse).success) {
        final medicosVinculados = hospital.get<List>('medicos_vinculados');
        if (medicosVinculados != null && medicosVinculados.isNotEmpty) {
          error.value =
              'Não é possível excluir. Existem médicos vinculados a este consultório.';
          return false;
        }

        // Obter o usuário associado para excluí-lo também
        final userPointer = hospital.get<ParseUser>('user_consultorio');

        // Excluir o consultório primeiro
        final deleteResponse = await hospital.delete();

        if (deleteResponse.success) {
          // Se houver um usuário vinculado, excluí-lo também
          if (userPointer != null && userPointer.objectId != null) {
            final user = ParseUser.forQuery()..objectId = userPointer.objectId;
            await user.delete();
          }

          // Atualizar a lista de hospitais
          await carregarHospitais();
          return true;
        } else {
          error.value =
              'Erro ao excluir consultório: ${deleteResponse.error?.message}';
          return false;
        }
      } else {
        error.value = 'Consultório não encontrado';
        return false;
      }
    } catch (e) {
      error.value = e.toString().replaceAll('Exception: ', '');
      debugPrint('Erro ao excluir consultório: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  Future<List<ParseObject>> verificarSecretariasVinculadas(
    String hospitalId,
  ) async {
    try {
      // Criar query para buscar secretárias vinculadas a este consultório
      final QueryBuilder<ParseObject> query =
          QueryBuilder<ParseObject>(ParseObject('Secretaria'))
            ..whereEqualTo(
              'consultorio',
              (ParseObject('consultorio')..objectId = hospitalId).toPointer(),
            )
            ..includeObject(['user_secretaria']);

      final response = await query.query();

      if (response.success && response.results != null) {
        debugPrint(
          'Encontradas ${response.results!.length} secretárias para este consultório',
        );
        return List<ParseObject>.from(response.results!);
      } else {
        debugPrint(
          'Nenhuma secretária encontrada para este consultório ou erro na consulta',
        );
        return [];
      }
    } catch (e) {
      debugPrint('Erro ao verificar secretárias vinculadas: $e');
      return [];
    }
  }

  Future<void> logout() async {
    try {
      isLoading.value = true;
      final user = await ParseUser.currentUser() as ParseUser?;

      if (user != null) {
        await user.logout();
      }

      Get.offAllNamed('/login');
    } catch (e) {
      error.value = e.toString().replaceAll('Exception: ', '');
      debugPrint('Erro ao fazer logout: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Método para gerar uma senha aleatória
  String _gerarSenhaAleatoria() {
    const uuid = Uuid();
    return uuid.v4().substring(0, 8); // Primeiros 8 caracteres de um UUID
  }
}
