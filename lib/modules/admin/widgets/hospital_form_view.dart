import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'package:fila_app/modules/admin/controllers/admin_controller.dart';
import 'package:fila_app/theme/theme.dart';
import 'package:fila_app/theme/constants.dart';
import 'package:fila_app/modules/admin/widgets/map_picker_widget.dart';

class HospitalFormView extends StatefulWidget {
  final VoidCallback onFormSubmitted;

  const HospitalFormView({
    super.key,
    required this.onFormSubmitted,
  });

  @override
  State<HospitalFormView> createState() => _HospitalFormViewState();
}

class _HospitalFormViewState extends State<HospitalFormView> {
  final AdminController controller = Get.find<AdminController>();

  // Máscaras para formatação
  final MaskTextInputFormatter _cnpjFormatter = MaskTextInputFormatter(
    mask: '##.###.###/####-##',
    filter: {"#": RegExp(r'[0-9]')},
  );

  final MaskTextInputFormatter _telefoneFormatter = MaskTextInputFormatter(
    mask: '(##) #####-####',
    filter: {"#": RegExp(r'[0-9]')},
  );
  
  // Status de validação
  final Map<String, bool> _fieldErrors = {};
  
  // Foco para navegação entre campos
  final FocusNode _nomeFocus = FocusNode();
  final FocusNode _emailFocus = FocusNode();
  final FocusNode _senhaFocus = FocusNode();
  final FocusNode _cnpjFocus = FocusNode();
  final FocusNode _tipoFocus = FocusNode();
  final FocusNode _telefoneFocus = FocusNode();
  
  @override
  void initState() {
    super.initState();
    
    // Adicionar listeners para validação em tempo real
    controller.nomeController.addListener(() => _validateField('nome'));
    controller.emailController.addListener(() => _validateField('email'));
    controller.cnpjController.addListener(() => _validateField('cnpj'));
    controller.telefoneController.addListener(() => _validateField('telefone'));
  }
  
  @override
  void dispose() {
    // Limpar os listeners
    controller.nomeController.removeListener(() => _validateField('nome'));
    controller.emailController.removeListener(() => _validateField('email'));
    controller.cnpjController.removeListener(() => _validateField('cnpj'));
    controller.telefoneController.removeListener(() => _validateField('telefone'));
    
    // Dispose de todos os FocusNodes
    _nomeFocus.dispose();
    _emailFocus.dispose();
    _senhaFocus.dispose();
    _cnpjFocus.dispose();
    _tipoFocus.dispose();
    _telefoneFocus.dispose();
    
    super.dispose();
  }
  
  // Método para validação em tempo real dos campos
  void _validateField(String field) {
    setState(() {
      switch(field) {
        case 'nome':
          _fieldErrors[field] = controller.nomeController.text.isEmpty;
          break;
        case 'email':
          _fieldErrors[field] = controller.emailController.text.isEmpty || 
                              !GetUtils.isEmail(controller.emailController.text);
          break;
        case 'cnpj':
          final cnpjText = _cnpjFormatter.getUnmaskedText();
          _fieldErrors[field] = cnpjText.length != 14;
          break;
        case 'telefone':
          final telefoneText = _telefoneFormatter.getUnmaskedText();
          _fieldErrors[field] = telefoneText.length < 10 || telefoneText.length > 11;
          break;
        default:
          break;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.screenMargin),
      child: Obx(() => AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            child: Column(
              key: ValueKey<bool>(controller.isEditMode.value),
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildHeader(),
                const SizedBox(height: AppSpacing.md),
                _buildHospitalInfoCard(),
                const SizedBox(height: AppSpacing.md),
                _buildLocationCard(),
                const SizedBox(height: AppSpacing.lg),
                _buildSubmitButton(),
                const SizedBox(height: AppSpacing.md),
                if (controller.isEditMode.value) _buildCancelButton(),
                const SizedBox(height: AppSpacing.xl),
              ],
            ),
          )),
    );
  }
  
  Widget _buildHeader() {
    return Card(
      elevation: 2,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSpacing.borderRadius),
      ),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [AppTheme.primaryColor, AppTheme.primaryColor.withOpacity(0.7)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(AppSpacing.borderRadius),
        ),
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          children: [
            Icon(
              controller.isEditMode.value ? Icons.edit_location_alt : Icons.add_business,
              size: 48,
              color: Colors.white,
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              controller.isEditMode.value 
                  ? 'Editar Consultório'
                  : 'Cadastrar Novo Consultório',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            if (controller.isEditMode.value && controller.nomeController.text.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  controller.nomeController.text,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildHospitalInfoCard() {
    return Card(
      elevation: 2,
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSpacing.borderRadius),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.cardPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              children: [
                const Icon(Icons.business, color: AppTheme.primaryColor),
                const SizedBox(width: AppSpacing.sm),
                Text(
                  'Informações do Consultório',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: AppSpacing.md),
            _buildTextField(
              controller: controller.nomeController,
              focusNode: _nomeFocus,
              nextFocus: _emailFocus,
              label: 'Nome do Consultório',
              hint: 'Digite o nome do consultório',
              icon: Icons.local_hospital,
              required: true,
              hasError: _fieldErrors['nome'] == true,
              errorText: 'Nome é obrigatório',
            ),
            const SizedBox(height: AppSpacing.md),
            _buildTextField(
              controller: controller.emailController,
              focusNode: _emailFocus,
              nextFocus: _senhaFocus,
              label: 'Email',
              hint: 'Digite o email administrativo',
              icon: Icons.email,
              keyboardType: TextInputType.emailAddress,
              required: true,
              hasError: _fieldErrors['email'] == true,
              errorText: 'Email inválido',
            ),
            const SizedBox(height: AppSpacing.md),
            _buildTextField(
              controller: controller.senhaController,
              focusNode: _senhaFocus,
              nextFocus: _cnpjFocus,
              label: 'Senha Provisória',
              hint: controller.isEditMode.value
                  ? 'Digite nova senha (apenas se desejar alterar)'
                  : 'Digite uma senha provisória',
              icon: Icons.password,
              obscureText: true,
              required: !controller.isEditMode.value,
              showTogglePassword: true,
            ),
            const SizedBox(height: AppSpacing.md),
            _buildTextField(
              controller: controller.cnpjController,
              focusNode: _cnpjFocus,
              nextFocus: _tipoFocus,
              label: 'CNPJ',
              hint: 'Digite o CNPJ',
              icon: Icons.business,
              keyboardType: TextInputType.number,
              inputFormatters: [_cnpjFormatter],
              required: true,
              hasError: _fieldErrors['cnpj'] == true,
              errorText: 'CNPJ inválido',
            ),
            const SizedBox(height: AppSpacing.md),
            _buildDropDownField(
              controller: controller.tipoController,
              focusNode: _tipoFocus,
              nextFocus: _telefoneFocus,
              label: 'Tipo',
              hint: 'Selecione o tipo',
              icon: Icons.category,
              items: const ['Público', 'Particular'],
              required: true,
            ),
            const SizedBox(height: AppSpacing.md),
            _buildTextField(
              controller: controller.telefoneController,
              focusNode: _telefoneFocus,
              label: 'Telefone',
              hint: 'Digite o telefone',
              icon: Icons.phone,
              keyboardType: TextInputType.phone,
              inputFormatters: [_telefoneFormatter],
              required: true,
              hasError: _fieldErrors['telefone'] == true,
              errorText: 'Telefone inválido',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationCard() {
    return Card(
      elevation: 2,
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSpacing.borderRadius),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.cardPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              children: [
                const Icon(Icons.location_on, color: AppTheme.primaryColor),
                const SizedBox(width: AppSpacing.sm),
                Text(
                  'Localização do Consultório',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: AppSpacing.md),
            Obx(() => FilledButton.icon(
                  onPressed: controller.loadingLocation.value
                      ? null
                      : controller.obterLocalizacaoAtual,
                  icon: controller.loadingLocation.value
                      ? const SizedBox(
                          width: 18,
                          height: 18,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        )
                      : const Icon(Icons.my_location),
                  label: Text(
                    controller.loadingLocation.value
                        ? 'Obtendo localização...'
                        : 'Obter localização atual',
                  ),
                  style: FilledButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.black87,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                )),
            const SizedBox(height: AppSpacing.md),
            Obx(() => MapPickerWidget(
                  latitude: controller.latitudeController.text.isEmpty
                      ? null
                      : double.tryParse(controller.latitudeController.text),
                  longitude: controller.longitudeController.text.isEmpty
                      ? null
                      : double.tryParse(controller.longitudeController.text),
                  onLocationSelected: (lat, lng) {
                    controller.latitudeController.text = lat.toString();
                    controller.longitudeController.text = lng.toString();
                  },
                  isLoading: controller.loadingLocation.value,
                )),
            const SizedBox(height: AppSpacing.md),
            Row(
              children: [
                Expanded(
                  child: _buildTextField(
                    controller: controller.latitudeController,
                    label: 'Latitude',
                    hint: 'Latitude',
                    icon: Icons.location_on,
                    keyboardType: const TextInputType.numberWithOptions(
                      decimal: true,
                    ),
                    required: true,
                    readOnly: true,
                  ),
                ),
                const SizedBox(width: AppSpacing.md),
                Expanded(
                  child: _buildTextField(
                    controller: controller.longitudeController,
                    label: 'Longitude',
                    hint: 'Longitude',
                    icon: Icons.location_on,
                    keyboardType: const TextInputType.numberWithOptions(
                      decimal: true,
                    ),
                    required: true,
                    readOnly: true,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubmitButton() {
    return Obx(() => FilledButton.icon(
          onPressed: controller.isLoading.value ? null : _submitForm,
          icon: controller.isLoading.value
              ? const SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : Icon(
                  controller.isEditMode.value ? Icons.save : Icons.add_business,
                ),
          label: Text(
            controller.isEditMode.value
                ? 'Atualizar Consultório'
                : 'Cadastrar Consultório',
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
                  color: Colors.white,
                ),
          ),
          style: FilledButton.styleFrom(
            backgroundColor: AppTheme.primaryColor,
            foregroundColor: Colors.black87,
            padding: const EdgeInsets.symmetric(vertical: 16),
            minimumSize: const Size(double.infinity, 56),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppSpacing.borderRadius),
            ),
            elevation: 3,
          ),
        ));
  }

  Widget _buildCancelButton() {
    return OutlinedButton.icon(
      onPressed: () {
        controller.limparFormulario();
      },
      style: OutlinedButton.styleFrom(
        foregroundColor: Colors.red,
        side: const BorderSide(color: Colors.red),
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSpacing.borderRadius),
        ),
      ),
      icon: const Icon(Icons.cancel),
      label: const Text('Cancelar Edição'),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    FocusNode? focusNode,
    FocusNode? nextFocus,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    bool obscureText = false,
    bool required = false,
    bool readOnly = false,
    int? minLines,
    int? maxLines,
    bool hasError = false,
    String errorText = '',
    bool showTogglePassword = false,
  }) {
    final RxBool _obscureText = obscureText.obs;
    
    return Obx(() => AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      margin: const EdgeInsets.only(bottom: 4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextField(
            controller: controller,
            focusNode: focusNode,
            onSubmitted: (_) {
              if (nextFocus != null) {
                FocusScope.of(context).requestFocus(nextFocus);
              }
            },
            decoration: InputDecoration(
              labelText: required ? '$label *' : label,
              hintText: hint,
              prefixIcon: Icon(icon, color: hasError ? Colors.red : AppTheme.primaryColor),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppSpacing.borderRadius),
                borderSide: BorderSide(
                  color: hasError ? Colors.red : Colors.grey[300]!
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppSpacing.borderRadius),
                borderSide: BorderSide(
                  color: hasError ? Colors.red : Colors.grey[300]!
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppSpacing.borderRadius),
                borderSide: BorderSide(
                  color: hasError ? Colors.red : AppTheme.primaryColor,
                  width: 2
                ),
              ),
              filled: true,
              fillColor: readOnly ? Colors.grey[100] : Colors.white,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: AppSpacing.md,
                vertical: AppSpacing.md,
              ),
              suffixIcon: showTogglePassword
                  ? IconButton(
                      icon: Icon(
                        _obscureText.value ? Icons.visibility : Icons.visibility_off,
                        color: Colors.grey[600],
                      ),
                      onPressed: () => _obscureText.value = !_obscureText.value,
                    )
                  : readOnly
                      ? const Icon(Icons.lock, size: 16, color: Colors.grey)
                      : null,
              errorStyle: const TextStyle(height: 0), // Hide the default error
            ),
            keyboardType: keyboardType,
            inputFormatters: inputFormatters,
            obscureText: showTogglePassword ? _obscureText.value : obscureText,
            readOnly: readOnly,
            minLines: minLines,
            maxLines: maxLines ?? 1,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: readOnly ? Colors.grey[700] : Colors.black,
                ),
          ),
          if (hasError && errorText.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(left: 12, top: 4),
              child: Text(
                errorText,
                style: const TextStyle(
                  color: Colors.red,
                  fontSize: 12,
                ),
              ),
            ),
        ],
      ),
    ));
  }

  Widget _buildDropDownField({
    required TextEditingController controller,
    FocusNode? focusNode,
    FocusNode? nextFocus,
    required String label,
    required String hint,
    required IconData icon,
    required List<String> items,
    bool required = false,
  }) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      margin: const EdgeInsets.only(bottom: 12),
      child: DropdownButtonFormField<String>(
        value: controller.text.isNotEmpty ? controller.text : null,
        focusNode: focusNode,
        onChanged: (value) {
          if (value != null) {
            controller.text = value;
            if (nextFocus != null) {
              FocusScope.of(context).requestFocus(nextFocus);
            }
          }
        },
        decoration: InputDecoration(
          labelText: required ? '$label *' : label,
          prefixIcon: Icon(icon, color: AppTheme.primaryColor),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppSpacing.borderRadius),
            borderSide: BorderSide(color: Colors.grey[300]!),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppSpacing.borderRadius),
            borderSide: BorderSide(color: Colors.grey[300]!),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppSpacing.borderRadius),
            borderSide:
                const BorderSide(color: AppTheme.primaryColor, width: 2),
          ),
          filled: true,
          fillColor: Colors.white,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: AppSpacing.md,
            vertical: AppSpacing.md,
          ),
        ),
        hint: Text(hint),
        isExpanded: true,
        icon: const Icon(Icons.arrow_drop_down, color: AppTheme.primaryColor),
        dropdownColor: Colors.white,
        items: items.map((String value) {
          return DropdownMenuItem<String>(
            value: value,
            child: Text(value),
          );
        }).toList(),
      ),
    );
  }

  void _submitForm() async {
    // Executa todas as validações
    _validateField('nome');
    _validateField('email');
    _validateField('cnpj');
    _validateField('telefone');
    
    // Verifica se há erros
    if (_fieldErrors.containsValue(true)) {
      _showSnackBar('Por favor, corrija os campos com erro antes de continuar', isError: true);
      return;
    }

    // Validação de campos obrigatórios
    if (controller.nomeController.text.isEmpty ||
        (!controller.isEditMode.value &&
            controller.senhaController.text.isEmpty) ||
        controller.cnpjController.text.isEmpty ||
        controller.tipoController.text.isEmpty ||
        controller.telefoneController.text.isEmpty ||
        controller.latitudeController.text.isEmpty ||
        controller.longitudeController.text.isEmpty) {
      _showSnackBar('Preencha todos os campos obrigatórios', isError: true);
      return;
    }

    // Confirmar ação
    final confirm = await _confirmOperation();
    if (!confirm) return;

    // Mostrar indicador de progresso
    _showProgressDialog(
      controller.isEditMode.value
          ? 'Atualizando consultório...'
          : 'Cadastrando consultório...',
    );

    try {
      final result = await controller.cadastrarHospital();

      if (!context.mounted) return;
      Navigator.of(context).pop(); // Fechar diálogo de progresso

      if (result) {
        _showSnackBar(
          controller.isEditMode.value
              ? 'Consultório atualizado com sucesso'
              : 'Consultório cadastrado com sucesso',
          isError: false,
        );
        widget.onFormSubmitted();
      } else {
        _showSnackBar('Erro: ${controller.error.value}', isError: true);
      }
    } catch (e) {
      if (!context.mounted) return;
      Navigator.of(context)
          .pop(); // Fechar diálogo de progresso em caso de erro
      _showSnackBar('Erro: $e', isError: true);
    }
  }
  
  void _showProgressDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSpacing.borderRadius),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation(AppTheme.primaryColor),
            ),
            const SizedBox(height: 20),
            Text(message),
          ],
        ),
      ),
    );
  }

  Future<bool> _confirmOperation() async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppSpacing.borderRadius),
            ),
            title: Text(
              controller.isEditMode.value
                  ? 'Confirmar Atualização'
                  : 'Confirmar Cadastro',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            content: Text(
              controller.isEditMode.value
                  ? 'Deseja atualizar os dados deste consultório?'
                  : 'Deseja cadastrar este novo consultório?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancelar'),
              ),
              FilledButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: FilledButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.black87,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppSpacing.borderRadius / 2),
                  ),
                ),
                child: const Text('Confirmar'),
              ),
            ],
          ),
        ) ??
        false;
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).hideCurrentSnackBar();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              isError ? Icons.error : Icons.check_circle,
              color: Colors.white,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
              ),
            ),
          ],
        ),
        backgroundColor: isError ? Colors.red.shade700 : Colors.green.shade700,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSpacing.borderRadius),
        ),
        margin: const EdgeInsets.all(8),
        duration: const Duration(seconds: 4),
        action: SnackBarAction(
          label: 'OK',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }
}
