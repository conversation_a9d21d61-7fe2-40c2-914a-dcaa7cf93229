import 'package:flutter/material.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'package:fila_app/theme/theme.dart';
import 'package:fila_app/theme/constants.dart';

class HospitalCard extends StatelessWidget {
  final ParseObject hospital;
  final VoidCallback onEdit;
  final VoidCallback onDelete;
  final VoidCallback onResetCredentials;
  final Function(bool) onStatusChange;

  const HospitalCard({
    super.key,
    required this.hospital,
    required this.onEdit,
    required this.onDelete,
    required this.onResetCredentials,
    required this.onStatusChange,
  });

  String _formatDate(DateTime? date) {
    if (date == null) return '';
    return '${date.day.toString().padLeft(2, '0')}/'
        '${date.month.toString().padLeft(2, '0')}/'
        '${date.year}';
  }

  @override
  Widget build(BuildContext context) {
    final nome = hospital.get<String>('nome') ?? 'Hospital sem nome';
    final tipo = hospital.get<String>('tipo') ?? 'Não informado';
    final ativo = hospital.get<bool>('ativo') ?? false;
    final telefone = hospital.get<String>('telefone');
    final endereco = hospital.get<String>('endereco');
    final dataCadastro =
        hospital.get<DateTime>('dataCadastro') ?? hospital.createdAt;

    return Card(
      elevation: 3,
      clipBehavior: Clip.hardEdge,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSpacing.borderRadius),
      ),
      margin: EdgeInsets.zero,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Status ribbon
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 4),
            color: ativo ? Colors.green.shade500 : Colors.red.shade500,
            child: Center(
              child: Text(
                ativo ? 'ATIVO' : 'INATIVO',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ),
          ),

          // Cabeçalho do card
          Container(
            padding: const EdgeInsets.all(AppSpacing.cardPadding),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  ativo
                      ? AppTheme.primaryColor.withAlpha(25)
                      : Colors.grey.withAlpha(25),
                  Colors.white,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: Row(
              children: [
                // Ícone do hospital
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: ativo
                        ? AppTheme.primaryColor.withAlpha(50)
                        : Colors.grey.withAlpha(50),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.local_hospital,
                    color: ativo ? Colors.green[700] : Colors.grey[700],
                    size: 28,
                  ),
                ),
                const SizedBox(width: 12),

                // Informações do hospital
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        nome,
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                      const SizedBox(height: 4),
                      Text('Tipo: $tipo'),
                      if (telefone != null) ...[
                        const SizedBox(height: 4),
                        Text('Tel: $telefone'),
                      ],
                      if (endereco != null && endereco.isNotEmpty) ...[
                        const SizedBox(height: 4),
                        Text(
                          'Endereço: $endereco',
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                      if (dataCadastro != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          'Cadastrado em: ${_formatDate(dataCadastro)}',
                          style:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Colors.grey[600],
                                  ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Botões de ação
          Padding(
            padding: const EdgeInsets.all(AppSpacing.sm),
            child: Column(
              children: [
                // Primeira linha de botões
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Expanded(
                      child: Tooltip(
                        message:
                            'Gera uma nova senha para o usuário deste hospital',
                        child: FilledButton.icon(
                          onPressed: onResetCredentials,
                          icon: const Icon(Icons.password, size: 18),
                          label: const Text('Gerar Credenciais'),
                          style: FilledButton.styleFrom(
                            backgroundColor: Colors.blue,
                            padding: const EdgeInsets.symmetric(vertical: 10),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: FilledButton.icon(
                        onPressed: onEdit,
                        icon: const Icon(Icons.edit, size: 18),
                        label: const Text('Editar'),
                        style: FilledButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor,
                          foregroundColor: Colors.black87,
                          padding: const EdgeInsets.symmetric(vertical: 10),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),

                // Segunda linha de botões
                Row(
                  children: [
                    // Botão de ativar/desativar
                    Expanded(
                      child: FilledButton.icon(
                        onPressed: () => onStatusChange(!ativo),
                        icon: Icon(
                          ativo
                              ? Icons.cancel_outlined
                              : Icons.check_circle_outline,
                          size: 18,
                        ),
                        label: Text(ativo
                            ? 'Desativar Consultório'
                            : 'Ativar Consultório'),
                        style: FilledButton.styleFrom(
                          backgroundColor: ativo ? Colors.orange : Colors.green,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 10),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),

                // Botão de excluir
                SizedBox(
                  width: double.infinity,
                  child: Tooltip(
                    message: 'Excluir este consultório permanentemente',
                    child: OutlinedButton.icon(
                      onPressed: onDelete,
                      icon: const Icon(Icons.delete_forever, color: Colors.red),
                      label: const Text('Excluir Consultório'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.red,
                        side: const BorderSide(color: Colors.red),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
