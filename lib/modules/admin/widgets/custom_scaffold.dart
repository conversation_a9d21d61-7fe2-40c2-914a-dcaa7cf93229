import 'package:flutter/material.dart';
import 'package:fila_app/theme/theme.dart';
import 'package:fila_app/theme/constants.dart';

class AdminCustomScaffold extends StatelessWidget {
  final String title;
  final TabController tabController;
  final List<Widget> tabs;
  final Widget body;
  final VoidCallback onLogout;
  final VoidCallback? onNewHospital;

  const AdminCustomScaffold({
    super.key,
    required this.title,
    required this.tabController,
    required this.tabs,
    required this.body,
    required this.onLogout,
    this.onNewHospital,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        backgroundColor: AppTheme.primaryColor,
        elevation: 0,
        scrolledUnderElevation: 2,
        actions: [
          if (onNewHospital != null)
            IconButton(
              icon: const Icon(Icons.add_circle_outline),
              tooltip: 'Novo Hospital',
              onPressed: onNewHospital,
            ),
          IconButton(
            icon: const Icon(Icons.logout),
            tooltip: 'Sair',
            onPressed: onLogout,
          ),
          const SizedBox(width: 8),
        ],
        bottom: TabBar(
          controller: tabController,
          indicatorColor: Colors.white,
          labelStyle: Theme.of(context).textTheme.labelLarge?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
          tabs: tabs,
          dividerHeight: 0,
        ),
      ),
      body: _GradientBackground(
        child: AnimatedSwitcher(
          duration: const Duration(milliseconds: 300),
          child: body,
        ),
      ),
    );
  }
}

class _GradientBackground extends StatelessWidget {
  final Widget child;

  const _GradientBackground({required this.child});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [Color(0x90dcf8ec), Color(0xFF75CBBB)],
          stops: [0.01, 1.0],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
      child: child,
    );
  }
}
