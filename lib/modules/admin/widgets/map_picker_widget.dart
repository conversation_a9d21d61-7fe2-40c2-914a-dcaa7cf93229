import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:fila_app/theme/theme.dart';
import 'package:fila_app/theme/constants.dart';
import 'package:geolocator/geolocator.dart';

class MapPickerWidget extends StatefulWidget {
  final double? latitude;
  final double? longitude;
  final Function(double lat, double lng) onLocationSelected;
  final bool isLoading;

  const MapPickerWidget({
    super.key,
    this.latitude,
    this.longitude,
    required this.onLocationSelected,
    this.isLoading = false,
  });

  @override
  State<MapPickerWidget> createState() => _MapPickerWidgetState();
}

class _MapPickerWidgetState extends State<MapPickerWidget> {
  GoogleMapController? _mapController;
  Set<Marker> _markers = {};
  LatLng? _selectedLocation;
  bool _isObtainingAddress = false;
  bool _isMapEditMode = false;
  bool _mapInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeSelectedLocation();
  }

  @override
  void didUpdateWidget(MapPickerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.latitude != widget.latitude ||
        oldWidget.longitude != widget.longitude) {
      _initializeSelectedLocation();
    }
  }

  void _initializeSelectedLocation() {
    if (widget.latitude != null && widget.longitude != null) {
      setState(() {
        _selectedLocation = LatLng(widget.latitude!, widget.longitude!);
        _updateMarkers();
      });

      // Se o controlador do mapa já estiver disponível, centralize nesta localização
      if (_mapController != null && _mapInitialized) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _centerMapOnSelectedLocation();
        });
      }
    }
  }

  @override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }

  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;
    setState(() {
      _mapInitialized = true;
    });
    _centerMapOnSelectedLocation();
  }

  void _centerMapOnSelectedLocation() {
    if (_selectedLocation != null && _mapController != null) {
      _mapController!.animateCamera(
        CameraUpdate.newLatLngZoom(_selectedLocation!, 15),
      );
    }
  }

  void _onMapTap(LatLng position) {
    if (!_isMapEditMode) return;

    setState(() {
      _selectedLocation = position;
      _updateMarkers();
    });

    widget.onLocationSelected(position.latitude, position.longitude);
  }

  void _updateMarkers() {
    if (_selectedLocation != null) {
      setState(() {
        _markers = {
          Marker(
            markerId: const MarkerId('selected_location'),
            position: _selectedLocation!,
            draggable: _isMapEditMode,
            icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure),
            infoWindow: const InfoWindow(title: 'Localização selecionada'),
            onDragEnd: (newPosition) {
              setState(() {
                _selectedLocation = newPosition;
              });
              widget.onLocationSelected(
                  newPosition.latitude, newPosition.longitude);
            },
          ),
        };
      });
    } else {
      setState(() {
        _markers = {};
      });
    }
  }

  void _toggleMapEditMode() {
    setState(() {
      _isMapEditMode = !_isMapEditMode;
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(_isMapEditMode 
          ? 'Modo de edição ativado. Toque no mapa para mover o marcador.' 
          : 'Modo de edição desativado.'),
        backgroundColor: _isMapEditMode ? Colors.blue : Colors.grey,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(8),
      ),
    );
  }

  Future<void> _getCurrentLocation() async {
    setState(() {
      _isObtainingAddress = true;
    });

    try {
      // Verificar permissão de localização
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          _showSnackBar('Permissão de localização negada');
          setState(() { _isObtainingAddress = false; });
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        _showSnackBar(
            'Permissão de localização negada permanentemente. Configure nas configurações do app.');
        setState(() { _isObtainingAddress = false; });
        return;
      }

      // Verificar se o GPS está habilitado
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        _showSnackBar('Serviço de localização desativado. Por favor, ative o GPS.');
        setState(() { _isObtainingAddress = false; });
        return;
      }

      // Obter posição atual com timeout mais longo
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 15),
      );

      LatLng currentPosition = LatLng(position.latitude, position.longitude);

      // Primeiro atualiza a câmera antes de atualizar os marcadores
      if (_mapController != null) {
        await _mapController!.animateCamera(
          CameraUpdate.newLatLngZoom(currentPosition, 15),
        );
      }

      // Depois atualiza o estado com a nova localização
      if (mounted) {
        setState(() {
          _selectedLocation = currentPosition;
          _updateMarkers();
          _isMapEditMode = true; // Ativa o modo de edição automaticamente
        });
      }

      // Notifica sobre a localização selecionada
      widget.onLocationSelected(position.latitude, position.longitude);
      
      // Feedback de sucesso
      _showSuccessSnackBar('Localização atual obtida com sucesso!');
      
    } catch (e) {
      _showSnackBar('Erro ao obter localização: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isObtainingAddress = false;
        });
      }
    }
  }

  void _openFullscreenMap() {
    Navigator.of(context).push(
      MaterialPageRoute(
        fullscreenDialog: true,
        builder: (context) {
          return Scaffold(
            appBar: AppBar(
              backgroundColor: AppTheme.primaryColor,
              title: const Text('Selecione a Localização'),
              elevation: 2,
              leading: IconButton(
                icon: const Icon(Icons.close),
                onPressed: () => Navigator.of(context).pop(),
              ),
              actions: [
                // Botão para obter localização atual
                IconButton(
                  icon: const Icon(Icons.my_location),
                  onPressed: _getCurrentLocation,
                  tooltip: 'Usar minha localização atual',
                ),
                // Botão para confirmar
                IconButton(
                  icon: const Icon(Icons.check),
                  onPressed: () {
                    if (_selectedLocation != null) {
                      widget.onLocationSelected(
                        _selectedLocation!.latitude,
                        _selectedLocation!.longitude,
                      );
                      Navigator.of(context).pop();
                    } else {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Selecione uma localização no mapa'),
                          backgroundColor: Colors.red,
                          behavior: SnackBarBehavior.floating,
                        ),
                      );
                    }
                  },
                  tooltip: 'Confirmar localização',
                ),
              ],
            ),
            body: Stack(
              children: [
                GoogleMap(
                  initialCameraPosition: CameraPosition(
                    target: _selectedLocation ??
                        const LatLng(-23.550520, -46.633308), // São Paulo como default
                    zoom: 15,
                  ),
                  onMapCreated: (controller) {
                    if (_selectedLocation != null) {
                      controller.animateCamera(
                        CameraUpdate.newLatLngZoom(_selectedLocation!, 15),
                      );
                    }
                  },
                  onTap: (position) {
                    setState(() {
                      _selectedLocation = position;
                      _updateMarkers();
                    });
                  },
                  markers: _markers,
                  mapToolbarEnabled: true,
                  compassEnabled: true,
                  myLocationEnabled: true,
                  myLocationButtonEnabled: false,
                  zoomControlsEnabled: true,
                ),

                // Botão para obter localização atual
                Positioned(
                  bottom: 100,
                  right: 16,
                  child: FloatingActionButton(
                    heroTag: "btn_location_fullscreen",
                    backgroundColor: AppTheme.primaryColor,
                    onPressed: _getCurrentLocation,
                    tooltip: 'Obter minha localização atual',
                    child: const Icon(Icons.my_location, color: Colors.black87),
                  ),
                ),

                // Instruções para o usuário
                Positioned(
                  bottom: 16,
                  left: 16,
                  right: 16,
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: const [
                        Icon(Icons.info_outline, color: AppTheme.primaryColor),
                        SizedBox(height: 8),
                        Text(
                          'Toque no mapa para selecionar a localização ou arraste o marcador para ajustar a posição.',
                          style: TextStyle(fontSize: 14),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),

                if (_isObtainingAddress)
                  Container(
                    color: Colors.black.withOpacity(0.3),
                    child: const Center(
                      child: Card(
                        child: Padding(
                          padding: EdgeInsets.all(16.0),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              CircularProgressIndicator(),
                              SizedBox(height: 16),
                              Text('Obtendo sua localização...')
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }

  void _showSnackBar(String message) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red.shade700,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(8),
      ),
    );
  }
  
  void _showSuccessSnackBar(String message) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.green.shade600,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(8),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSpacing.borderRadius),
      ),
      child: Container(
        height: 300,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppSpacing.borderRadius),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(AppSpacing.borderRadius),
          child: Stack(
            children: [
              // Camada do Google Maps
              GoogleMap(
                onMapCreated: _onMapCreated,
                initialCameraPosition: CameraPosition(
                  target:
                      _selectedLocation ?? const LatLng(-23.550520, -46.633308),
                  zoom: 12,
                ),
                markers: _markers,
                mapToolbarEnabled: false,
                compassEnabled: true,
                myLocationEnabled: _isMapEditMode,
                myLocationButtonEnabled: false,
                zoomControlsEnabled: true,
                scrollGesturesEnabled: _isMapEditMode,
                zoomGesturesEnabled: _isMapEditMode,
                rotateGesturesEnabled: _isMapEditMode,
                tiltGesturesEnabled: _isMapEditMode,
                onTap: _onMapTap,
              ),
              
              // Camada de interação
              if (!_isMapEditMode)
                Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: _openFullscreenMap,
                    child: Container(
                      color: Colors.black.withOpacity(0.03),
                      child: Center(
                        child: _selectedLocation == null
                          ? Column(
                              mainAxisSize: MainAxisSize.min,
                              children: const [
                                Icon(
                                  Icons.location_on,
                                  color: AppTheme.primaryColor,
                                  size: 48,
                                ),
                                SizedBox(height: 8),
                                Text(
                                  'Toque para selecionar a localização',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            )
                          : const SizedBox(),
                      ),
                    ),
                  ),
                ),

              // Botões de ação
              Positioned(
                bottom: 16,
                right: 16,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Botão editar mapa em tela cheia
                    FloatingActionButton(
                      heroTag: "btn_edit_location",
                      mini: true,
                      backgroundColor: Colors.white,
                      foregroundColor: AppTheme.primaryColor,
                      onPressed: _openFullscreenMap,
                      tooltip: 'Abrir mapa em tela cheia',
                      elevation: 3,
                      child: const Icon(Icons.open_in_full, size: 20),
                    ),
                    const SizedBox(height: 8),
                    // Botão toggle modo de edição
                    FloatingActionButton(
                      heroTag: "btn_toggle_edit",
                      mini: true,
                      backgroundColor: _isMapEditMode 
                          ? Colors.blue
                          : Colors.white,
                      foregroundColor: _isMapEditMode 
                          ? Colors.white
                          : AppTheme.primaryColor,
                      onPressed: _toggleMapEditMode,
                      tooltip: _isMapEditMode
                          ? 'Desativar edição no mapa'
                          : 'Ativar edição no mapa',
                      elevation: 3,
                      child: Icon(
                        _isMapEditMode ? Icons.edit_off : Icons.edit,
                        size: 20,
                      ),
                    ),
                  ],
                ),
              ),

              // Overlay de carregamento
              if (widget.isLoading || _isObtainingAddress)
                Container(
                  color: Colors.black.withOpacity(0.3),
                  child: const Center(
                    child: Card(
                      child: Padding(
                        padding: EdgeInsets.all(16.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            CircularProgressIndicator(),
                            SizedBox(height: 16),
                            Text('Carregando...')
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                
              // Indicador de mapa não editável
              if (!_isMapEditMode && _selectedLocation != null)
                Positioned(
                  top: 16,
                  left: 16,
                  right: 16,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.9),
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: const [
                        Icon(Icons.info_outline, size: 16, color: Colors.blue),
                        SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Toque no mapa para editar em tela cheia',
                            style: TextStyle(fontSize: 12),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
