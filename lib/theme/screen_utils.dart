// In a screen_utils.dart file
import 'package:flutter/material.dart';

class ScreenUtils {
  static MediaQueryData? _mediaQueryData;
  static double? screenWidth;
  static double? screenHeight;
  static double? defaultSize;
  static Orientation? orientation;

  void init(BuildContext context) {
    _mediaQueryData = MediaQuery.of(context);
    screenWidth = _mediaQueryData!.size.width;
    screenHeight = _mediaQueryData!.size.height;
    orientation = _mediaQueryData!.orientation;
  }

  // Get the proportionate height as per screen size
  static double getProportionateScreenHeight(double inputHeight) {
    double screenHeight = ScreenUtils.screenHeight!;
    // 812 is the standard height used during design
    return (inputHeight / 812.0) * screenHeight;
  }

  // Get the proportionate width as per screen size
  static double getProportionateScreenWidth(double inputWidth) {
    double screenWidth = ScreenUtils.screenWidth!;
    // 375 is the standard width used during design
    return (inputWidth / 375.0) * screenWidth;
  }
}
