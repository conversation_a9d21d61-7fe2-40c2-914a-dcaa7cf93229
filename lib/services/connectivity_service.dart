import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ConnectivityService extends GetxService {
  final Rx<ConnectivityResult> connectionStatus =
      Rx<ConnectivityResult>(ConnectivityResult.none);
  final RxBool isConnected = true.obs;

  final Connectivity _connectivity = Connectivity();
  late StreamSubscription<List<ConnectivityResult>> _connectivitySubscription;

  @override
  void onInit() {
    super.onInit();
    _initConnectivity();
    // Estamos usando o método correto para lidar com a lista de resultados
    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
  }

  @override
  void onClose() {
    _connectivitySubscription.cancel();
    super.onClose();
  }

  Future<void> _initConnectivity() async {
    try {
      final results = await _connectivity.checkConnectivity();
      // Modificamos esta linha para lidar com a lista de resultados
      if (results.isNotEmpty) {
        _updateSingleConnectionStatus(results.first);
      } else {
        _updateSingleConnectionStatus(ConnectivityResult.none);
      }
    } catch (e) {
      debugPrint('Erro ao verificar conectividade: $e');
    }
  }

  // Este método já está corretamente configurado para receber uma lista
  void _updateConnectionStatus(List<ConnectivityResult> results) {
    if (results.isNotEmpty) {
      // Pegamos o primeiro resultado, que geralmente é o mais relevante
      _updateSingleConnectionStatus(results.first);
    } else {
      _updateSingleConnectionStatus(ConnectivityResult.none);
    }
  }

  // Método auxiliar para atualizar o status com um único resultado
  void _updateSingleConnectionStatus(ConnectivityResult result) {
    connectionStatus.value = result;
    isConnected.value = result != ConnectivityResult.none;
  }
}
