import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';

class ParseBatchService {
  // Método para salvar múltiplos objetos em uma única requisição
  static Future<List<ParseResponse>> saveBatch(
      List<ParseObject> objects) async {
    final List<Future<ParseResponse>> futures = [];

    for (var object in objects) {
      futures.add(object.save());
    }

    return await Future.wait(futures);
  }

  // Método para carregar objetos relacionados em uma única consulta
  static Future<List<ParseObject>> getRelatedObjects(
      String className, List<String> objectIds,
      {List<String> include = const []}) async {
    if (objectIds.isEmpty) return [];

    final query = QueryBuilder<ParseObject>(ParseObject(className))
      ..whereContainedIn('objectId', objectIds);

    if (include.isNotEmpty) {
      for (var field in include) {
        query.includeObject([field]);
      }
    }

    final response = await query.query();

    if (response.success && response.results != null) {
      return response.results!.cast<ParseObject>();
    }

    return [];
  }
}
