import 'dart:async';
import 'dart:collection';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';

/// Classe para armazenar métricas do serviço de throttling
class ThrottlingMetrics {
  final int totalRequestsProcessed;
  final int totalRequestsQueued;
  final int currentQueueSize;
  final int maxQueueSize;
  final int requestsDropped;
  final int requestsPerSecond;

  ThrottlingMetrics({
    required this.totalRequestsProcessed,
    required this.totalRequestsQueued,
    required this.currentQueueSize,
    required this.maxQueueSize,
    required this.requestsDropped,
    required this.requestsPerSecond,
  });

  @override
  String toString() {
    return 'ThrottlingMetrics(processed: $totalRequestsProcessed, queued: $totalRequestsQueued, '
        'currentQueue: $currentQueueSize, maxQueue: $maxQueueSize, '
        'dropped: $requestsDropped, rps: $requestsPerSecond)';
  }
}

/// Enumeração para prioridade de requisições
enum ThrottlePriority { low, medium, high, critical }

/// Serviço para controlar a taxa de requisições para o Back4App
/// Garante que não sejam feitas mais que 10 requisições por segundo
/// e implementa estratégias avançadas de gerenciamento de requisições
class ThrottlingService {
  // Taxa máxima de requisições por segundo
  static const int _maxRequestsPerSecond = 10;

  // Janela de tempo para controle de requisições (em milissegundos)
  static const int _timeWindowMs = 1000;

  // Filas de requisições pendentes (separadas por prioridade)
  final Queue<_ThrottledRequest> _highPriorityQueue =
      Queue<_ThrottledRequest>();
  final Queue<_ThrottledRequest> _normalPriorityQueue =
      Queue<_ThrottledRequest>();

  // Contador de requisições na janela atual
  int _requestCount = 0;

  // Timestamp da última requisição
  int _lastRequestTimestamp = 0;

  // Timer para resetar o contador de requisições
  Timer? _resetTimer;

  // Timer para processar a fila de requisições
  Timer? _processQueueTimer;

  // Flag para indicar se o serviço está processando a fila
  bool _isProcessingQueue = false;

  // Estatísticas de uso
  int _totalRequestsProcessed = 0;
  int _totalRequestsQueued = 0;
  int _maxQueueSize = 0;
  int _requestsDropped = 0;

  // Lista de ouvintes para notificação de mudanças na fila
  final List<Function(int queueSize)> _queueSizeListeners = [];

  // Stream controller para métricas em tempo real
  final _metricsStreamController =
      StreamController<ThrottlingMetrics>.broadcast();
  Stream<ThrottlingMetrics> get metricsStream =>
      _metricsStreamController.stream;

  // Método singleton
  static final ThrottlingService _instance = ThrottlingService._internal();

  factory ThrottlingService() {
    return _instance;
  }

  ThrottlingService._internal() {
    _startResetTimer();
    _startMetricsReporting();
  }

  /// Inicia o timer que reseta o contador de requisições
  void _startResetTimer() {
    _resetTimer?.cancel();
    _resetTimer =
        Timer.periodic(const Duration(milliseconds: _timeWindowMs), (_) {
      _requestCount = 0;
      _processQueue();
    });
  }

  /// Inicia o timer para reportar métricas periodicamente
  void _startMetricsReporting() {
    Timer.periodic(const Duration(seconds: 5), (_) {
      _reportMetrics();
    });
  }

  /// Reporta métricas de uso do serviço
  void _reportMetrics() {
    final metrics = ThrottlingMetrics(
      totalRequestsProcessed: _totalRequestsProcessed,
      totalRequestsQueued: _totalRequestsQueued,
      currentQueueSize: queueSize,
      maxQueueSize: _maxQueueSize,
      requestsDropped: _requestsDropped,
      requestsPerSecond: _requestCount,
    );

    _metricsStreamController.add(metrics);
  }

  /// Adiciona um ouvinte para receber atualizações do tamanho da fila
  void addQueueSizeListener(Function(int queueSize) listener) {
    if (!_queueSizeListeners.contains(listener)) {
      _queueSizeListeners.add(listener);
    }
  }

  /// Remove um ouvinte de atualizações do tamanho da fila
  void removeQueueSizeListener(Function(int queueSize) listener) {
    _queueSizeListeners.remove(listener);
  }

  /// Notifica os ouvintes sobre mudanças no tamanho da fila
  void _notifyQueueSizeChanged() {
    final size = queueSize;
    for (var listener in _queueSizeListeners) {
      listener(size);
    }
  }

  /// Executa uma requisição com controle de taxa
  Future<T> throttle<T>({
    required Future<T> Function() action,
    String operationName = 'generic_operation',
    bool isPriority = false,
    Duration timeout = const Duration(seconds: 30),
  }) async {
    // Criar um Completer para controlar a resposta assíncrona
    final completer = Completer<T>();

    // Criar a requisição throttled
    final request = _ThrottledRequest(
      action: action,
      completer: completer,
      operationName: operationName,
      isPriority: isPriority,
      createdAt: DateTime.now(),
      timeout: timeout,
    );

    // Incrementar contador de requisições enfileiradas
    _totalRequestsQueued++;

    // Adicionar à fila com base na prioridade
    if (isPriority) {
      // Requisições prioritárias vão para o início da fila
      _highPriorityQueue.addFirst(request);
    } else {
      // Requisições normais vão para o final da fila
      _normalPriorityQueue.add(request);
    }

    // Atualizar tamanho máximo da fila
    if (queueSize > _maxQueueSize) {
      _maxQueueSize = queueSize;
    }

    _notifyQueueSizeChanged();

    // Processar a fila imediatamente se possível
    _processQueue();

    // Adicionar um timeout para evitar que requisições fiquem presas na fila indefinidamente
    Timer(timeout, () {
      bool removed = false;

      // Tentar remover da fila de alta prioridade
      if (_highPriorityQueue.contains(request)) {
        _highPriorityQueue.remove(request);
        removed = true;
      }
      // Tentar remover da fila normal
      else if (_normalPriorityQueue.contains(request)) {
        _normalPriorityQueue.remove(request);
        removed = true;
      }

      if (removed && !completer.isCompleted) {
        _requestsDropped++;
        _notifyQueueSizeChanged();
        completer.completeError(
          TimeoutException(
              'A requisição expirou após ${timeout.inSeconds} segundos',
              timeout),
        );
        debugPrint(
            'THROTTLING: ${request.operationName} cancelada por timeout');
      }
    });

    return completer.future;
  }

  /// Processa a fila de requisições respeitando o limite de taxa
  void _processQueue() {
    if (_isProcessingQueue ||
        (_highPriorityQueue.isEmpty && _normalPriorityQueue.isEmpty)) {
      return;
    }

    _isProcessingQueue = true;

    try {
      // Processar tantas requisições quanto possível dentro da taxa permitida
      while (
          (_highPriorityQueue.isNotEmpty || _normalPriorityQueue.isNotEmpty) &&
              _requestCount < _maxRequestsPerSecond) {
        // Determinar de qual fila pegar a próxima requisição
        final request = _highPriorityQueue.isNotEmpty
            ? _highPriorityQueue.removeFirst()
            : _normalPriorityQueue.removeFirst();

        _notifyQueueSizeChanged();

        _requestCount++;
        _totalRequestsProcessed++;
        _lastRequestTimestamp = DateTime.now().millisecondsSinceEpoch;

        // Registrar para debugging
        debugPrint(
            'THROTTLING: Executando ${request.operationName} (${_requestCount}/$_maxRequestsPerSecond)');

        // Executar a ação e completar o future
        request.action().then((result) {
          if (!request.completer.isCompleted) {
            request.completer.complete(result);
          }
        }).catchError((error) {
          if (!request.completer.isCompleted) {
            request.completer.completeError(error);
          }
          debugPrint('THROTTLING: Erro em ${request.operationName}: $error');
        });
      }
    } finally {
      _isProcessingQueue = false;

      // Se ainda há requisições na fila, agendar processamento no próximo ciclo
      if (_highPriorityQueue.isNotEmpty || _normalPriorityQueue.isNotEmpty) {
        _processQueueTimer?.cancel();
        _processQueueTimer = Timer(
          // Calcular espera mínima baseada no tempo transcorrido na janela atual
          Duration(milliseconds: (_timeWindowMs ~/ _maxRequestsPerSecond)),
          _processQueue,
        );
      }
    }
  }

  /// Obter o tamanho atual da fila de requisições
  int get queueSize => _highPriorityQueue.length + _normalPriorityQueue.length;

  /// Limpar a fila (usar com cuidado)
  void clearQueue() {
    final highPriorityRequests = List.from(_highPriorityQueue);
    final normalPriorityRequests = List.from(_normalPriorityQueue);

    _highPriorityQueue.clear();
    _normalPriorityQueue.clear();

    final allRequests = [...highPriorityRequests, ...normalPriorityRequests];

    for (var request in allRequests) {
      if (!request.completer.isCompleted) {
        request.completer.completeError(
          Exception('A requisição foi cancelada manualmente'),
        );
      }
    }

    _requestsDropped += allRequests.length;
    _notifyQueueSizeChanged();
    debugPrint(
        'THROTTLING: Fila limpa, ${allRequests.length} requisições canceladas');
  }

  /// Liberar recursos ao encerrar o aplicativo
  void dispose() {
    _resetTimer?.cancel();
    _processQueueTimer?.cancel();
    _metricsStreamController.close();
    clearQueue();
  }
}

/// Classe que representa uma requisição na fila de throttling
class _ThrottledRequest<T> {
  final Future<T> Function() action;
  final Completer<T> completer;
  final String operationName;
  final bool isPriority;
  final DateTime createdAt;
  final Duration timeout;

  _ThrottledRequest({
    required this.action,
    required this.completer,
    required this.operationName,
    required this.isPriority,
    required this.createdAt,
    required this.timeout,
  });
}
