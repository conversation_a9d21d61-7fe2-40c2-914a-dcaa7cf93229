import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'connectivity_service.dart';

class SyncManager extends GetxService {
  final String _pendingOperationsFile = 'pending_operations.json';
  final List<Map<String, dynamic>> _pendingOperations = [];
  bool _isSyncing = false;
  Timer? _syncTimer;

  @override
  void onInit() {
    super.onInit();
    _loadPendingOperations();

    // Adicionar listener para mudanças de conectividade
    final connectivityService = Get.find<ConnectivityService>();
    ever(connectivityService.isConnected, (connected) {
      if (connected && _pendingOperations.isNotEmpty) {
        syncPendingOperations();
      }
    });

    // Configurar sincronização periódica
    _syncTimer = Timer.periodic(Duration(minutes: 5), (_) {
      if (connectivityService.isConnected.value) {
        syncPendingOperations();
      }
    });
  }

  @override
  void onClose() {
    _syncTimer?.cancel();
    super.onClose();
  }

  Future<void> queueOperation(
      String className, String operation, Map<String, dynamic> data,
      {String? objectId}) async {
    final op = {
      'class': className,
      'operation': operation,
      'data': data,
      'objectId': objectId,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };

    _pendingOperations.add(op);
    await _savePendingOperations();

    // Tenta sincronizar imediatamente se estiver online
    final connectivityService = Get.find<ConnectivityService>();
    if (connectivityService.isConnected.value) {
      syncPendingOperations();
    }
  }

  Future<void> syncPendingOperations() async {
    if (_isSyncing || _pendingOperations.isEmpty) return;

    _isSyncing = true;

    try {
      final List<Map<String, dynamic>> successfulOps = [];

      for (final op in _pendingOperations) {
        try {
          final ParseObject object = ParseObject(op['class']);

          if (op['objectId'] != null) {
            object.objectId = op['objectId'];
          }

          op['data'].forEach((key, value) {
            object.set(key, value);
          });

          switch (op['operation']) {
            case 'create':
            case 'update':
              final result = await object.save();
              if (result.success) {
                successfulOps.add(op);
              }
              break;
            case 'delete':
              final result = await object.delete();
              if (result.success) {
                successfulOps.add(op);
              }
              break;
          }
        } catch (e) {
          debugPrint('Erro ao sincronizar operação: $e');
        }
      }

      // Remover operações bem-sucedidas da fila
      for (final op in successfulOps) {
        _pendingOperations.remove(op);
      }

      await _savePendingOperations();
    } finally {
      _isSyncing = false;
    }
  }

  Future<void> _loadPendingOperations() async {
    try {
      final dir = await getApplicationDocumentsDirectory();
      final file = File('${dir.path}/$_pendingOperationsFile');

      if (await file.exists()) {
        final content = await file.readAsString();
        final List<dynamic> operations = jsonDecode(content);
        _pendingOperations.addAll(
            operations.map((op) => op as Map<String, dynamic>).toList());
      }
    } catch (e) {
      debugPrint('Erro ao carregar operações pendentes: $e');
    }
  }

  Future<void> _savePendingOperations() async {
    try {
      final dir = await getApplicationDocumentsDirectory();
      final file = File('${dir.path}/$_pendingOperationsFile');

      await file.writeAsString(jsonEncode(_pendingOperations));
    } catch (e) {
      debugPrint('Erro ao salvar operações pendentes: $e');
    }
  }
}
