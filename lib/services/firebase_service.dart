import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';

class FirebaseService {
  static Future<void> initialize() async {
    if (_isFirebaseSupported()) {
      try {
        await Firebase.initializeApp();
        debugPrint('Firebase inicializado com sucesso');
        
        // Registrar handlers de mensagens
        await _setupMessaging();
      } catch (e) {
        debugPrint('Erro ao inicializar Firebase: $e');
      }
    } else {
      debugPrint('Firebase não é suportado nesta plataforma');
    }
  }

  static Future<String?> getToken({bool printToken = false}) async {
    if (!_isFirebaseSupported()) {
      if (printToken) print('Firebase não é suportado nesta plataforma, não é possível obter token FCM');
      return null;
    }
    
    try {
      if (printToken) print('Tentando obter token FCM...');
      final token = await FirebaseMessaging.instance.getToken();
      if (token != null && printToken) {
        print('Token FCM obtido com sucesso: $token');
      } else if (printToken) {
        print('Token FCM não disponível (null)');
      }
      return token;
    } catch (e) {
      if (printToken) print('Erro ao obter token FCM: $e');
      return null;
    }
  }
  
  static Future<void> _setupMessaging() async {
    if (!_isFirebaseSupported()) return;
    
    // Registrar handler para mensagens em background
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
    
    // Solicitar permissão para notificações (iOS e web)
    if (Platform.isIOS || kIsWeb) {
      final settings = await FirebaseMessaging.instance.requestPermission(
        alert: true,
        badge: true,
        sound: true,
      );
      debugPrint('Permissão de notificação: ${settings.authorizationStatus}');
    }
  }
  
  // Verifica se a plataforma suporta Firebase
  static bool _isFirebaseSupported() {
    return !kIsWeb && (Platform.isAndroid || Platform.isIOS);
  }
}

// Função para lidar com mensagens em background
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  debugPrint('Mensagem em background: ${message.notification?.title}');
}