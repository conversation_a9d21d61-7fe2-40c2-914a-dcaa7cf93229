import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fila_app/services/throttling_service.dart';

/// Serviço para gerenciar atualizações automáticas e manuais
/// respeitando o limite de requisições ao Back4App
class AutoRefreshService extends GetxService {
  // Singleton
  static final AutoRefreshService _instance = AutoRefreshService._internal();
  factory AutoRefreshService() => _instance;
  AutoRefreshService._internal();

  // Serviço de throttling para controlar requisições
  final ThrottlingService _throttlingService = ThrottlingService();
  
  // Mapa de timers de atualização automática por ID de tela
  final Map<String, Timer> _refreshTimers = {};
  
  // Mapa de timestamps da última atualização por ID de tela
  final Map<String, int> _lastRefreshTimestamps = {};
  
  // Mapa de estados de atualização por ID de tela
  final Map<String, RxBool> _refreshingStates = {};
  
  // Intervalo mínimo entre atualizações manuais (em milissegundos)
  static const int _minManualRefreshInterval = 2000; // 2 segundos
  
  // Intervalo padrão para atualizações automáticas (em segundos)
  static const int _defaultAutoRefreshInterval = 60; // 1 minuto
  
  /// Inicia uma atualização automática para uma tela específica
  /// 
  /// [screenId] - Identificador único da tela
  /// [refreshAction] - Função a ser executada para atualizar os dados
  /// [intervalSeconds] - Intervalo entre atualizações automáticas (em segundos)
  /// [onlyWhenVisible] - Se verdadeiro, só atualiza quando a tela está visível
  void startAutoRefresh({
    required String screenId,
    required Future<void> Function() refreshAction,
    int intervalSeconds = _defaultAutoRefreshInterval,
    bool onlyWhenVisible = true,
  }) {
    // Cancelar timer existente, se houver
    stopAutoRefresh(screenId);
    
    // Criar estado de atualização para esta tela, se não existir
    _refreshingStates[screenId] ??= false.obs;
    
    // Criar novo timer
    _refreshTimers[screenId] = Timer.periodic(
      Duration(seconds: intervalSeconds),
      (_) async {
        // Verificar se a tela está visível, se necessário
        if (onlyWhenVisible && !_isScreenVisible(screenId)) {
          debugPrint('REFRESH: Pulando atualização automática para $screenId (tela não visível)');
          return;
        }
        
        // Verificar se já está atualizando
        if (_refreshingStates[screenId]?.value == true) {
          debugPrint('REFRESH: Pulando atualização automática para $screenId (já atualizando)');
          return;
        }
        
        // Executar atualização via throttling
        try {
          _refreshingStates[screenId]?.value = true;
          
          await _throttlingService.throttle(
            action: refreshAction,
            operationName: 'auto_refresh_$screenId',
            isPriority: false,
          );
          
          // Registrar timestamp da atualização
          _lastRefreshTimestamps[screenId] = DateTime.now().millisecondsSinceEpoch;
          
          debugPrint('REFRESH: Atualização automática concluída para $screenId');
        } catch (e) {
          debugPrint('REFRESH: Erro na atualização automática para $screenId: $e');
        } finally {
          _refreshingStates[screenId]?.value = false;
        }
      },
    );
    
    debugPrint('REFRESH: Iniciada atualização automática para $screenId (intervalo: ${intervalSeconds}s)');
  }
  
  /// Para a atualização automática para uma tela específica
  void stopAutoRefresh(String screenId) {
    final timer = _refreshTimers[screenId];
    if (timer != null) {
      timer.cancel();
      _refreshTimers.remove(screenId);
      debugPrint('REFRESH: Parada atualização automática para $screenId');
    }
  }
  
  /// Executa uma atualização manual para uma tela específica
  /// 
  /// [screenId] - Identificador único da tela
  /// [refreshAction] - Função a ser executada para atualizar os dados
  /// [forceRefresh] - Se verdadeiro, força a atualização mesmo se o intervalo mínimo não tiver passado
  Future<bool> manualRefresh({
    required String screenId,
    required Future<void> Function() refreshAction,
    bool forceRefresh = false,
  }) async {
    // Verificar se já está atualizando
    if (_refreshingStates[screenId]?.value == true) {
      debugPrint('REFRESH: Ignorando atualização manual para $screenId (já atualizando)');
      return false;
    }
    
    // Verificar intervalo mínimo entre atualizações manuais
    final lastRefresh = _lastRefreshTimestamps[screenId] ?? 0;
    final now = DateTime.now().millisecondsSinceEpoch;
    final elapsed = now - lastRefresh;
    
    if (!forceRefresh && elapsed < _minManualRefreshInterval) {
      debugPrint('REFRESH: Ignorando atualização manual para $screenId (muito frequente)');
      return false;
    }
    
    // Criar estado de atualização para esta tela, se não existir
    _refreshingStates[screenId] ??= false.obs;
    
    // Executar atualização via throttling
    try {
      _refreshingStates[screenId]?.value = true;
      
      await _throttlingService.throttle(
        action: refreshAction,
        operationName: 'manual_refresh_$screenId',
        isPriority: true, // Atualizações manuais têm prioridade
      );
      
      // Registrar timestamp da atualização
      _lastRefreshTimestamps[screenId] = now;
      
      debugPrint('REFRESH: Atualização manual concluída para $screenId');
      return true;
    } catch (e) {
      debugPrint('REFRESH: Erro na atualização manual para $screenId: $e');
      return false;
    } finally {
      _refreshingStates[screenId]?.value = false;
    }
  }
  
  /// Obtém o estado de atualização para uma tela específica
  RxBool getRefreshingState(String screenId) {
    _refreshingStates[screenId] ??= false.obs;
    return _refreshingStates[screenId]!;
  }
  
  /// Verifica se uma tela está visível
  bool _isScreenVisible(String screenId) {
    // Verificar se a rota atual contém o ID da tela
    final currentRoute = Get.currentRoute;
    return currentRoute.contains(screenId);
  }
  
  /// Libera recursos ao encerrar o aplicativo
  @override
  void onClose() {
    // Cancelar todos os timers
    for (final timer in _refreshTimers.values) {
      timer.cancel();
    }
    _refreshTimers.clear();
    
    super.onClose();
  }
}
