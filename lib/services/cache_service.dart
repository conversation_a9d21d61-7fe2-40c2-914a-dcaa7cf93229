import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'dart:convert';

class CacheService extends GetxService {
  late Box<String> _cacheBox;

  // Duração padrão do cache em minutos
  final int _defaultCacheDuration = 5;

  Future<CacheService> init() async {
    await Hive.initFlutter();
    _cacheBox = await Hive.openBox<String>('app_cache');
    return this;
  }

  Future<void> saveData(String key, dynamic data,
      {int? cacheDurationMinutes}) async {
    final expiryTime = DateTime.now()
        .add(Duration(minutes: cacheDurationMinutes ?? _defaultCacheDuration))
        .millisecondsSinceEpoch;

    final cacheObject = {
      'data': data,
      'expiry': expiryTime,
    };

    await _cacheBox.put(key, jsonEncode(cacheObject));
  }

  T? getData<T>(String key, T Function(dynamic data) converter) {
    final cachedData = _cacheBox.get(key);

    if (cachedData == null) return null;

    final cacheObject = jsonDecode(cachedData);
    final expiry = cacheObject['expiry'] as int;

    // Verificar se o cache expirou
    if (DateTime.now().millisecondsSinceEpoch > expiry) {
      _cacheBox.delete(key);
      return null;
    }

    return converter(cacheObject['data']);
  }

  Future<void> clearCache() async {
    await _cacheBox.clear();
  }

  Future<void> removeItem(String key) async {
    await _cacheBox.delete(key);
  }
}
