import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:fila_app/services/connectivity_service.dart';
import 'package:fila_app/services/sync_manager.dart';
import 'package:fila_app/services/cache_service.dart';
import 'package:fila_app/services/throttling_service.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:fila_app/conexao.dart';

class AppServiceManager extends GetxService {
  final RxBool _servicesReady = false.obs;
  final RxDouble _initProgress = 0.0.obs;
  final RxString _currentInitTask = "Iniciando...".obs;

  bool get servicesReady => _servicesReady.value;
  double get initProgress => _initProgress.value;
  String get currentInitTask => _currentInitTask.value;

  Future<AppServiceManager> init() async {
    try {
      // Fase 1: Serviços fundamentais (20%)
      _updateInitStatus("Carregando configurações", 0.05);
      await dotenv.load(fileName: ".env");

      _updateInitStatus("Verificando conectividade", 0.1);
      final connectivityService = ConnectivityService();
      Get.put(connectivityService, permanent: true);

      _updateInitStatus("Inicializando cache local", 0.15);
      final cacheService = CacheService();
      Get.put(cacheService, permanent: true);
      await cacheService.init();

      _updateInitStatus("Configurando controlador de requisições", 0.2);
      final throttlingService = ThrottlingService();
      Get.put(throttlingService, permanent: true);

      // Fase 2: Conexão com servidor (50%)
      _updateInitStatus("Conectando ao servidor", 0.3);
      await Conexao.initialize();

      // Fase 3: Serviços de sincronização (75%)
      _updateInitStatus("Configurando sincronização", 0.6);
      final syncManager = SyncManager();
      Get.put(syncManager, permanent: true);

      // Fase 4: Finalização (100%)
      _updateInitStatus("Verificando atualizações pendentes", 0.9);
      if (connectivityService.isConnected.value) {
        await syncManager.syncPendingOperations();
      }

      _updateInitStatus("Inicialização completa", 1.0);
      _servicesReady.value = true;

      return this;
    } catch (e) {
      debugPrint('Erro na inicialização dos serviços: $e');
      _updateInitStatus("Erro: $e", 0.0);
      rethrow;
    }
  }

  void _updateInitStatus(String task, double progress) {
    _currentInitTask.value = task;
    _initProgress.value = progress;
    debugPrint(
        'Inicialização: $task - ${(progress * 100).toStringAsFixed(0)}%');
  }
}
