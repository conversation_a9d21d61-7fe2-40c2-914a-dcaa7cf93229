import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_dotenv/flutter_dotenv.dart';

class AIService {
  final String _apiKey;
  final String _baseUrl = 'https://api.openai.com/v1/chat/completions';

  // Contexto do aplicativo para treinar a IA
  final String _systemPrompt = '''
Você é um assistente virtual no aplicativo Saúde Sem Espera, que ajuda pacientes a entrar na fila de atendimento médico virtualmente.
Informações sobre o aplicativo:
- Os usuários podem entrar na fila escaneando um QR code
- O app mostra o tempo estimado de espera
- Pacientes recebem notificações sobre sua posição na fila
- Os médicos podem ver e gerenciar as filas de atendimento
- O app respeita a LGPD e mantém os dados dos usuários seguros

Suas respostas devem ser sempre breves, educadas e úteis, com no máximo 3 parágrafos.
Use formatação clara e simples. Não use caracteres especiais além dos necessários para acentuação em português.
Formate seu texto para ser facilmente legível em um aplicativo móvel.

IMPORTANTE SOBRE ENCAMINHAMENTO:
1. Tente resolver todas as dúvidas do usuário de forma autônoma.
2. Se após 2-3 interações você não conseguir resolver a dúvida ou o usuário expressar frustração/insatisfação, ofereça encaminhar para um atendente humano.
3. Se o usuário relatar problemas técnicos complexos, solicitar falar com um humano, ou tiver dúvidas médicas específicas, ofereça encaminhar para um atendente.
4. Para encaminhar, responda com a frase "ENCAMINHAR_PARA_SUPORTE" no início da sua resposta, seguida por um breve resumo do problema.
''';

  AIService({String? apiKey})
      : _apiKey = apiKey ?? dotenv.env['OPENAI_API_KEY'] ?? '';

  Future<String> getCompletion(
      String prompt, List<Map<String, String>> history) async {
    try {
      if (_apiKey.isEmpty) {
        return 'Chave API não configurada. Por favor, configure no arquivo .env';
      }

      // Formata o histórico de conversa para o formato da OpenAI
      final List<Map<String, String>> messages = [
        {"role": "system", "content": _systemPrompt},
      ];

      // Adiciona o histórico de mensagens
      for (var message in history) {
        messages.add(message);
      }

      // Adiciona a nova pergunta do usuário
      messages.add({"role": "user", "content": prompt});

      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'Content-Type': 'application/json; charset=utf-8',
          'Authorization': 'Bearer $_apiKey',
        },
        body: jsonEncode({
          'model': 'gpt-3.5-turbo',
          'messages': messages,
          'max_tokens': 300,
          'temperature': 0.7,
        }),
      );

      if (response.statusCode == 200) {
        // Usar utf8.decode para garantir que os caracteres especiais sejam interpretados corretamente
        final data = jsonDecode(utf8.decode(response.bodyBytes));
        String text = data['choices'][0]['message']['content'].trim();

        // Normalizar o texto para garantir que caracteres especiais estejam corretos
        text = _normalizeText(text);

        return text;
      } else {
        debugPrint('Erro API: ${response.body}');
        return 'Desculpe, estou com dificuldades para processar sua pergunta. Por favor, tente novamente mais tarde.';
      }
    } catch (e) {
      debugPrint('Exceção: $e');
      return 'Ocorreu um erro ao processar sua solicitação. Verifique sua conexão e tente novamente.';
    }
  }

  // Função para normalizar texto e corrigir caracteres especiais
  String _normalizeText(String text) {
    // Mapeamento de caracteres problemáticos comuns em UTF-8
    final Map<String, String> replacements = {
      'Ã£': 'ã',
      'Ã¡': 'á',
      'Ã©': 'é',
      'Ãª': 'ê',
      'Ã­': 'í',
      'Ã³': 'ó',
      'Ãº': 'ú',
      'Ã§': 'ç',
      'Ã ': 'à',
      'Ã¢': 'â',
      'Ã´': 'ô',
    };

    String normalizedText = text;
    replacements.forEach((key, value) {
      normalizedText = normalizedText.replaceAll(key, value);
    });

    return normalizedText;
  }
}
