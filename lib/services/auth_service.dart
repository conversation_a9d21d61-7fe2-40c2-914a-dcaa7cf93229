import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'package:flutter/foundation.dart' show debugPrint;

class AuthService {
  static Future<Map<String, dynamic>> checkCurrentUser() async {
    try {
      final user = await ParseUser.currentUser() as ParseUser?;

      if (user == null) {
        debugPrint('Nenhum usuário logado');
        return {'success': false, 'error': 'Nenhum usuário logado'};
      }

      // Verificar se o token ainda é válido
      final ParseResponse response = await user.getUpdatedUser();
      if (!response.success) {
        debugPrint('Sessão expirada');
        await user.logout();
        return {'success': false, 'error': 'Sessão expirada'};
      }

      debugPrint('Usuário já logado: ${user.objectId}');
      final tipo = user.get<String>('tipo');

      if (tipo == 'medico') {
        final queryMedico = QueryBuilder<ParseObject>(ParseObject('Medico'))
          ..whereEqualTo('user_medico', user.toPointer());

        final medicoResponse = await queryMedico.query();

        if (!medicoResponse.success ||
            medicoResponse.results == null ||
            medicoResponse.results!.isEmpty) {
          throw Exception('Perfil médico não encontrado');
        }

        final medico = medicoResponse.results!.first;
        final ativo = medico.get<bool>('ativo') ?? false;

        if (!ativo) {
          throw Exception('Conta desativada. Entre em contato com o suporte.');
        }

        return {
          'success': true,
          'user': user,
          'tipo': 'medico',
          'medico': medico,
          'route': '/medico'
        };
      } else if (tipo == 'consultorio') {
        final queryConsultorio =
            QueryBuilder<ParseObject>(ParseObject('consultorio'))
              ..whereEqualTo('user_consultorio', user.toPointer());

        final consultorioResponse = await queryConsultorio.query();

        if (!consultorioResponse.success ||
            consultorioResponse.results == null ||
            consultorioResponse.results!.isEmpty) {
          throw Exception('Perfil de consultório não encontrado');
        }

        final consultorio = consultorioResponse.results!.first;

        return {
          'success': true,
          'user': user,
          'tipo': 'consultorio',
          'consultorio': consultorio,
          'route': '/hospital'
        };
      } else if (tipo == 'secretaria') {
        // Melhoria 1: Usar objeto "Secretaria" diretamente, sem depender da relação invertida
        final querySecretaria =
            QueryBuilder<ParseObject>(ParseObject('Secretaria'))
              ..whereEqualTo('user_secretaria', user.toPointer());

        final secretariaResponse = await querySecretaria.query();

        if (!secretariaResponse.success ||
            secretariaResponse.results == null ||
            secretariaResponse.results!.isEmpty) {
          // Melhoria 2: Log detalhado para depuração
          debugPrint(
              'Perfil de secretária não encontrado via relação direta. Tentando método alternativo...');

          // Melhoria 3: Método alternativo - buscar pelo email
          final querySecretariaAlternativa =
              QueryBuilder<ParseObject>(ParseObject('Secretaria'))
                ..whereEqualTo('email', user.emailAddress);

          final secretariaResponseAlt =
              await querySecretariaAlternativa.query();

          if (!secretariaResponseAlt.success ||
              secretariaResponseAlt.results == null ||
              secretariaResponseAlt.results!.isEmpty) {
            throw Exception('Perfil de secretária não encontrado');
          }

          final secretaria = secretariaResponseAlt.results!.first;

          // Melhoria 4: Se encontrou pelo método alternativo, corrigir a relação
          if (secretaria.get<ParseObject>('user_secretaria') == null) {
            secretaria.set('user_secretaria', user);
            await secretaria.save();
            debugPrint('Relação user_secretaria corrigida automaticamente');
          }

          // Verificar status ativo
          final ativo = secretaria.get<bool>('ativo') ?? false;
          if (!ativo) {
            throw Exception(
                'Conta desativada. Entre em contato com o suporte.');
          }

          return {
            'success': true,
            'user': user,
            'tipo': 'secretaria',
            'secretaria': secretaria,
            'route': '/home_secretaria'
          };
        }

        final secretaria = secretariaResponse.results!.first;
        final ativo = secretaria.get<bool>('ativo') ?? false;

        if (!ativo) {
          throw Exception('Conta desativada. Entre em contato com o suporte.');
        }

        return {
          'success': true,
          'user': user,
          'tipo': 'secretaria',
          'secretaria': secretaria,
          'route': '/home_secretaria'
        };
      } else if (tipo == 'admin') {
        // Verificar se o usuário tem permissões de administrador
        final isAdmin = user.get<bool>('isAdmin') ?? false;

        if (!isAdmin) {
          throw Exception(
              'Este usuário não possui permissões de administrador');
        }

        return {
          'success': true,
          'user': user,
          'tipo': 'admin',
          'route': '/admin_hospitais'
        };
      }

      throw Exception('Tipo de usuário inválido');
    } catch (e) {
      debugPrint('Erro ao verificar usuário atual: $e');
      return {
        'success': false,
        'error': e.toString().replaceAll('Exception: ', '')
      };
    }
  }
}
