# FilaApp 🏥

![FilaApp Logo](assets/logo-removebg-preview-1-28.png)

## Gerenciamento Inteligente de Filas para Hospitais e Médicos

FilaApp é uma solução completa para modernizar e otimizar o gerenciamento de filas em ambientes médicos, melhorando a experiência tanto para pacientes quanto para equipes médicas.

[![Flutter](https://img.shields.io/badge/Flutter-3.5.4-02569B?logo=flutter)](https://flutter.dev/)
[![Dart](https://img.shields.io/badge/Dart-3.5.4-0175C2?logo=dart)](https://dart.dev/)
[![Parse Server](https://img.shields.io/badge/Parse_Server-SDK-1199EE)](https://parseplatform.org/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](https://opensource.org/licenses/MIT)

## 🚀 Características

### Para Pacientes
- **Entrada Virtual na Fila**: Escaneie o QR code do médico para entrar na fila sem precisar esperar fisicamente na recepção
- **Status em Tempo Real**: Acompanhe sua posição na fila e tempo estimado de espera
- **Notificações**: Receba atualizações sobre mudanças na fila ou mensagens da equipe médica
- **Navegação Integrada**: Opção para obter direções até o hospital usando Google Maps ou Waze
- **Feedbacks**: Compartilhe sua experiência ao sair da fila

### Para Médicos
- **Gestão de Perfil**: Atualize facilmente informações profissionais
- **Vínculo com Hospitais**: Selecione em quais hospitais você realiza atendimentos
- **QR Codes Personalizados**: Gere QR codes únicos para cada hospital vinculado

### Para Hospitais/Consultórios
- **Painel Administrativo**: Gerencie filas, médicos e secretárias
- **Modificação de Filas**: Reordene pacientes na fila conforme necessário
- **Comunicação Direta**: Envie mensagens para pacientes na fila
- **Estatísticas**: Acompanhe métricas de atendimento, tempo médio de espera e mais

### Para Secretárias
- **Gestão de Solicitações**: Aprove ou rejeite solicitações de entrada na fila
- **Interface Intuitiva**: Veja e gerencie filas de todos os médicos do consultório
- **Comunicação**: Contate pacientes via WhatsApp diretamente do app

### Para Administradores
- **Controle Total**: Gerencie todos os hospitais, ative ou desative unidades
- **Cadastro de Hospitais**: Adicione novos hospitais com localização via mapa integrado

## 📱 Telas do Aplicativo

### Tela Inicial
![Tela Inicial](screenshots/tela_inicial.png)

### Interface do Paciente
![Interface do Paciente](screenshots/interface_paciente.png)

### Gerenciamento de Filas
![Gerenciamento de Filas](screenshots/gerenciamento_filas.png)

### Perfil Médico
![Perfil Médico](screenshots/perfil_medico.png)

## 🔧 Tecnologias

- **Frontend**: Flutter/Dart
- **Backend**: Parse Server (Back4App)
- **Gerenciamento de Estado**: GetX
- **Mapas e Localização**: Google Maps, Geolocator
- **QR Code**: mobile_scanner (leitura), qr_flutter (geração)
- **Segurança**: flutter_secure_storage
- **Formatação de Documentos**: PDF/Printing

## 📊 Arquitetura

FilaApp segue a arquitetura MVC com GetX para gerenciamento de estado:

- **Models**: Representações dos dados
- **Views**: Interfaces de usuário
- **Controllers**: Lógica de negócios
- **Bindings**: Injeção de dependências
- **Services**: Serviços gerais da aplicação

## 🛠️ Instalação e Configuração

1. Clone o repositório:
   ```bash
   git clone https://github.com/seu-usuario/fila-app.git
   cd fila-app
   ```

2. Instale as dependências:
   ```bash
   flutter pub get
   ```

3. Configure o arquivo `.env` na raiz do projeto:
   ```
   PARSE_APPLICATION_ID=seu_application_id
   PARSE_CLIENT_KEY=seu_client_key
   PARSE_SERVER_URL=sua_url_do_parse_server
   MAPS_API_KEY=sua_chave_api_google_maps
   GOOGLE_MAPS_API_KEY=sua_chave_api_google_maps
   ```

4. Execute o aplicativo:
   ```bash
   flutter run
   ```

## 🔐 Autenticação e Níveis de Acesso

O sistema possui cinco tipos de usuários, cada um com permissões específicas:

1. **Paciente**: Acesso básico para entrar e acompanhar filas
2. **Médico**: Gerencia seu perfil e visualiza hospitais vinculados
3. **Hospital/Consultório**: Administra filas e médicos vinculados
4. **Secretária**: Gerencia filas e solicitações de pacientes
5. **Administrador**: Controle completo sobre o sistema

## 📱 Recursos de UX/UI

- **Design Responsivo**: Adaptável a diferentes tamanhos de tela
- **Tema Consistente**: Identidade visual unificada
- **Feedback Visual**: Animações e indicadores claros de status
- **Acessibilidade**: Contraste adequado e tamanhos de fonte ajustáveis
- **Modo Offline**: Funcionamento básico mesmo sem conexão de internet

## 🌐 Integração com Serviços

- **Google Maps**: Navegação até os hospitais
- **Waze**: Alternativa para navegação
- **WhatsApp**: Comunicação direta com pacientes
- **Back4App**: Backend como serviço para armazenamento de dados

## 📋 Próximos Passos e Melhorias Futuras

- [ ] Implementação de notificações push via Firebase Cloud Messaging
- [ ] Dashboard analítico com estatísticas avançadas
- [ ] Integração com sistemas de prontuário eletrônico
- [ ] Suporte a múltiplos idiomas
- [ ] Pagamento de consultas pelo aplicativo
- [ ] Implementação de chatbot para dúvidas frequentes

## 👨‍💻 Contribuindo

Contribuições são bem-vindas! Siga estes passos:

1. Faça um fork do projeto
2. Crie uma branch para sua feature (`git checkout -b feature/nova-funcionalidade`)
3. Faça commit das alterações (`git commit -m 'Adiciona nova funcionalidade'`)
4. Faça push para a branch (`git push origin feature/nova-funcionalidade`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está licenciado sob a [Licença MIT](LICENSE).

## 📞 Contato

Para qualquer dúvida, sugestão ou interesse em parcerias, entre em contato:

- Email: <EMAIL>
- Website: www.saudesemespera.com
- Instagram: @saudesemesperaapp

---

Desenvolvido com ❤️ para melhorar a experiência de atendimento médico para todos.
