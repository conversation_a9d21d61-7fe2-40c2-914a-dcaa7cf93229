<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Fila App</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>fila_app</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>NSAppleMusicUsageDescription</key>
	<string>This app does not need access to Apple Music.</string>
	<key>NSCalendarsUsageDescription</key>
	<string>This app does not need access to your calendar.</string>
	<key>NSCameraUsageDescription</key>
	<string>Este app precisa de acesso à câmera para ler QR codes.</string>
	<key>NSContactsUsageDescription</key>
	<string>This app does not need access to your contacts.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Este app precisa de acesso à sua localização para verificar se você está próximo da clínica.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Este app precisa de acesso à sua localização para verificar se você está próximo da clínica.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Este app precisa de acesso à sua localização para verificar se você está próximo da clínica.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Este app não precisa de acesso ao seu microfone.</string>
	<key>NSMotionUsageDescription</key>
	<string>Este app não precisa de acesso aos seus dados de movimento.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Este app não precisa de acesso à sua galeria de fotos.</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>Este app não precisa de reconhecimento de fala.</string>
</dict>
</plist>
